.class public final LFz0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008I\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0081\u0008\u0018\u00002\u00020\u0001B\u00a5\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u0002\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\u0006\u0010\n\u001a\u00020\u0002\u0012\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u0002\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000e\u001a\u00020\u0004\u0012\u0006\u0010\u000f\u001a\u00020\u0007\u0012\u0006\u0010\u0010\u001a\u00020\u0007\u0012\u0006\u0010\u0011\u001a\u00020\u0007\u0012\u0006\u0010\u0012\u001a\u00020\u0007\u0012\u0006\u0010\u0013\u001a\u00020\u0007\u0012\u0006\u0010\u0014\u001a\u00020\u0007\u0012\u0006\u0010\u0015\u001a\u00020\u0007\u0012\u0006\u0010\u0016\u001a\u00020\u0007\u0012\u0006\u0010\u0017\u001a\u00020\u0007\u0012\u0006\u0010\u0018\u001a\u00020\u0007\u0012\u0006\u0010\u0019\u001a\u00020\u0004\u0012\u0006\u0010\u001a\u001a\u00020\u0007\u0012\u0006\u0010\u001b\u001a\u00020\u0007\u0012\u0006\u0010\u001c\u001a\u00020\u0007\u0012\u0006\u0010\u001d\u001a\u00020\u0007\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0008\u0010!\u001a\u0004\u0018\u00010 \u0012\u0008\u0010#\u001a\u0004\u0018\u00010\"\u0012\u0008\u0010%\u001a\u0004\u0018\u00010$\u0012\u0008\u0010\'\u001a\u0004\u0018\u00010&\u0012\u0008\u0010)\u001a\u0004\u0018\u00010(\u0012\u0008\u0010+\u001a\u0004\u0018\u00010*\u0012\u0006\u0010-\u001a\u00020,\u0012\u000c\u00100\u001a\u0008\u0012\u0004\u0012\u00020/0.\u00a2\u0006\u0004\u00081\u00102J\u00f0\u0002\u00103\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00072\u0008\u0008\u0002\u0010\t\u001a\u00020\u00022\u0008\u0008\u0002\u0010\n\u001a\u00020\u00022\n\u0008\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00022\u0008\u0008\u0002\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0002\u0010\u000e\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0010\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0011\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0012\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0013\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0014\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0015\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0016\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0018\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0019\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u001b\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u001c\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u001d\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u001f\u001a\u00020\u001e2\n\u0008\u0002\u0010!\u001a\u0004\u0018\u00010 2\n\u0008\u0002\u0010#\u001a\u0004\u0018\u00010\"2\n\u0008\u0002\u0010%\u001a\u0004\u0018\u00010$2\n\u0008\u0002\u0010\'\u001a\u0004\u0018\u00010&2\n\u0008\u0002\u0010)\u001a\u0004\u0018\u00010(2\n\u0008\u0002\u0010+\u001a\u0004\u0018\u00010*2\u0008\u0008\u0002\u0010-\u001a\u00020,2\u000e\u0008\u0002\u00100\u001a\u0008\u0012\u0004\u0012\u00020/0.H\u00c6\u0001\u00a2\u0006\u0004\u00083\u00104J\u0010\u00105\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u00085\u00106J\u0010\u00108\u001a\u000207H\u00d6\u0001\u00a2\u0006\u0004\u00088\u00109J\u001a\u0010;\u001a\u00020\u00072\u0008\u0010:\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008;\u0010<R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u00083\u0010=\u001a\u0004\u0008>\u0010?R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008@\u0010A\u001a\u0004\u0008B\u00106R\u0019\u0010\u0006\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010FR\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008G\u0010H\u001a\u0004\u0008I\u0010JR\u0017\u0010\t\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008K\u0010=\u001a\u0004\u0008L\u0010?R\u0017\u0010\n\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008M\u0010=\u001a\u0004\u0008N\u0010?R\u0019\u0010\u000b\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008O\u0010D\u001a\u0004\u0008M\u0010FR\u0017\u0010\r\u001a\u00020\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u0008P\u0010Q\u001a\u0004\u0008R\u0010SR\u0017\u0010\u000e\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008T\u0010A\u001a\u0004\u0008O\u00106R\u0017\u0010\u000f\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008U\u0010H\u001a\u0004\u0008V\u0010JR\u0017\u0010\u0010\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008R\u0010H\u001a\u0004\u0008W\u0010JR\u0017\u0010\u0011\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008>\u0010H\u001a\u0004\u0008X\u0010JR\u0017\u0010\u0012\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008Y\u0010H\u001a\u0004\u0008Z\u0010JR\u0017\u0010\u0013\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008I\u0010H\u001a\u0004\u0008[\u0010JR\u0017\u0010\u0014\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\\\u0010H\u001a\u0004\u0008]\u0010JR\u0017\u0010\u0015\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008W\u0010H\u001a\u0004\u0008^\u0010JR\u0017\u0010\u0016\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008_\u0010H\u001a\u0004\u0008`\u0010JR\u0017\u0010\u0017\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008a\u0010H\u001a\u0004\u0008G\u0010JR\u0017\u0010\u0018\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008b\u0010H\u001a\u0004\u0008c\u0010JR\u0017\u0010\u0019\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008L\u0010A\u001a\u0004\u0008b\u00106R\u0017\u0010\u001a\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008N\u0010H\u001a\u0004\u0008\u001a\u0010JR\u0017\u0010\u001b\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008d\u0010H\u001a\u0004\u0008a\u0010JR\u0017\u0010\u001c\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008e\u0010H\u001a\u0004\u0008e\u0010JR\u0017\u0010\u001d\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008f\u0010H\u001a\u0004\u0008f\u0010JR\u0017\u0010\u001f\u001a\u00020\u001e8\u0006\u00a2\u0006\u000c\n\u0004\u0008g\u0010=\u001a\u0004\u0008g\u0010?R\u0019\u0010!\u001a\u0004\u0018\u00010 8\u0006\u00a2\u0006\u000c\n\u0004\u0008c\u0010h\u001a\u0004\u0008U\u0010iR\u0019\u0010#\u001a\u0004\u0018\u00010\"8\u0006\u00a2\u0006\u000c\n\u0004\u0008j\u0010k\u001a\u0004\u0008P\u0010lR\u0019\u0010%\u001a\u0004\u0018\u00010$8\u0006\u00a2\u0006\u000c\n\u0004\u0008m\u0010n\u001a\u0004\u0008K\u0010oR\u0019\u0010\'\u001a\u0004\u0018\u00010&8\u0006\u00a2\u0006\u000c\n\u0004\u0008p\u0010q\u001a\u0004\u0008d\u0010rR\u0019\u0010)\u001a\u0004\u0018\u00010(8\u0006\u00a2\u0006\u000c\n\u0004\u0008s\u0010t\u001a\u0004\u0008\\\u0010uR\u0019\u0010+\u001a\u0004\u0018\u00010*8\u0006\u00a2\u0006\u000c\n\u0004\u0008v\u0010w\u001a\u0004\u0008x\u0010yR\u0017\u0010-\u001a\u00020,8\u0006\u00a2\u0006\u000c\n\u0004\u0008z\u0010{\u001a\u0004\u0008|\u0010}R\u001e\u00100\u001a\u0008\u0012\u0004\u0012\u00020/0.8\u0006\u00a2\u0006\r\n\u0004\u0008~\u0010\u007f\u001a\u0005\u0008T\u0010\u0080\u0001R\u0014\u0010\u0083\u0001\u001a\u00030\u0081\u00018F\u00a2\u0006\u0007\u001a\u0005\u0008C\u0010\u0082\u0001R\u0014\u0010\u0086\u0001\u001a\u00030\u0084\u00018F\u00a2\u0006\u0007\u001a\u0005\u0008Y\u0010\u0085\u0001R\u0014\u0010\u0089\u0001\u001a\u00030\u0087\u00018F\u00a2\u0006\u0007\u001a\u0005\u0008_\u0010\u0088\u0001\u00a8\u0006\u008a\u0001"
    }
    d2 = {
        "LFz0/a;",
        "",
        "",
        "gameId",
        "",
        "statGameId",
        "constId",
        "",
        "live",
        "sportId",
        "subGameId",
        "champId",
        "Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;",
        "gameConditionType",
        "componentKey",
        "quickBetEnabled",
        "menuButtonEnabled",
        "quickBetAvailable",
        "marketsAvailable",
        "statisticAvailable",
        "filterAvailable",
        "duelAvailable",
        "subscriptionAvailable",
        "broadcastingRun",
        "isNightModeEnable",
        "specialEventBackgroundUrl",
        "isLoading",
        "showSubGames",
        "transferContinue",
        "transferFailed",
        "Ll8/b$a$c;",
        "transferTimeLeft",
        "LgB0/a;",
        "gameBroadcastModel",
        "Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;",
        "currentGameInfoCardType",
        "LQA0/b;",
        "cardSectionModel",
        "LRA0/h;",
        "timerModel",
        "LWA0/c;",
        "matchCacheScoreModel",
        "LWA0/a;",
        "cachePenaltyModel",
        "Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;",
        "cardExpandType",
        "",
        "LTT/f;",
        "favoriteTeamModelList",
        "<init>",
        "(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V",
        "a",
        "(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;)LFz0/a;",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "J",
        "l",
        "()J",
        "b",
        "Ljava/lang/String;",
        "getStatGameId",
        "c",
        "Ljava/lang/Long;",
        "getConstId",
        "()Ljava/lang/Long;",
        "d",
        "Z",
        "n",
        "()Z",
        "e",
        "t",
        "f",
        "u",
        "g",
        "h",
        "Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;",
        "k",
        "()Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;",
        "i",
        "j",
        "getQuickBetEnabled",
        "p",
        "getQuickBetAvailable",
        "m",
        "getMarketsAvailable",
        "getStatisticAvailable",
        "o",
        "getFilterAvailable",
        "getDuelAvailable",
        "q",
        "getSubscriptionAvailable",
        "r",
        "s",
        "z",
        "v",
        "w",
        "x",
        "y",
        "LgB0/a;",
        "()LgB0/a;",
        "A",
        "Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;",
        "()Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;",
        "B",
        "LQA0/b;",
        "()LQA0/b;",
        "C",
        "LRA0/h;",
        "()LRA0/h;",
        "D",
        "LWA0/c;",
        "()LWA0/c;",
        "E",
        "LWA0/a;",
        "getCachePenaltyModel",
        "()LWA0/a;",
        "F",
        "Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;",
        "getCardExpandType",
        "()Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;",
        "G",
        "Ljava/util/List;",
        "()Ljava/util/List;",
        "Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;",
        "()Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;",
        "actionMenuDialogParams",
        "Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams;",
        "()Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams;",
        "launchGameScenarioParams",
        "Lorg/xbet/related/api/presentation/RelatedParams;",
        "()Lorg/xbet/related/api/presentation/RelatedParams;",
        "relatedParams",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

.field public final B:LQA0/b;

.field public final C:LRA0/h;

.field public final D:LWA0/c;

.field public final E:LWA0/a;

.field public final F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LTT/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a:J

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/lang/Long;

.field public final d:Z

.field public final e:J

.field public final f:J

.field public final g:Ljava/lang/Long;

.field public final h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Z

.field public final k:Z

.field public final l:Z

.field public final m:Z

.field public final n:Z

.field public final o:Z

.field public final p:Z

.field public final q:Z

.field public final r:Z

.field public final s:Z

.field public final t:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Z

.field public final v:Z

.field public final w:Z

.field public final x:Z

.field public final y:J

.field public final z:LgB0/a;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/Long;",
            "ZJJ",
            "Ljava/lang/Long;",
            "Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;",
            "Ljava/lang/String;",
            "ZZZZZZZZZZ",
            "Ljava/lang/String;",
            "ZZZZJ",
            "LgB0/a;",
            "Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;",
            "LQA0/b;",
            "LRA0/h;",
            "LWA0/c;",
            "LWA0/a;",
            "Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;",
            "Ljava/util/List<",
            "LTT/f;",
            ">;)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-wide p1, p0, LFz0/a;->a:J

    .line 4
    iput-object p3, p0, LFz0/a;->b:Ljava/lang/String;

    .line 5
    iput-object p4, p0, LFz0/a;->c:Ljava/lang/Long;

    .line 6
    iput-boolean p5, p0, LFz0/a;->d:Z

    .line 7
    iput-wide p6, p0, LFz0/a;->e:J

    .line 8
    iput-wide p8, p0, LFz0/a;->f:J

    .line 9
    iput-object p10, p0, LFz0/a;->g:Ljava/lang/Long;

    .line 10
    iput-object p11, p0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 11
    iput-object p12, p0, LFz0/a;->i:Ljava/lang/String;

    .line 12
    iput-boolean p13, p0, LFz0/a;->j:Z

    .line 13
    iput-boolean p14, p0, LFz0/a;->k:Z

    .line 14
    iput-boolean p15, p0, LFz0/a;->l:Z

    move/from16 p1, p16

    .line 15
    iput-boolean p1, p0, LFz0/a;->m:Z

    move/from16 p1, p17

    .line 16
    iput-boolean p1, p0, LFz0/a;->n:Z

    move/from16 p1, p18

    .line 17
    iput-boolean p1, p0, LFz0/a;->o:Z

    move/from16 p1, p19

    .line 18
    iput-boolean p1, p0, LFz0/a;->p:Z

    move/from16 p1, p20

    .line 19
    iput-boolean p1, p0, LFz0/a;->q:Z

    move/from16 p1, p21

    .line 20
    iput-boolean p1, p0, LFz0/a;->r:Z

    move/from16 p1, p22

    .line 21
    iput-boolean p1, p0, LFz0/a;->s:Z

    move-object/from16 p1, p23

    .line 22
    iput-object p1, p0, LFz0/a;->t:Ljava/lang/String;

    move/from16 p1, p24

    .line 23
    iput-boolean p1, p0, LFz0/a;->u:Z

    move/from16 p1, p25

    .line 24
    iput-boolean p1, p0, LFz0/a;->v:Z

    move/from16 p1, p26

    .line 25
    iput-boolean p1, p0, LFz0/a;->w:Z

    move/from16 p1, p27

    .line 26
    iput-boolean p1, p0, LFz0/a;->x:Z

    move-wide/from16 p1, p28

    .line 27
    iput-wide p1, p0, LFz0/a;->y:J

    move-object/from16 p1, p30

    .line 28
    iput-object p1, p0, LFz0/a;->z:LgB0/a;

    move-object/from16 p1, p31

    .line 29
    iput-object p1, p0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    move-object/from16 p1, p32

    .line 30
    iput-object p1, p0, LFz0/a;->B:LQA0/b;

    move-object/from16 p1, p33

    .line 31
    iput-object p1, p0, LFz0/a;->C:LRA0/h;

    move-object/from16 p1, p34

    .line 32
    iput-object p1, p0, LFz0/a;->D:LWA0/c;

    move-object/from16 p1, p35

    .line 33
    iput-object p1, p0, LFz0/a;->E:LWA0/a;

    move-object/from16 p1, p36

    .line 34
    iput-object p1, p0, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    move-object/from16 p1, p37

    .line 35
    iput-object p1, p0, LFz0/a;->G:Ljava/util/List;

    return-void
.end method

.method public synthetic constructor <init>(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p37}, LFz0/a;-><init>(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;)V

    return-void
.end method

.method public static synthetic b(LFz0/a;JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;IILjava/lang/Object;)LFz0/a;
    .locals 19

    move-object/from16 v0, p0

    move/from16 v1, p38

    and-int/lit8 v2, v1, 0x1

    if-eqz v2, :cond_0

    .line 1
    iget-wide v2, v0, LFz0/a;->a:J

    goto :goto_0

    :cond_0
    move-wide/from16 v2, p1

    :goto_0
    and-int/lit8 v4, v1, 0x2

    if-eqz v4, :cond_1

    iget-object v4, v0, LFz0/a;->b:Ljava/lang/String;

    goto :goto_1

    :cond_1
    move-object/from16 v4, p3

    :goto_1
    and-int/lit8 v5, v1, 0x4

    if-eqz v5, :cond_2

    iget-object v5, v0, LFz0/a;->c:Ljava/lang/Long;

    goto :goto_2

    :cond_2
    move-object/from16 v5, p4

    :goto_2
    and-int/lit8 v6, v1, 0x8

    if-eqz v6, :cond_3

    iget-boolean v6, v0, LFz0/a;->d:Z

    goto :goto_3

    :cond_3
    move/from16 v6, p5

    :goto_3
    and-int/lit8 v7, v1, 0x10

    if-eqz v7, :cond_4

    iget-wide v7, v0, LFz0/a;->e:J

    goto :goto_4

    :cond_4
    move-wide/from16 v7, p6

    :goto_4
    and-int/lit8 v9, v1, 0x20

    if-eqz v9, :cond_5

    iget-wide v9, v0, LFz0/a;->f:J

    goto :goto_5

    :cond_5
    move-wide/from16 v9, p8

    :goto_5
    and-int/lit8 v11, v1, 0x40

    if-eqz v11, :cond_6

    iget-object v11, v0, LFz0/a;->g:Ljava/lang/Long;

    goto :goto_6

    :cond_6
    move-object/from16 v11, p10

    :goto_6
    and-int/lit16 v12, v1, 0x80

    if-eqz v12, :cond_7

    iget-object v12, v0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    goto :goto_7

    :cond_7
    move-object/from16 v12, p11

    :goto_7
    and-int/lit16 v13, v1, 0x100

    if-eqz v13, :cond_8

    iget-object v13, v0, LFz0/a;->i:Ljava/lang/String;

    goto :goto_8

    :cond_8
    move-object/from16 v13, p12

    :goto_8
    and-int/lit16 v14, v1, 0x200

    if-eqz v14, :cond_9

    iget-boolean v14, v0, LFz0/a;->j:Z

    goto :goto_9

    :cond_9
    move/from16 v14, p13

    :goto_9
    and-int/lit16 v15, v1, 0x400

    if-eqz v15, :cond_a

    iget-boolean v15, v0, LFz0/a;->k:Z

    goto :goto_a

    :cond_a
    move/from16 v15, p14

    :goto_a
    move-wide/from16 v16, v2

    and-int/lit16 v2, v1, 0x800

    if-eqz v2, :cond_b

    iget-boolean v2, v0, LFz0/a;->l:Z

    goto :goto_b

    :cond_b
    move/from16 v2, p15

    :goto_b
    and-int/lit16 v3, v1, 0x1000

    if-eqz v3, :cond_c

    iget-boolean v3, v0, LFz0/a;->m:Z

    goto :goto_c

    :cond_c
    move/from16 v3, p16

    :goto_c
    move/from16 p1, v2

    and-int/lit16 v2, v1, 0x2000

    if-eqz v2, :cond_d

    iget-boolean v2, v0, LFz0/a;->n:Z

    goto :goto_d

    :cond_d
    move/from16 v2, p17

    :goto_d
    move/from16 p2, v2

    and-int/lit16 v2, v1, 0x4000

    if-eqz v2, :cond_e

    iget-boolean v2, v0, LFz0/a;->o:Z

    goto :goto_e

    :cond_e
    move/from16 v2, p18

    :goto_e
    const v18, 0x8000

    and-int v18, v1, v18

    if-eqz v18, :cond_f

    iget-boolean v1, v0, LFz0/a;->p:Z

    goto :goto_f

    :cond_f
    move/from16 v1, p19

    :goto_f
    const/high16 v18, 0x10000

    and-int v18, p38, v18

    move/from16 p3, v1

    if-eqz v18, :cond_10

    iget-boolean v1, v0, LFz0/a;->q:Z

    goto :goto_10

    :cond_10
    move/from16 v1, p20

    :goto_10
    const/high16 v18, 0x20000

    and-int v18, p38, v18

    move/from16 p4, v1

    if-eqz v18, :cond_11

    iget-boolean v1, v0, LFz0/a;->r:Z

    goto :goto_11

    :cond_11
    move/from16 v1, p21

    :goto_11
    const/high16 v18, 0x40000

    and-int v18, p38, v18

    move/from16 p5, v1

    if-eqz v18, :cond_12

    iget-boolean v1, v0, LFz0/a;->s:Z

    goto :goto_12

    :cond_12
    move/from16 v1, p22

    :goto_12
    const/high16 v18, 0x80000

    and-int v18, p38, v18

    move/from16 p6, v1

    if-eqz v18, :cond_13

    iget-object v1, v0, LFz0/a;->t:Ljava/lang/String;

    goto :goto_13

    :cond_13
    move-object/from16 v1, p23

    :goto_13
    const/high16 v18, 0x100000

    and-int v18, p38, v18

    move-object/from16 p7, v1

    if-eqz v18, :cond_14

    iget-boolean v1, v0, LFz0/a;->u:Z

    goto :goto_14

    :cond_14
    move/from16 v1, p24

    :goto_14
    const/high16 v18, 0x200000

    and-int v18, p38, v18

    move/from16 p8, v1

    if-eqz v18, :cond_15

    iget-boolean v1, v0, LFz0/a;->v:Z

    goto :goto_15

    :cond_15
    move/from16 v1, p25

    :goto_15
    const/high16 v18, 0x400000

    and-int v18, p38, v18

    move/from16 p9, v1

    if-eqz v18, :cond_16

    iget-boolean v1, v0, LFz0/a;->w:Z

    goto :goto_16

    :cond_16
    move/from16 v1, p26

    :goto_16
    const/high16 v18, 0x800000

    and-int v18, p38, v18

    move/from16 p10, v1

    if-eqz v18, :cond_17

    iget-boolean v1, v0, LFz0/a;->x:Z

    goto :goto_17

    :cond_17
    move/from16 v1, p27

    :goto_17
    const/high16 v18, 0x1000000

    and-int v18, p38, v18

    move/from16 p12, v1

    move/from16 p11, v2

    if-eqz v18, :cond_18

    iget-wide v1, v0, LFz0/a;->y:J

    goto :goto_18

    :cond_18
    move-wide/from16 v1, p28

    :goto_18
    const/high16 v18, 0x2000000

    and-int v18, p38, v18

    move-wide/from16 p13, v1

    if-eqz v18, :cond_19

    iget-object v1, v0, LFz0/a;->z:LgB0/a;

    goto :goto_19

    :cond_19
    move-object/from16 v1, p30

    :goto_19
    const/high16 v2, 0x4000000

    and-int v2, p38, v2

    if-eqz v2, :cond_1a

    iget-object v2, v0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    goto :goto_1a

    :cond_1a
    move-object/from16 v2, p31

    :goto_1a
    const/high16 v18, 0x8000000

    and-int v18, p38, v18

    move-object/from16 p15, v1

    if-eqz v18, :cond_1b

    iget-object v1, v0, LFz0/a;->B:LQA0/b;

    goto :goto_1b

    :cond_1b
    move-object/from16 v1, p32

    :goto_1b
    const/high16 v18, 0x10000000

    and-int v18, p38, v18

    move-object/from16 p16, v1

    if-eqz v18, :cond_1c

    iget-object v1, v0, LFz0/a;->C:LRA0/h;

    goto :goto_1c

    :cond_1c
    move-object/from16 v1, p33

    :goto_1c
    const/high16 v18, 0x20000000

    and-int v18, p38, v18

    move-object/from16 p17, v1

    if-eqz v18, :cond_1d

    iget-object v1, v0, LFz0/a;->D:LWA0/c;

    goto :goto_1d

    :cond_1d
    move-object/from16 v1, p34

    :goto_1d
    const/high16 v18, 0x40000000    # 2.0f

    and-int v18, p38, v18

    move-object/from16 p18, v1

    if-eqz v18, :cond_1e

    iget-object v1, v0, LFz0/a;->E:LWA0/a;

    goto :goto_1e

    :cond_1e
    move-object/from16 v1, p35

    :goto_1e
    const/high16 v18, -0x80000000

    and-int v18, p38, v18

    move-object/from16 p19, v1

    if-eqz v18, :cond_1f

    iget-object v1, v0, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    goto :goto_1f

    :cond_1f
    move-object/from16 v1, p36

    :goto_1f
    and-int/lit8 v18, p39, 0x1

    if-eqz v18, :cond_20

    move-object/from16 p20, v1

    iget-object v1, v0, LFz0/a;->G:Ljava/util/List;

    move-object/from16 p37, p20

    move-object/from16 p38, v1

    move/from16 p21, p4

    move/from16 p22, p5

    move/from16 p23, p6

    move-object/from16 p24, p7

    move/from16 p25, p8

    move/from16 p26, p9

    move/from16 p27, p10

    move/from16 p28, p12

    move-wide/from16 p29, p13

    move-object/from16 p31, p15

    move-object/from16 p33, p16

    move-object/from16 p34, p17

    move-object/from16 p35, p18

    move-object/from16 p36, p19

    move-object/from16 p32, v2

    move/from16 p17, v3

    move-object/from16 p4, v4

    move-object/from16 p5, v5

    move/from16 p6, v6

    move-wide/from16 p7, v7

    move-wide/from16 p9, v9

    move-object/from16 p12, v12

    move-object/from16 p13, v13

    move/from16 p14, v14

    move/from16 p15, v15

    move/from16 p16, p1

    move/from16 p18, p2

    move/from16 p20, p3

    :goto_20
    move/from16 p19, p11

    move-object/from16 p1, v0

    move-object/from16 p11, v11

    move-wide/from16 p2, v16

    goto :goto_21

    :cond_20
    move-object/from16 p38, p37

    move-object/from16 p37, v1

    move/from16 p20, p3

    move/from16 p21, p4

    move/from16 p22, p5

    move/from16 p23, p6

    move-object/from16 p24, p7

    move/from16 p25, p8

    move/from16 p26, p9

    move/from16 p27, p10

    move/from16 p28, p12

    move-wide/from16 p29, p13

    move-object/from16 p31, p15

    move-object/from16 p33, p16

    move-object/from16 p34, p17

    move-object/from16 p35, p18

    move-object/from16 p36, p19

    move-object/from16 p32, v2

    move/from16 p17, v3

    move-object/from16 p4, v4

    move-object/from16 p5, v5

    move/from16 p6, v6

    move-wide/from16 p7, v7

    move-wide/from16 p9, v9

    move-object/from16 p12, v12

    move-object/from16 p13, v13

    move/from16 p14, v14

    move/from16 p15, v15

    move/from16 p16, p1

    move/from16 p18, p2

    goto :goto_20

    :goto_21
    invoke-virtual/range {p1 .. p38}, LFz0/a;->a(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;)LFz0/a;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public final a(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;)LFz0/a;
    .locals 39
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p36    # Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p37    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/Long;",
            "ZJJ",
            "Ljava/lang/Long;",
            "Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;",
            "Ljava/lang/String;",
            "ZZZZZZZZZZ",
            "Ljava/lang/String;",
            "ZZZZJ",
            "LgB0/a;",
            "Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;",
            "LQA0/b;",
            "LRA0/h;",
            "LWA0/c;",
            "LWA0/a;",
            "Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;",
            "Ljava/util/List<",
            "LTT/f;",
            ">;)",
            "LFz0/a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LFz0/a;

    .line 2
    .line 3
    const/16 v38, 0x0

    .line 4
    .line 5
    move-wide/from16 v1, p1

    .line 6
    .line 7
    move-object/from16 v3, p3

    .line 8
    .line 9
    move-object/from16 v4, p4

    .line 10
    .line 11
    move/from16 v5, p5

    .line 12
    .line 13
    move-wide/from16 v6, p6

    .line 14
    .line 15
    move-wide/from16 v8, p8

    .line 16
    .line 17
    move-object/from16 v10, p10

    .line 18
    .line 19
    move-object/from16 v11, p11

    .line 20
    .line 21
    move-object/from16 v12, p12

    .line 22
    .line 23
    move/from16 v13, p13

    .line 24
    .line 25
    move/from16 v14, p14

    .line 26
    .line 27
    move/from16 v15, p15

    .line 28
    .line 29
    move/from16 v16, p16

    .line 30
    .line 31
    move/from16 v17, p17

    .line 32
    .line 33
    move/from16 v18, p18

    .line 34
    .line 35
    move/from16 v19, p19

    .line 36
    .line 37
    move/from16 v20, p20

    .line 38
    .line 39
    move/from16 v21, p21

    .line 40
    .line 41
    move/from16 v22, p22

    .line 42
    .line 43
    move-object/from16 v23, p23

    .line 44
    .line 45
    move/from16 v24, p24

    .line 46
    .line 47
    move/from16 v25, p25

    .line 48
    .line 49
    move/from16 v26, p26

    .line 50
    .line 51
    move/from16 v27, p27

    .line 52
    .line 53
    move-wide/from16 v28, p28

    .line 54
    .line 55
    move-object/from16 v30, p30

    .line 56
    .line 57
    move-object/from16 v31, p31

    .line 58
    .line 59
    move-object/from16 v32, p32

    .line 60
    .line 61
    move-object/from16 v33, p33

    .line 62
    .line 63
    move-object/from16 v34, p34

    .line 64
    .line 65
    move-object/from16 v35, p35

    .line 66
    .line 67
    move-object/from16 v36, p36

    .line 68
    .line 69
    move-object/from16 v37, p37

    .line 70
    .line 71
    invoke-direct/range {v0 .. v38}, LFz0/a;-><init>(JLjava/lang/String;Ljava/lang/Long;ZJJLjava/lang/Long;Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZZZJLgB0/a;Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;LQA0/b;LRA0/h;LWA0/c;LWA0/a;Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 72
    .line 73
    .line 74
    return-object v0
.end method

.method public final c()Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;
    .locals 18
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-wide v2, v0, LFz0/a;->a:J

    .line 4
    .line 5
    iget-object v6, v0, LFz0/a;->b:Ljava/lang/String;

    .line 6
    .line 7
    iget-wide v4, v0, LFz0/a;->f:J

    .line 8
    .line 9
    iget-object v1, v0, LFz0/a;->c:Ljava/lang/Long;

    .line 10
    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 14
    .line 15
    .line 16
    move-result-wide v7

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const-wide/16 v7, -0x1

    .line 19
    .line 20
    :goto_0
    iget-boolean v9, v0, LFz0/a;->d:Z

    .line 21
    .line 22
    iget-wide v10, v0, LFz0/a;->e:J

    .line 23
    .line 24
    iget-boolean v12, v0, LFz0/a;->n:Z

    .line 25
    .line 26
    iget-boolean v13, v0, LFz0/a;->m:Z

    .line 27
    .line 28
    iget-boolean v14, v0, LFz0/a;->o:Z

    .line 29
    .line 30
    iget-boolean v15, v0, LFz0/a;->p:Z

    .line 31
    .line 32
    iget-boolean v1, v0, LFz0/a;->q:Z

    .line 33
    .line 34
    move/from16 v16, v1

    .line 35
    .line 36
    iget-object v1, v0, LFz0/a;->i:Ljava/lang/String;

    .line 37
    .line 38
    move-object/from16 v17, v1

    .line 39
    .line 40
    new-instance v1, Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;

    .line 41
    .line 42
    invoke-direct/range {v1 .. v17}, Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;-><init>(JJLjava/lang/String;JZJZZZZZLjava/lang/String;)V

    .line 43
    .line 44
    .line 45
    return-object v1
.end method

.method public final d()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->r:Z

    .line 2
    .line 3
    return v0
.end method

.method public final e()LQA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->B:LQA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, LFz0/a;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, LFz0/a;

    .line 12
    .line 13
    iget-wide v3, p0, LFz0/a;->a:J

    .line 14
    .line 15
    iget-wide v5, p1, LFz0/a;->a:J

    .line 16
    .line 17
    cmp-long v1, v3, v5

    .line 18
    .line 19
    if-eqz v1, :cond_2

    .line 20
    .line 21
    return v2

    .line 22
    :cond_2
    iget-object v1, p0, LFz0/a;->b:Ljava/lang/String;

    .line 23
    .line 24
    iget-object v3, p1, LFz0/a;->b:Ljava/lang/String;

    .line 25
    .line 26
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-nez v1, :cond_3

    .line 31
    .line 32
    return v2

    .line 33
    :cond_3
    iget-object v1, p0, LFz0/a;->c:Ljava/lang/Long;

    .line 34
    .line 35
    iget-object v3, p1, LFz0/a;->c:Ljava/lang/Long;

    .line 36
    .line 37
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    if-nez v1, :cond_4

    .line 42
    .line 43
    return v2

    .line 44
    :cond_4
    iget-boolean v1, p0, LFz0/a;->d:Z

    .line 45
    .line 46
    iget-boolean v3, p1, LFz0/a;->d:Z

    .line 47
    .line 48
    if-eq v1, v3, :cond_5

    .line 49
    .line 50
    return v2

    .line 51
    :cond_5
    iget-wide v3, p0, LFz0/a;->e:J

    .line 52
    .line 53
    iget-wide v5, p1, LFz0/a;->e:J

    .line 54
    .line 55
    cmp-long v1, v3, v5

    .line 56
    .line 57
    if-eqz v1, :cond_6

    .line 58
    .line 59
    return v2

    .line 60
    :cond_6
    iget-wide v3, p0, LFz0/a;->f:J

    .line 61
    .line 62
    iget-wide v5, p1, LFz0/a;->f:J

    .line 63
    .line 64
    cmp-long v1, v3, v5

    .line 65
    .line 66
    if-eqz v1, :cond_7

    .line 67
    .line 68
    return v2

    .line 69
    :cond_7
    iget-object v1, p0, LFz0/a;->g:Ljava/lang/Long;

    .line 70
    .line 71
    iget-object v3, p1, LFz0/a;->g:Ljava/lang/Long;

    .line 72
    .line 73
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v1

    .line 77
    if-nez v1, :cond_8

    .line 78
    .line 79
    return v2

    .line 80
    :cond_8
    iget-object v1, p0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 81
    .line 82
    iget-object v3, p1, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 83
    .line 84
    if-eq v1, v3, :cond_9

    .line 85
    .line 86
    return v2

    .line 87
    :cond_9
    iget-object v1, p0, LFz0/a;->i:Ljava/lang/String;

    .line 88
    .line 89
    iget-object v3, p1, LFz0/a;->i:Ljava/lang/String;

    .line 90
    .line 91
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v1

    .line 95
    if-nez v1, :cond_a

    .line 96
    .line 97
    return v2

    .line 98
    :cond_a
    iget-boolean v1, p0, LFz0/a;->j:Z

    .line 99
    .line 100
    iget-boolean v3, p1, LFz0/a;->j:Z

    .line 101
    .line 102
    if-eq v1, v3, :cond_b

    .line 103
    .line 104
    return v2

    .line 105
    :cond_b
    iget-boolean v1, p0, LFz0/a;->k:Z

    .line 106
    .line 107
    iget-boolean v3, p1, LFz0/a;->k:Z

    .line 108
    .line 109
    if-eq v1, v3, :cond_c

    .line 110
    .line 111
    return v2

    .line 112
    :cond_c
    iget-boolean v1, p0, LFz0/a;->l:Z

    .line 113
    .line 114
    iget-boolean v3, p1, LFz0/a;->l:Z

    .line 115
    .line 116
    if-eq v1, v3, :cond_d

    .line 117
    .line 118
    return v2

    .line 119
    :cond_d
    iget-boolean v1, p0, LFz0/a;->m:Z

    .line 120
    .line 121
    iget-boolean v3, p1, LFz0/a;->m:Z

    .line 122
    .line 123
    if-eq v1, v3, :cond_e

    .line 124
    .line 125
    return v2

    .line 126
    :cond_e
    iget-boolean v1, p0, LFz0/a;->n:Z

    .line 127
    .line 128
    iget-boolean v3, p1, LFz0/a;->n:Z

    .line 129
    .line 130
    if-eq v1, v3, :cond_f

    .line 131
    .line 132
    return v2

    .line 133
    :cond_f
    iget-boolean v1, p0, LFz0/a;->o:Z

    .line 134
    .line 135
    iget-boolean v3, p1, LFz0/a;->o:Z

    .line 136
    .line 137
    if-eq v1, v3, :cond_10

    .line 138
    .line 139
    return v2

    .line 140
    :cond_10
    iget-boolean v1, p0, LFz0/a;->p:Z

    .line 141
    .line 142
    iget-boolean v3, p1, LFz0/a;->p:Z

    .line 143
    .line 144
    if-eq v1, v3, :cond_11

    .line 145
    .line 146
    return v2

    .line 147
    :cond_11
    iget-boolean v1, p0, LFz0/a;->q:Z

    .line 148
    .line 149
    iget-boolean v3, p1, LFz0/a;->q:Z

    .line 150
    .line 151
    if-eq v1, v3, :cond_12

    .line 152
    .line 153
    return v2

    .line 154
    :cond_12
    iget-boolean v1, p0, LFz0/a;->r:Z

    .line 155
    .line 156
    iget-boolean v3, p1, LFz0/a;->r:Z

    .line 157
    .line 158
    if-eq v1, v3, :cond_13

    .line 159
    .line 160
    return v2

    .line 161
    :cond_13
    iget-boolean v1, p0, LFz0/a;->s:Z

    .line 162
    .line 163
    iget-boolean v3, p1, LFz0/a;->s:Z

    .line 164
    .line 165
    if-eq v1, v3, :cond_14

    .line 166
    .line 167
    return v2

    .line 168
    :cond_14
    iget-object v1, p0, LFz0/a;->t:Ljava/lang/String;

    .line 169
    .line 170
    iget-object v3, p1, LFz0/a;->t:Ljava/lang/String;

    .line 171
    .line 172
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 173
    .line 174
    .line 175
    move-result v1

    .line 176
    if-nez v1, :cond_15

    .line 177
    .line 178
    return v2

    .line 179
    :cond_15
    iget-boolean v1, p0, LFz0/a;->u:Z

    .line 180
    .line 181
    iget-boolean v3, p1, LFz0/a;->u:Z

    .line 182
    .line 183
    if-eq v1, v3, :cond_16

    .line 184
    .line 185
    return v2

    .line 186
    :cond_16
    iget-boolean v1, p0, LFz0/a;->v:Z

    .line 187
    .line 188
    iget-boolean v3, p1, LFz0/a;->v:Z

    .line 189
    .line 190
    if-eq v1, v3, :cond_17

    .line 191
    .line 192
    return v2

    .line 193
    :cond_17
    iget-boolean v1, p0, LFz0/a;->w:Z

    .line 194
    .line 195
    iget-boolean v3, p1, LFz0/a;->w:Z

    .line 196
    .line 197
    if-eq v1, v3, :cond_18

    .line 198
    .line 199
    return v2

    .line 200
    :cond_18
    iget-boolean v1, p0, LFz0/a;->x:Z

    .line 201
    .line 202
    iget-boolean v3, p1, LFz0/a;->x:Z

    .line 203
    .line 204
    if-eq v1, v3, :cond_19

    .line 205
    .line 206
    return v2

    .line 207
    :cond_19
    iget-wide v3, p0, LFz0/a;->y:J

    .line 208
    .line 209
    iget-wide v5, p1, LFz0/a;->y:J

    .line 210
    .line 211
    invoke-static {v3, v4, v5, v6}, Ll8/b$a$c;->h(JJ)Z

    .line 212
    .line 213
    .line 214
    move-result v1

    .line 215
    if-nez v1, :cond_1a

    .line 216
    .line 217
    return v2

    .line 218
    :cond_1a
    iget-object v1, p0, LFz0/a;->z:LgB0/a;

    .line 219
    .line 220
    iget-object v3, p1, LFz0/a;->z:LgB0/a;

    .line 221
    .line 222
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 223
    .line 224
    .line 225
    move-result v1

    .line 226
    if-nez v1, :cond_1b

    .line 227
    .line 228
    return v2

    .line 229
    :cond_1b
    iget-object v1, p0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    .line 230
    .line 231
    iget-object v3, p1, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    .line 232
    .line 233
    if-eq v1, v3, :cond_1c

    .line 234
    .line 235
    return v2

    .line 236
    :cond_1c
    iget-object v1, p0, LFz0/a;->B:LQA0/b;

    .line 237
    .line 238
    iget-object v3, p1, LFz0/a;->B:LQA0/b;

    .line 239
    .line 240
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 241
    .line 242
    .line 243
    move-result v1

    .line 244
    if-nez v1, :cond_1d

    .line 245
    .line 246
    return v2

    .line 247
    :cond_1d
    iget-object v1, p0, LFz0/a;->C:LRA0/h;

    .line 248
    .line 249
    iget-object v3, p1, LFz0/a;->C:LRA0/h;

    .line 250
    .line 251
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 252
    .line 253
    .line 254
    move-result v1

    .line 255
    if-nez v1, :cond_1e

    .line 256
    .line 257
    return v2

    .line 258
    :cond_1e
    iget-object v1, p0, LFz0/a;->D:LWA0/c;

    .line 259
    .line 260
    iget-object v3, p1, LFz0/a;->D:LWA0/c;

    .line 261
    .line 262
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 263
    .line 264
    .line 265
    move-result v1

    .line 266
    if-nez v1, :cond_1f

    .line 267
    .line 268
    return v2

    .line 269
    :cond_1f
    iget-object v1, p0, LFz0/a;->E:LWA0/a;

    .line 270
    .line 271
    iget-object v3, p1, LFz0/a;->E:LWA0/a;

    .line 272
    .line 273
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 274
    .line 275
    .line 276
    move-result v1

    .line 277
    if-nez v1, :cond_20

    .line 278
    .line 279
    return v2

    .line 280
    :cond_20
    iget-object v1, p0, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    .line 281
    .line 282
    iget-object v3, p1, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    .line 283
    .line 284
    if-eq v1, v3, :cond_21

    .line 285
    .line 286
    return v2

    .line 287
    :cond_21
    iget-object v1, p0, LFz0/a;->G:Ljava/util/List;

    .line 288
    .line 289
    iget-object p1, p1, LFz0/a;->G:Ljava/util/List;

    .line 290
    .line 291
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 292
    .line 293
    .line 294
    move-result p1

    .line 295
    if-nez p1, :cond_22

    .line 296
    .line 297
    return v2

    .line 298
    :cond_22
    return v0
.end method

.method public final f()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->g:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LFz0/a;->i:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 5

    .line 1
    iget-wide v0, p0, LFz0/a;->a:J

    .line 2
    .line 3
    invoke-static {v0, v1}, Lu/l;->a(J)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget-object v1, p0, LFz0/a;->b:Ljava/lang/String;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    mul-int/lit8 v0, v0, 0x1f

    .line 17
    .line 18
    iget-object v1, p0, LFz0/a;->c:Ljava/lang/Long;

    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    if-nez v1, :cond_0

    .line 22
    .line 23
    const/4 v1, 0x0

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    :goto_0
    add-int/2addr v0, v1

    .line 30
    mul-int/lit8 v0, v0, 0x1f

    .line 31
    .line 32
    iget-boolean v1, p0, LFz0/a;->d:Z

    .line 33
    .line 34
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    add-int/2addr v0, v1

    .line 39
    mul-int/lit8 v0, v0, 0x1f

    .line 40
    .line 41
    iget-wide v3, p0, LFz0/a;->e:J

    .line 42
    .line 43
    invoke-static {v3, v4}, Lu/l;->a(J)I

    .line 44
    .line 45
    .line 46
    move-result v1

    .line 47
    add-int/2addr v0, v1

    .line 48
    mul-int/lit8 v0, v0, 0x1f

    .line 49
    .line 50
    iget-wide v3, p0, LFz0/a;->f:J

    .line 51
    .line 52
    invoke-static {v3, v4}, Lu/l;->a(J)I

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    add-int/2addr v0, v1

    .line 57
    mul-int/lit8 v0, v0, 0x1f

    .line 58
    .line 59
    iget-object v1, p0, LFz0/a;->g:Ljava/lang/Long;

    .line 60
    .line 61
    if-nez v1, :cond_1

    .line 62
    .line 63
    const/4 v1, 0x0

    .line 64
    goto :goto_1

    .line 65
    :cond_1
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    :goto_1
    add-int/2addr v0, v1

    .line 70
    mul-int/lit8 v0, v0, 0x1f

    .line 71
    .line 72
    iget-object v1, p0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 73
    .line 74
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    add-int/2addr v0, v1

    .line 79
    mul-int/lit8 v0, v0, 0x1f

    .line 80
    .line 81
    iget-object v1, p0, LFz0/a;->i:Ljava/lang/String;

    .line 82
    .line 83
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 84
    .line 85
    .line 86
    move-result v1

    .line 87
    add-int/2addr v0, v1

    .line 88
    mul-int/lit8 v0, v0, 0x1f

    .line 89
    .line 90
    iget-boolean v1, p0, LFz0/a;->j:Z

    .line 91
    .line 92
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 93
    .line 94
    .line 95
    move-result v1

    .line 96
    add-int/2addr v0, v1

    .line 97
    mul-int/lit8 v0, v0, 0x1f

    .line 98
    .line 99
    iget-boolean v1, p0, LFz0/a;->k:Z

    .line 100
    .line 101
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 102
    .line 103
    .line 104
    move-result v1

    .line 105
    add-int/2addr v0, v1

    .line 106
    mul-int/lit8 v0, v0, 0x1f

    .line 107
    .line 108
    iget-boolean v1, p0, LFz0/a;->l:Z

    .line 109
    .line 110
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 111
    .line 112
    .line 113
    move-result v1

    .line 114
    add-int/2addr v0, v1

    .line 115
    mul-int/lit8 v0, v0, 0x1f

    .line 116
    .line 117
    iget-boolean v1, p0, LFz0/a;->m:Z

    .line 118
    .line 119
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 120
    .line 121
    .line 122
    move-result v1

    .line 123
    add-int/2addr v0, v1

    .line 124
    mul-int/lit8 v0, v0, 0x1f

    .line 125
    .line 126
    iget-boolean v1, p0, LFz0/a;->n:Z

    .line 127
    .line 128
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 129
    .line 130
    .line 131
    move-result v1

    .line 132
    add-int/2addr v0, v1

    .line 133
    mul-int/lit8 v0, v0, 0x1f

    .line 134
    .line 135
    iget-boolean v1, p0, LFz0/a;->o:Z

    .line 136
    .line 137
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 138
    .line 139
    .line 140
    move-result v1

    .line 141
    add-int/2addr v0, v1

    .line 142
    mul-int/lit8 v0, v0, 0x1f

    .line 143
    .line 144
    iget-boolean v1, p0, LFz0/a;->p:Z

    .line 145
    .line 146
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 147
    .line 148
    .line 149
    move-result v1

    .line 150
    add-int/2addr v0, v1

    .line 151
    mul-int/lit8 v0, v0, 0x1f

    .line 152
    .line 153
    iget-boolean v1, p0, LFz0/a;->q:Z

    .line 154
    .line 155
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 156
    .line 157
    .line 158
    move-result v1

    .line 159
    add-int/2addr v0, v1

    .line 160
    mul-int/lit8 v0, v0, 0x1f

    .line 161
    .line 162
    iget-boolean v1, p0, LFz0/a;->r:Z

    .line 163
    .line 164
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 165
    .line 166
    .line 167
    move-result v1

    .line 168
    add-int/2addr v0, v1

    .line 169
    mul-int/lit8 v0, v0, 0x1f

    .line 170
    .line 171
    iget-boolean v1, p0, LFz0/a;->s:Z

    .line 172
    .line 173
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 174
    .line 175
    .line 176
    move-result v1

    .line 177
    add-int/2addr v0, v1

    .line 178
    mul-int/lit8 v0, v0, 0x1f

    .line 179
    .line 180
    iget-object v1, p0, LFz0/a;->t:Ljava/lang/String;

    .line 181
    .line 182
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 183
    .line 184
    .line 185
    move-result v1

    .line 186
    add-int/2addr v0, v1

    .line 187
    mul-int/lit8 v0, v0, 0x1f

    .line 188
    .line 189
    iget-boolean v1, p0, LFz0/a;->u:Z

    .line 190
    .line 191
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 192
    .line 193
    .line 194
    move-result v1

    .line 195
    add-int/2addr v0, v1

    .line 196
    mul-int/lit8 v0, v0, 0x1f

    .line 197
    .line 198
    iget-boolean v1, p0, LFz0/a;->v:Z

    .line 199
    .line 200
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 201
    .line 202
    .line 203
    move-result v1

    .line 204
    add-int/2addr v0, v1

    .line 205
    mul-int/lit8 v0, v0, 0x1f

    .line 206
    .line 207
    iget-boolean v1, p0, LFz0/a;->w:Z

    .line 208
    .line 209
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 210
    .line 211
    .line 212
    move-result v1

    .line 213
    add-int/2addr v0, v1

    .line 214
    mul-int/lit8 v0, v0, 0x1f

    .line 215
    .line 216
    iget-boolean v1, p0, LFz0/a;->x:Z

    .line 217
    .line 218
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 219
    .line 220
    .line 221
    move-result v1

    .line 222
    add-int/2addr v0, v1

    .line 223
    mul-int/lit8 v0, v0, 0x1f

    .line 224
    .line 225
    iget-wide v3, p0, LFz0/a;->y:J

    .line 226
    .line 227
    invoke-static {v3, v4}, Ll8/b$a$c;->k(J)I

    .line 228
    .line 229
    .line 230
    move-result v1

    .line 231
    add-int/2addr v0, v1

    .line 232
    mul-int/lit8 v0, v0, 0x1f

    .line 233
    .line 234
    iget-object v1, p0, LFz0/a;->z:LgB0/a;

    .line 235
    .line 236
    if-nez v1, :cond_2

    .line 237
    .line 238
    const/4 v1, 0x0

    .line 239
    goto :goto_2

    .line 240
    :cond_2
    invoke-virtual {v1}, LgB0/a;->hashCode()I

    .line 241
    .line 242
    .line 243
    move-result v1

    .line 244
    :goto_2
    add-int/2addr v0, v1

    .line 245
    mul-int/lit8 v0, v0, 0x1f

    .line 246
    .line 247
    iget-object v1, p0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    .line 248
    .line 249
    if-nez v1, :cond_3

    .line 250
    .line 251
    const/4 v1, 0x0

    .line 252
    goto :goto_3

    .line 253
    :cond_3
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 254
    .line 255
    .line 256
    move-result v1

    .line 257
    :goto_3
    add-int/2addr v0, v1

    .line 258
    mul-int/lit8 v0, v0, 0x1f

    .line 259
    .line 260
    iget-object v1, p0, LFz0/a;->B:LQA0/b;

    .line 261
    .line 262
    if-nez v1, :cond_4

    .line 263
    .line 264
    const/4 v1, 0x0

    .line 265
    goto :goto_4

    .line 266
    :cond_4
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 267
    .line 268
    .line 269
    move-result v1

    .line 270
    :goto_4
    add-int/2addr v0, v1

    .line 271
    mul-int/lit8 v0, v0, 0x1f

    .line 272
    .line 273
    iget-object v1, p0, LFz0/a;->C:LRA0/h;

    .line 274
    .line 275
    if-nez v1, :cond_5

    .line 276
    .line 277
    const/4 v1, 0x0

    .line 278
    goto :goto_5

    .line 279
    :cond_5
    invoke-virtual {v1}, LRA0/h;->hashCode()I

    .line 280
    .line 281
    .line 282
    move-result v1

    .line 283
    :goto_5
    add-int/2addr v0, v1

    .line 284
    mul-int/lit8 v0, v0, 0x1f

    .line 285
    .line 286
    iget-object v1, p0, LFz0/a;->D:LWA0/c;

    .line 287
    .line 288
    if-nez v1, :cond_6

    .line 289
    .line 290
    const/4 v1, 0x0

    .line 291
    goto :goto_6

    .line 292
    :cond_6
    invoke-virtual {v1}, LWA0/c;->hashCode()I

    .line 293
    .line 294
    .line 295
    move-result v1

    .line 296
    :goto_6
    add-int/2addr v0, v1

    .line 297
    mul-int/lit8 v0, v0, 0x1f

    .line 298
    .line 299
    iget-object v1, p0, LFz0/a;->E:LWA0/a;

    .line 300
    .line 301
    if-nez v1, :cond_7

    .line 302
    .line 303
    goto :goto_7

    .line 304
    :cond_7
    invoke-virtual {v1}, LWA0/a;->hashCode()I

    .line 305
    .line 306
    .line 307
    move-result v2

    .line 308
    :goto_7
    add-int/2addr v0, v2

    .line 309
    mul-int/lit8 v0, v0, 0x1f

    .line 310
    .line 311
    iget-object v1, p0, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    .line 312
    .line 313
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 314
    .line 315
    .line 316
    move-result v1

    .line 317
    add-int/2addr v0, v1

    .line 318
    mul-int/lit8 v0, v0, 0x1f

    .line 319
    .line 320
    iget-object v1, p0, LFz0/a;->G:Ljava/util/List;

    .line 321
    .line 322
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 323
    .line 324
    .line 325
    move-result v1

    .line 326
    add-int/2addr v0, v1

    .line 327
    return v0
.end method

.method public final i()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LTT/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LFz0/a;->G:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()LgB0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->z:LgB0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()J
    .locals 2

    .line 1
    iget-wide v0, p0, LFz0/a;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final m()Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-wide v1, p0, LFz0/a;->a:J

    .line 2
    .line 3
    iget-wide v4, p0, LFz0/a;->e:J

    .line 4
    .line 5
    iget-object v3, p0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 6
    .line 7
    new-instance v0, Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams;

    .line 8
    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams;-><init>(JLorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;J)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public final n()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public final o()LWA0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->D:LWA0/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->k:Z

    .line 2
    .line 3
    return v0
.end method

.method public final q()Lorg/xbet/related/api/presentation/RelatedParams;
    .locals 8
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-wide v2, p0, LFz0/a;->a:J

    .line 2
    .line 3
    iget-boolean v1, p0, LFz0/a;->d:Z

    .line 4
    .line 5
    iget-object v0, p0, LFz0/a;->g:Ljava/lang/Long;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 10
    .line 11
    .line 12
    move-result-wide v4

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const-wide/16 v4, -0x1

    .line 15
    .line 16
    :goto_0
    sget-object v6, Lorg/xbet/related/api/presentation/RelatedParams$ScreenType;->RELATED_GAMES_SCREEN:Lorg/xbet/related/api/presentation/RelatedParams$ScreenType;

    .line 17
    .line 18
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 19
    .line 20
    new-instance v0, Lorg/xbet/related/api/presentation/RelatedParams;

    .line 21
    .line 22
    invoke-direct/range {v0 .. v7}, Lorg/xbet/related/api/presentation/RelatedParams;-><init>(ZJJLorg/xbet/related/api/presentation/RelatedParams$ScreenType;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method

.method public final r()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->v:Z

    .line 2
    .line 3
    return v0
.end method

.method public final s()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LFz0/a;->t:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t()J
    .locals 2

    .line 1
    iget-wide v0, p0, LFz0/a;->e:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public toString()Ljava/lang/String;
    .locals 38
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-wide v1, v0, LFz0/a;->a:J

    .line 4
    .line 5
    iget-object v3, v0, LFz0/a;->b:Ljava/lang/String;

    .line 6
    .line 7
    iget-object v4, v0, LFz0/a;->c:Ljava/lang/Long;

    .line 8
    .line 9
    iget-boolean v5, v0, LFz0/a;->d:Z

    .line 10
    .line 11
    iget-wide v6, v0, LFz0/a;->e:J

    .line 12
    .line 13
    iget-wide v8, v0, LFz0/a;->f:J

    .line 14
    .line 15
    iget-object v10, v0, LFz0/a;->g:Ljava/lang/Long;

    .line 16
    .line 17
    iget-object v11, v0, LFz0/a;->h:Lorg/xbet/sportgame/core/domain/models/LaunchGameScenarioParams$GameConditionType;

    .line 18
    .line 19
    iget-object v12, v0, LFz0/a;->i:Ljava/lang/String;

    .line 20
    .line 21
    iget-boolean v13, v0, LFz0/a;->j:Z

    .line 22
    .line 23
    iget-boolean v14, v0, LFz0/a;->k:Z

    .line 24
    .line 25
    iget-boolean v15, v0, LFz0/a;->l:Z

    .line 26
    .line 27
    move/from16 v16, v15

    .line 28
    .line 29
    iget-boolean v15, v0, LFz0/a;->m:Z

    .line 30
    .line 31
    move/from16 v17, v15

    .line 32
    .line 33
    iget-boolean v15, v0, LFz0/a;->n:Z

    .line 34
    .line 35
    move/from16 v18, v15

    .line 36
    .line 37
    iget-boolean v15, v0, LFz0/a;->o:Z

    .line 38
    .line 39
    move/from16 v19, v15

    .line 40
    .line 41
    iget-boolean v15, v0, LFz0/a;->p:Z

    .line 42
    .line 43
    move/from16 v20, v15

    .line 44
    .line 45
    iget-boolean v15, v0, LFz0/a;->q:Z

    .line 46
    .line 47
    move/from16 v21, v15

    .line 48
    .line 49
    iget-boolean v15, v0, LFz0/a;->r:Z

    .line 50
    .line 51
    move/from16 v22, v15

    .line 52
    .line 53
    iget-boolean v15, v0, LFz0/a;->s:Z

    .line 54
    .line 55
    move/from16 v23, v15

    .line 56
    .line 57
    iget-object v15, v0, LFz0/a;->t:Ljava/lang/String;

    .line 58
    .line 59
    move-object/from16 v24, v15

    .line 60
    .line 61
    iget-boolean v15, v0, LFz0/a;->u:Z

    .line 62
    .line 63
    move/from16 v25, v15

    .line 64
    .line 65
    iget-boolean v15, v0, LFz0/a;->v:Z

    .line 66
    .line 67
    move/from16 v26, v15

    .line 68
    .line 69
    iget-boolean v15, v0, LFz0/a;->w:Z

    .line 70
    .line 71
    move/from16 v27, v15

    .line 72
    .line 73
    iget-boolean v15, v0, LFz0/a;->x:Z

    .line 74
    .line 75
    move/from16 v28, v14

    .line 76
    .line 77
    move/from16 v29, v15

    .line 78
    .line 79
    iget-wide v14, v0, LFz0/a;->y:J

    .line 80
    .line 81
    invoke-static {v14, v15}, Ll8/b$a$c;->n(J)Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v14

    .line 85
    iget-object v15, v0, LFz0/a;->z:LgB0/a;

    .line 86
    .line 87
    move-object/from16 v30, v15

    .line 88
    .line 89
    iget-object v15, v0, LFz0/a;->A:Lorg/xbet/sportgame/advanced/impl/presentation/models/CardInfoGame$Type;

    .line 90
    .line 91
    move-object/from16 v31, v15

    .line 92
    .line 93
    iget-object v15, v0, LFz0/a;->B:LQA0/b;

    .line 94
    .line 95
    move-object/from16 v32, v15

    .line 96
    .line 97
    iget-object v15, v0, LFz0/a;->C:LRA0/h;

    .line 98
    .line 99
    move-object/from16 v33, v15

    .line 100
    .line 101
    iget-object v15, v0, LFz0/a;->D:LWA0/c;

    .line 102
    .line 103
    move-object/from16 v34, v15

    .line 104
    .line 105
    iget-object v15, v0, LFz0/a;->E:LWA0/a;

    .line 106
    .line 107
    move-object/from16 v35, v15

    .line 108
    .line 109
    iget-object v15, v0, LFz0/a;->F:Lorg/xbet/sportgame/advanced/impl/presentation/state/CardExpandType;

    .line 110
    .line 111
    move-object/from16 v36, v15

    .line 112
    .line 113
    iget-object v15, v0, LFz0/a;->G:Ljava/util/List;

    .line 114
    .line 115
    new-instance v0, Ljava/lang/StringBuilder;

    .line 116
    .line 117
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 118
    .line 119
    .line 120
    move-object/from16 v37, v15

    .line 121
    .line 122
    const-string v15, "GameAdvancedStateModel(gameId="

    .line 123
    .line 124
    invoke-virtual {v0, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 125
    .line 126
    .line 127
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 128
    .line 129
    .line 130
    const-string v1, ", statGameId="

    .line 131
    .line 132
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 133
    .line 134
    .line 135
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 136
    .line 137
    .line 138
    const-string v1, ", constId="

    .line 139
    .line 140
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 141
    .line 142
    .line 143
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 144
    .line 145
    .line 146
    const-string v1, ", live="

    .line 147
    .line 148
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 149
    .line 150
    .line 151
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 152
    .line 153
    .line 154
    const-string v1, ", sportId="

    .line 155
    .line 156
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 157
    .line 158
    .line 159
    invoke-virtual {v0, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 160
    .line 161
    .line 162
    const-string v1, ", subGameId="

    .line 163
    .line 164
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 165
    .line 166
    .line 167
    invoke-virtual {v0, v8, v9}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 168
    .line 169
    .line 170
    const-string v1, ", champId="

    .line 171
    .line 172
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 173
    .line 174
    .line 175
    invoke-virtual {v0, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 176
    .line 177
    .line 178
    const-string v1, ", gameConditionType="

    .line 179
    .line 180
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 181
    .line 182
    .line 183
    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 184
    .line 185
    .line 186
    const-string v1, ", componentKey="

    .line 187
    .line 188
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 189
    .line 190
    .line 191
    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 192
    .line 193
    .line 194
    const-string v1, ", quickBetEnabled="

    .line 195
    .line 196
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 197
    .line 198
    .line 199
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 200
    .line 201
    .line 202
    const-string v1, ", menuButtonEnabled="

    .line 203
    .line 204
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 205
    .line 206
    .line 207
    move/from16 v1, v28

    .line 208
    .line 209
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 210
    .line 211
    .line 212
    const-string v1, ", quickBetAvailable="

    .line 213
    .line 214
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 215
    .line 216
    .line 217
    move/from16 v1, v16

    .line 218
    .line 219
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 220
    .line 221
    .line 222
    const-string v1, ", marketsAvailable="

    .line 223
    .line 224
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 225
    .line 226
    .line 227
    move/from16 v1, v17

    .line 228
    .line 229
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 230
    .line 231
    .line 232
    const-string v1, ", statisticAvailable="

    .line 233
    .line 234
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 235
    .line 236
    .line 237
    move/from16 v1, v18

    .line 238
    .line 239
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 240
    .line 241
    .line 242
    const-string v1, ", filterAvailable="

    .line 243
    .line 244
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 245
    .line 246
    .line 247
    move/from16 v1, v19

    .line 248
    .line 249
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 250
    .line 251
    .line 252
    const-string v1, ", duelAvailable="

    .line 253
    .line 254
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 255
    .line 256
    .line 257
    move/from16 v1, v20

    .line 258
    .line 259
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 260
    .line 261
    .line 262
    const-string v1, ", subscriptionAvailable="

    .line 263
    .line 264
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 265
    .line 266
    .line 267
    move/from16 v1, v21

    .line 268
    .line 269
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 270
    .line 271
    .line 272
    const-string v1, ", broadcastingRun="

    .line 273
    .line 274
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 275
    .line 276
    .line 277
    move/from16 v1, v22

    .line 278
    .line 279
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 280
    .line 281
    .line 282
    const-string v1, ", isNightModeEnable="

    .line 283
    .line 284
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 285
    .line 286
    .line 287
    move/from16 v1, v23

    .line 288
    .line 289
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 290
    .line 291
    .line 292
    const-string v1, ", specialEventBackgroundUrl="

    .line 293
    .line 294
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 295
    .line 296
    .line 297
    move-object/from16 v1, v24

    .line 298
    .line 299
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 300
    .line 301
    .line 302
    const-string v1, ", isLoading="

    .line 303
    .line 304
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 305
    .line 306
    .line 307
    move/from16 v1, v25

    .line 308
    .line 309
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 310
    .line 311
    .line 312
    const-string v1, ", showSubGames="

    .line 313
    .line 314
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 315
    .line 316
    .line 317
    move/from16 v1, v26

    .line 318
    .line 319
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 320
    .line 321
    .line 322
    const-string v1, ", transferContinue="

    .line 323
    .line 324
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 325
    .line 326
    .line 327
    move/from16 v1, v27

    .line 328
    .line 329
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 330
    .line 331
    .line 332
    const-string v1, ", transferFailed="

    .line 333
    .line 334
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 335
    .line 336
    .line 337
    move/from16 v1, v29

    .line 338
    .line 339
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 340
    .line 341
    .line 342
    const-string v1, ", transferTimeLeft="

    .line 343
    .line 344
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 345
    .line 346
    .line 347
    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 348
    .line 349
    .line 350
    const-string v1, ", gameBroadcastModel="

    .line 351
    .line 352
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 353
    .line 354
    .line 355
    move-object/from16 v1, v30

    .line 356
    .line 357
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 358
    .line 359
    .line 360
    const-string v1, ", currentGameInfoCardType="

    .line 361
    .line 362
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 363
    .line 364
    .line 365
    move-object/from16 v1, v31

    .line 366
    .line 367
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 368
    .line 369
    .line 370
    const-string v1, ", cardSectionModel="

    .line 371
    .line 372
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 373
    .line 374
    .line 375
    move-object/from16 v1, v32

    .line 376
    .line 377
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 378
    .line 379
    .line 380
    const-string v1, ", timerModel="

    .line 381
    .line 382
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 383
    .line 384
    .line 385
    move-object/from16 v1, v33

    .line 386
    .line 387
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 388
    .line 389
    .line 390
    const-string v1, ", matchCacheScoreModel="

    .line 391
    .line 392
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 393
    .line 394
    .line 395
    move-object/from16 v1, v34

    .line 396
    .line 397
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 398
    .line 399
    .line 400
    const-string v1, ", cachePenaltyModel="

    .line 401
    .line 402
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 403
    .line 404
    .line 405
    move-object/from16 v1, v35

    .line 406
    .line 407
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 408
    .line 409
    .line 410
    const-string v1, ", cardExpandType="

    .line 411
    .line 412
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 413
    .line 414
    .line 415
    move-object/from16 v1, v36

    .line 416
    .line 417
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 418
    .line 419
    .line 420
    const-string v1, ", favoriteTeamModelList="

    .line 421
    .line 422
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 423
    .line 424
    .line 425
    move-object/from16 v1, v37

    .line 426
    .line 427
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 428
    .line 429
    .line 430
    const-string v1, ")"

    .line 431
    .line 432
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 433
    .line 434
    .line 435
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 436
    .line 437
    .line 438
    move-result-object v0

    .line 439
    return-object v0
.end method

.method public final u()J
    .locals 2

    .line 1
    iget-wide v0, p0, LFz0/a;->f:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final v()LRA0/h;
    .locals 1

    .line 1
    iget-object v0, p0, LFz0/a;->C:LRA0/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->w:Z

    .line 2
    .line 3
    return v0
.end method

.method public final x()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->x:Z

    .line 2
    .line 3
    return v0
.end method

.method public final y()J
    .locals 2

    .line 1
    iget-wide v0, p0, LFz0/a;->y:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final z()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LFz0/a;->s:Z

    .line 2
    .line 3
    return v0
.end method
