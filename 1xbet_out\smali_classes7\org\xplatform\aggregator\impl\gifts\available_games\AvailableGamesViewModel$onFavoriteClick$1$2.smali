.class final Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.available_games.AvailableGamesViewModel$onFavoriteClick$1$2"
    f = "AvailableGamesViewModel.kt"
    l = {
        0xa2,
        0xa8
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->Z3(JZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $isFavorite:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;


# direct methods
.method public constructor <init>(ZLorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;",
            ">;)V"
        }
    .end annotation

    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$isFavorite:Z

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;

    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$isFavorite:Z

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;-><init>(ZLorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    :goto_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    move-object v9, p0

    .line 28
    goto :goto_2

    .line 29
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$isFavorite:Z

    .line 33
    .line 34
    if-eqz p1, :cond_3

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 37
    .line 38
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->C3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lf81/d;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 43
    .line 44
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 45
    .line 46
    .line 47
    move-result-wide v5

    .line 48
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->s3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lek0/a;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    invoke-virtual {p1}, Lek0/a;->c()Z

    .line 55
    .line 56
    .line 57
    move-result v7

    .line 58
    iput v3, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->label:I

    .line 59
    .line 60
    const/4 v8, 0x0

    .line 61
    move-object v9, p0

    .line 62
    invoke-interface/range {v4 .. v9}, Lf81/d;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    if-ne p1, v0, :cond_4

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_3
    move-object v9, p0

    .line 70
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 71
    .line 72
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->r3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lf81/a;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iget-object v1, v9, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 77
    .line 78
    iget-object v3, v9, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 79
    .line 80
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->s3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lek0/a;

    .line 81
    .line 82
    .line 83
    move-result-object v3

    .line 84
    invoke-virtual {v3}, Lek0/a;->c()Z

    .line 85
    .line 86
    .line 87
    move-result v3

    .line 88
    iput v2, v9, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;->label:I

    .line 89
    .line 90
    const/4 v2, 0x0

    .line 91
    invoke-interface {p1, v1, v3, v2, p0}, Lf81/a;->a(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    if-ne p1, v0, :cond_4

    .line 96
    .line 97
    :goto_1
    return-object v0

    .line 98
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 99
    .line 100
    return-object p1
.end method
