.class public final LhD0/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\u001a7\u0010\u0007\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0005H\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\"\u0014\u0010\u000c\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000f\u00b2\u0006\u000e\u0010\u000e\u001a\u00020\r8\n@\nX\u008a\u008e\u0002"
    }
    d2 = {
        "Ltc1/a;",
        "LjD0/c;",
        "LjD0/i;",
        "",
        "viewModel",
        "Lkotlin/Function0;",
        "onBackClick",
        "b",
        "(Ltc1/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V",
        "Lt0/i;",
        "a",
        "F",
        "shimmerHeight",
        "",
        "contentHeight",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LA11/a;->a:LA11/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LA11/a;->V()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    sput v0, LhD0/h;->a:F

    .line 8
    .line 9
    return-void
.end method

.method public static synthetic a(Ltc1/a;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LhD0/h;->e(Ltc1/a;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Ltc1/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V
    .locals 19
    .param p0    # Ltc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltc1/a<",
            "LjD0/c;",
            "LjD0/i;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "I)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move/from16 v2, p3

    .line 6
    .line 7
    const v3, -0x31db3bfc

    .line 8
    .line 9
    .line 10
    move-object/from16 v4, p2

    .line 11
    .line 12
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    and-int/lit8 v5, v2, 0x6

    .line 17
    .line 18
    if-nez v5, :cond_2

    .line 19
    .line 20
    and-int/lit8 v5, v2, 0x8

    .line 21
    .line 22
    if-nez v5, :cond_0

    .line 23
    .line 24
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v5

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v5

    .line 33
    :goto_0
    if-eqz v5, :cond_1

    .line 34
    .line 35
    const/4 v5, 0x4

    .line 36
    goto :goto_1

    .line 37
    :cond_1
    const/4 v5, 0x2

    .line 38
    :goto_1
    or-int/2addr v5, v2

    .line 39
    goto :goto_2

    .line 40
    :cond_2
    move v5, v2

    .line 41
    :goto_2
    and-int/lit8 v6, v2, 0x30

    .line 42
    .line 43
    if-nez v6, :cond_4

    .line 44
    .line 45
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v6

    .line 49
    if-eqz v6, :cond_3

    .line 50
    .line 51
    const/16 v6, 0x20

    .line 52
    .line 53
    goto :goto_3

    .line 54
    :cond_3
    const/16 v6, 0x10

    .line 55
    .line 56
    :goto_3
    or-int/2addr v5, v6

    .line 57
    :cond_4
    and-int/lit8 v6, v5, 0x13

    .line 58
    .line 59
    const/16 v7, 0x12

    .line 60
    .line 61
    if-ne v6, v7, :cond_6

    .line 62
    .line 63
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 64
    .line 65
    .line 66
    move-result v6

    .line 67
    if-nez v6, :cond_5

    .line 68
    .line 69
    goto :goto_4

    .line 70
    :cond_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 71
    .line 72
    .line 73
    move-object/from16 v16, v4

    .line 74
    .line 75
    goto/16 :goto_5

    .line 76
    .line 77
    :cond_6
    :goto_4
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 78
    .line 79
    .line 80
    move-result v6

    .line 81
    if-eqz v6, :cond_7

    .line 82
    .line 83
    const/4 v6, -0x1

    .line 84
    const-string v7, "org.xbet.statistic.cycling.impl.cycling_menu.presentation.components.TabsWidgetsCyclingMenuComponent (TabsWidgetsCyclingMenuComponent.kt:66)"

    .line 85
    .line 86
    invoke-static {v3, v5, v6, v7}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 87
    .line 88
    .line 89
    :cond_7
    invoke-static {}, Landroidx/compose/ui/platform/CompositionLocalsKt;->f()Landroidx/compose/runtime/x0;

    .line 90
    .line 91
    .line 92
    move-result-object v3

    .line 93
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    check-cast v3, Lt0/e;

    .line 98
    .line 99
    sget v6, LhD0/h;->a:F

    .line 100
    .line 101
    invoke-interface {v3, v6}, Lt0/e;->m1(F)I

    .line 102
    .line 103
    .line 104
    move-result v3

    .line 105
    const v6, 0x6e3c21fe

    .line 106
    .line 107
    .line 108
    invoke-interface {v4, v6}, Landroidx/compose/runtime/j;->t(I)V

    .line 109
    .line 110
    .line 111
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 112
    .line 113
    .line 114
    move-result-object v6

    .line 115
    sget-object v7, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 116
    .line 117
    invoke-virtual {v7}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 118
    .line 119
    .line 120
    move-result-object v7

    .line 121
    if-ne v6, v7, :cond_8

    .line 122
    .line 123
    invoke-static {v3}, Landroidx/compose/runtime/V0;->a(I)Landroidx/compose/runtime/h0;

    .line 124
    .line 125
    .line 126
    move-result-object v6

    .line 127
    invoke-interface {v4, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 128
    .line 129
    .line 130
    :cond_8
    check-cast v6, Landroidx/compose/runtime/h0;

    .line 131
    .line 132
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 133
    .line 134
    .line 135
    sget v3, Ltc1/a;->b1:I

    .line 136
    .line 137
    and-int/lit8 v5, v5, 0xe

    .line 138
    .line 139
    or-int/2addr v3, v5

    .line 140
    invoke-interface {v0, v4, v3}, Luc1/c;->w2(Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/r1;

    .line 141
    .line 142
    .line 143
    move-result-object v3

    .line 144
    sget-object v5, LA11/a;->a:LA11/a;

    .line 145
    .line 146
    invoke-virtual {v5}, LA11/a;->y()F

    .line 147
    .line 148
    .line 149
    move-result v7

    .line 150
    const/16 v11, 0xe

    .line 151
    .line 152
    const/4 v12, 0x0

    .line 153
    const/4 v8, 0x0

    .line 154
    const/4 v9, 0x0

    .line 155
    const/4 v10, 0x0

    .line 156
    invoke-static/range {v7 .. v12}, Landroidx/compose/foundation/layout/v0;->c(FFFFILjava/lang/Object;)Landroidx/compose/foundation/layout/u0;

    .line 157
    .line 158
    .line 159
    move-result-object v14

    .line 160
    invoke-static {}, LV01/s;->f()Landroidx/compose/runtime/x0;

    .line 161
    .line 162
    .line 163
    move-result-object v5

    .line 164
    invoke-interface {v4, v5}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 165
    .line 166
    .line 167
    move-result-object v5

    .line 168
    check-cast v5, Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 169
    .line 170
    invoke-virtual {v5}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackground-0d7_KjU()J

    .line 171
    .line 172
    .line 173
    move-result-wide v10

    .line 174
    new-instance v5, LhD0/h$a;

    .line 175
    .line 176
    invoke-direct {v5, v1}, LhD0/h$a;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 177
    .line 178
    .line 179
    const v7, 0x39fbdec0

    .line 180
    .line 181
    .line 182
    const/4 v8, 0x1

    .line 183
    const/16 v9, 0x36

    .line 184
    .line 185
    invoke-static {v7, v8, v5, v4, v9}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 186
    .line 187
    .line 188
    move-result-object v5

    .line 189
    new-instance v7, LhD0/h$b;

    .line 190
    .line 191
    invoke-direct {v7, v3, v6, v0}, LhD0/h$b;-><init>(Landroidx/compose/runtime/r1;Landroidx/compose/runtime/h0;Ltc1/a;)V

    .line 192
    .line 193
    .line 194
    const v3, -0x7bc0636b

    .line 195
    .line 196
    .line 197
    invoke-static {v3, v8, v7, v4, v9}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 198
    .line 199
    .line 200
    move-result-object v15

    .line 201
    const v17, 0x30000030

    .line 202
    .line 203
    .line 204
    const/16 v18, 0xbd

    .line 205
    .line 206
    move-object/from16 v16, v4

    .line 207
    .line 208
    const/4 v4, 0x0

    .line 209
    const/4 v6, 0x0

    .line 210
    const/4 v7, 0x0

    .line 211
    const/4 v8, 0x0

    .line 212
    const/4 v9, 0x0

    .line 213
    const-wide/16 v12, 0x0

    .line 214
    .line 215
    invoke-static/range {v4 .. v18}, Landroidx/compose/material3/ScaffoldKt;->a(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;IJJLandroidx/compose/foundation/layout/u0;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 216
    .line 217
    .line 218
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 219
    .line 220
    .line 221
    move-result v3

    .line 222
    if-eqz v3, :cond_9

    .line 223
    .line 224
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 225
    .line 226
    .line 227
    :cond_9
    :goto_5
    invoke-interface/range {v16 .. v16}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 228
    .line 229
    .line 230
    move-result-object v3

    .line 231
    if-eqz v3, :cond_a

    .line 232
    .line 233
    new-instance v4, LhD0/g;

    .line 234
    .line 235
    invoke-direct {v4, v0, v1, v2}, LhD0/g;-><init>(Ltc1/a;Lkotlin/jvm/functions/Function0;I)V

    .line 236
    .line 237
    .line 238
    invoke-interface {v3, v4}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 239
    .line 240
    .line 241
    :cond_a
    return-void
.end method

.method public static final c(Landroidx/compose/runtime/h0;)I
    .locals 0

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/P;->e()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final d(Landroidx/compose/runtime/h0;I)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Landroidx/compose/runtime/h0;->h(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e(Ltc1/a;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p3, p2}, LhD0/h;->b(Ltc1/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final synthetic f(Landroidx/compose/runtime/h0;)I
    .locals 0

    .line 1
    invoke-static {p0}, LhD0/h;->c(Landroidx/compose/runtime/h0;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic g(Landroidx/compose/runtime/h0;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, LhD0/h;->d(Landroidx/compose/runtime/h0;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h()F
    .locals 1

    .line 1
    sget v0, LhD0/h;->a:F

    .line 2
    .line 3
    return v0
.end method
