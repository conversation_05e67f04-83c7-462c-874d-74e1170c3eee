.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g;
.super Lkotlin/coroutines/a;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/CoroutineExceptionHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;-><init>(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/tile_matching/domain/usecases/b;Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;Lorg/xbet/tile_matching/domain/usecases/d;Lorg/xbet/tile_matching/domain/usecases/a;Lorg/xbet/core/domain/usecases/game_state/h;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/tile_matching/domain/usecases/e;Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;Lm8/a;LTv/e;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000!\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u00012\u00020\u0002J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "org/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g",
        "Lkotlin/coroutines/a;",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "Lkotlin/coroutines/CoroutineContext;",
        "context",
        "",
        "exception",
        "",
        "handleException",
        "(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V",
        "kotlinx-coroutines-core"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V
    .locals 0

    .line 1
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g;->b:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    invoke-direct {p0, p1}, Lkotlin/coroutines/a;-><init>(Lkotlin/coroutines/CoroutineContext$b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g;->b:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    invoke-static {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->J3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
