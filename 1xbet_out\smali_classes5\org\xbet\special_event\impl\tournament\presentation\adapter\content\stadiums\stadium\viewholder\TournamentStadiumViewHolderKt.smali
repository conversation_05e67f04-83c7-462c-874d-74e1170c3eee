.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0008\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a#\u0010\u000c\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a#\u0010\u000e\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\r\u001a#\u0010\u000f\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\r\u001a#\u0010\u0010\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\r\u001a#\u0010\u0011\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\r*$\u0008\u0000\u0010\u0012\"\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00072\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007\u00a8\u0006\u0013"
    }
    d2 = {
        "Ltx0/a;",
        "tournamentStadiumClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "o",
        "(Ltx0/a;)LA4/c;",
        "LB4/a;",
        "Lux0/a;",
        "LGq0/f1;",
        "Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolder;",
        "",
        "n",
        "(LB4/a;)V",
        "k",
        "m",
        "j",
        "l",
        "TournamentStadiumViewHolder",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/f1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->p(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/f1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->s(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ltx0/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->q(Ltx0/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Ltx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->r(Ltx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic e(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->j(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->k(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->l(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->m(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->n(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final j(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lux0/a;",
            "LGq0/f1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/f1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/f1;->b:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lux0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lux0/a;->d()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final k(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lux0/a;",
            "LGq0/f1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/f1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/f1;->c:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lux0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lux0/a;->e()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final l(LB4/a;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lux0/a;",
            "LGq0/f1;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    check-cast v1, LGq0/f1;

    .line 8
    .line 9
    iget-object v1, v1, LGq0/f1;->d:Landroid/widget/ImageView;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, Lux0/a;

    .line 16
    .line 17
    invoke-virtual {v2}, Lux0/a;->f()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    check-cast p0, Lux0/a;

    .line 26
    .line 27
    invoke-virtual {p0}, Lux0/a;->o()I

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    const/4 p0, 0x0

    .line 32
    new-array v6, p0, [LYW0/d;

    .line 33
    .line 34
    const/16 v10, 0xec

    .line 35
    .line 36
    const/4 v11, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v5, 0x0

    .line 39
    const/4 v7, 0x0

    .line 40
    const/4 v8, 0x0

    .line 41
    const/4 v9, 0x0

    .line 42
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public static final m(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lux0/a;",
            "LGq0/f1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/f1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/f1;->e:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lux0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lux0/a;->j()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final n(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lux0/a;",
            "LGq0/f1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/f1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/f1;->f:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lux0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lux0/a;->s()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final o(Ltx0/a;)LA4/c;
    .locals 4
    .param p0    # Ltx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ltx0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lvx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lvx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lvx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lvx0/b;-><init>(Ltx0/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$tournamentStadiumAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$tournamentStadiumAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$tournamentStadiumAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$tournamentStadiumAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final p(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/f1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/f1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/f1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final q(Ltx0/a;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/f1;

    .line 6
    .line 7
    invoke-virtual {v0}, LGq0/f1;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, Lvx0/c;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, Lvx0/c;-><init>(Ltx0/a;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;

    .line 22
    .line 23
    invoke-direct {p0, p1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;-><init>(LB4/a;LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    new-instance p0, Lvx0/d;

    .line 30
    .line 31
    invoke-direct {p0, p1}, Lvx0/d;-><init>(LB4/a;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p1, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 35
    .line 36
    .line 37
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 38
    .line 39
    return-object p0
.end method

.method public static final r(Ltx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lux0/a;

    .line 6
    .line 7
    invoke-virtual {p2}, Lux0/a;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, Lux0/a;

    .line 16
    .line 17
    invoke-virtual {p1}, Lux0/a;->s()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-interface {p0, v0, v1, p1}, Ltx0/a;->b(JLjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final s(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, LGq0/f1;

    .line 8
    .line 9
    iget-object p0, p0, LGq0/f1;->d:Landroid/widget/ImageView;

    .line 10
    .line 11
    invoke-virtual {v0, p0}, LCX0/l;->j(Landroid/widget/ImageView;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
