.class public final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a;,
        Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;,
        Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;,
        Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009e\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u00087\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001:\u0006\u00ba\u0001\u00bb\u0001\u00bc\u0001B\u00cb\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0008\u0008\u0001\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u00a2\u0006\u0004\u00082\u00103J\u001d\u00108\u001a\u0002072\u000c\u00106\u001a\u0008\u0012\u0004\u0012\u00020504H\u0002\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010<\u001a\u0002072\u0006\u0010;\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008<\u0010=J\u000f\u0010>\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008>\u0010?J\u000f\u0010@\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008@\u0010?J\u000f\u0010A\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008A\u0010?J\u000f\u0010B\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008B\u0010?J\u0010\u0010D\u001a\u00020CH\u0082@\u00a2\u0006\u0004\u0008D\u0010EJ\u001e\u0010G\u001a\u0002072\u000c\u0010F\u001a\u0008\u0012\u0004\u0012\u00020:04H\u0082@\u00a2\u0006\u0004\u0008G\u0010HJ\u001f\u0010L\u001a\u0002072\u0006\u0010J\u001a\u00020I2\u0006\u0010K\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008L\u0010MJ\u0017\u0010P\u001a\u0002072\u0006\u0010O\u001a\u00020NH\u0002\u00a2\u0006\u0004\u0008P\u0010QJ#\u0010T\u001a\u0008\u0012\u0004\u0012\u00020R042\u000c\u0010S\u001a\u0008\u0012\u0004\u0012\u00020R04H\u0002\u00a2\u0006\u0004\u0008T\u0010UJ\u000f\u0010V\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008V\u0010?J\u0013\u0010X\u001a\u000207*\u00020WH\u0002\u00a2\u0006\u0004\u0008X\u0010YJ\u000f\u0010Z\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008Z\u0010?J\u0015\u0010]\u001a\u0008\u0012\u0004\u0012\u00020\\0[H\u0000\u00a2\u0006\u0004\u0008]\u0010^J\u0015\u0010_\u001a\u0008\u0012\u0004\u0012\u00020W0[H\u0000\u00a2\u0006\u0004\u0008_\u0010^J\u0015\u0010a\u001a\u0008\u0012\u0004\u0012\u00020`0[H\u0000\u00a2\u0006\u0004\u0008a\u0010^J\u0015\u0010c\u001a\u0008\u0012\u0004\u0012\u00020b0[H\u0000\u00a2\u0006\u0004\u0008c\u0010^J\u000f\u0010d\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008d\u0010?J\u000f\u0010e\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008e\u0010?J\u0017\u0010f\u001a\u0002072\u0006\u0010J\u001a\u00020IH\u0000\u00a2\u0006\u0004\u0008f\u0010gJ\u000f\u0010h\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008h\u0010?J\u000f\u0010i\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008i\u0010?J\u0017\u0010l\u001a\u0002072\u0006\u0010k\u001a\u00020jH\u0000\u00a2\u0006\u0004\u0008l\u0010mJ\u001f\u0010n\u001a\u0002072\u0006\u0010J\u001a\u00020I2\u0006\u0010;\u001a\u00020:H\u0000\u00a2\u0006\u0004\u0008n\u0010oJ\u000f\u0010p\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008p\u0010?R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0018\u0010%\u001a\u00020$8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0001\u0010\u009a\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0001\u0010\u009e\u0001R\u0019\u0010\u00a1\u0001\u001a\u00020C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u001c\u0010\u00a5\u0001\u001a\u0005\u0018\u00010\u00a2\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u001c\u0010\u00a7\u0001\u001a\u0005\u0018\u00010\u00a2\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a4\u0001R\u0019\u0010\u00a9\u0001\u001a\u00020C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a0\u0001R\u0019\u0010\u00ab\u0001\u001a\u00020C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00a0\u0001R\u001e\u0010\u00af\u0001\u001a\t\u0012\u0004\u0012\u00020\\0\u00ac\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0001\u0010\u00ae\u0001R\u001e\u0010\u00b3\u0001\u001a\t\u0012\u0004\u0012\u00020`0\u00b0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00b2\u0001R\u001e\u0010\u00b5\u0001\u001a\t\u0012\u0004\u0012\u00020W0\u00b0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0001\u0010\u00b2\u0001R\u0018\u0010\u00b9\u0001\u001a\u00030\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00b8\u0001\u00a8\u0006\u00bd\u0001"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LDg/c;",
        "oneXGamesAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "LwX0/a;",
        "appScreensProvider",
        "LJT/c;",
        "addOneXGameLastActionUseCase",
        "LwX0/c;",
        "router",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;",
        "getPromoItemsUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "getGameMetaUseCase",
        "Lw30/k;",
        "getGameWorkStatusUseCase",
        "Lw30/s;",
        "getWorkStatusDelayUseCase",
        "Lw30/q;",
        "getGpResultScenario",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lm8/a;",
        "dispatchers",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/games_section/api/models/OneXGamesPromoType;",
        "promoType",
        "Lak/a;",
        "balanceFeature",
        "LUR/a;",
        "promoFatmanLogger",
        "LAR/a;",
        "depositFatmanLogger",
        "LHX0/e;",
        "resourceManager",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "LA7/a;",
        "getCommonConfigUseCase",
        "<init>",
        "(LDg/c;Lorg/xbet/analytics/domain/scope/I;LwX0/a;LJT/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lw30/k;Lw30/s;Lw30/q;LxX0/a;Lm8/a;Lcom/xbet/onexuser/domain/user/c;LSX0/a;Lorg/xbet/games_section/api/models/OneXGamesPromoType;Lak/a;LUR/a;LAR/a;LHX0/e;Lgk0/a;LA7/a;)V",
        "",
        "",
        "gameIdList",
        "",
        "x4",
        "(Ljava/util/List;)V",
        "Lq50/a;",
        "promoItem",
        "q4",
        "(Lq50/a;)V",
        "v4",
        "()V",
        "h4",
        "Y3",
        "a4",
        "",
        "j4",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "promoUiModelList",
        "g4",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "screenName",
        "item",
        "Z3",
        "(Ljava/lang/String;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V",
        "Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;",
        "type",
        "i4",
        "(Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;)V",
        "LTv/f;",
        "list",
        "W3",
        "(Ljava/util/List;)Ljava/util/List;",
        "u4",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
        "s4",
        "(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V",
        "k4",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a;",
        "d4",
        "()Lkotlinx/coroutines/flow/e;",
        "e4",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;",
        "f4",
        "",
        "V3",
        "X3",
        "w4",
        "o4",
        "(Ljava/lang/String;)V",
        "n4",
        "m4",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "r4",
        "(Lorg/xbet/balance/model/BalanceModel;)V",
        "p4",
        "(Ljava/lang/String;Lq50/a;)V",
        "l4",
        "v1",
        "LDg/c;",
        "x1",
        "Lorg/xbet/analytics/domain/scope/I;",
        "y1",
        "LwX0/a;",
        "F1",
        "LJT/c;",
        "H1",
        "LwX0/c;",
        "I1",
        "Lorg/xbet/ui_common/utils/M;",
        "P1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "S1",
        "Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;",
        "V1",
        "Li8/j;",
        "b2",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "v2",
        "Lw30/k;",
        "x2",
        "Lw30/s;",
        "y2",
        "Lw30/q;",
        "F2",
        "LxX0/a;",
        "H2",
        "Lm8/a;",
        "I2",
        "Lcom/xbet/onexuser/domain/user/c;",
        "P2",
        "LSX0/a;",
        "S2",
        "Lorg/xbet/games_section/api/models/OneXGamesPromoType;",
        "V2",
        "Lak/a;",
        "X2",
        "LUR/a;",
        "F3",
        "LAR/a;",
        "H3",
        "LHX0/e;",
        "I3",
        "Lgk0/a;",
        "S3",
        "Z",
        "showCachedData",
        "Lkotlinx/coroutines/x0;",
        "H4",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "X4",
        "updateWorkStatusJob",
        "v5",
        "lastConnection",
        "w5",
        "promoItemsLoaded",
        "Lkotlinx/coroutines/flow/U;",
        "x5",
        "Lkotlinx/coroutines/flow/U;",
        "singleEvent",
        "Lkotlinx/coroutines/flow/V;",
        "y5",
        "Lkotlinx/coroutines/flow/V;",
        "viewState",
        "z5",
        "state",
        "Lv7/b;",
        "A5",
        "Lv7/b;",
        "commonConfig",
        "c",
        "b",
        "a",
        "promo_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final A5:Lv7/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:LJT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:LAR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H4:Lkotlinx/coroutines/x0;

.field public final I1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lgk0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S2:Lorg/xbet/games_section/api/models/OneXGamesPromoType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S3:Z

.field public final V1:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:LUR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public X4:Lkotlinx/coroutines/x0;

.field public final b2:Lorg/xbet/core/domain/usecases/game_info/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LDg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lw30/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v5:Z

.field public w5:Z

.field public final x1:Lorg/xbet/analytics/domain/scope/I;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lw30/s;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lw30/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDg/c;Lorg/xbet/analytics/domain/scope/I;LwX0/a;LJT/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lw30/k;Lw30/s;Lw30/q;LxX0/a;Lm8/a;Lcom/xbet/onexuser/domain/user/c;LSX0/a;Lorg/xbet/games_section/api/models/OneXGamesPromoType;Lak/a;LUR/a;LAR/a;LHX0/e;Lgk0/a;LA7/a;)V
    .locals 0
    .param p1    # LDg/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LJT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/core/domain/usecases/game_info/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lw30/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lw30/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lw30/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/games_section/api/models/OneXGamesPromoType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LUR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LA7/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v1:LDg/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x1:Lorg/xbet/analytics/domain/scope/I;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F1:LJT/c;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S1:Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V1:Li8/j;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->b2:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v2:Lw30/k;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x2:Lw30/s;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y2:Lw30/q;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F2:LxX0/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I2:Lcom/xbet/onexuser/domain/user/c;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->P2:LSX0/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S2:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V2:Lak/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X2:LUR/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F3:LAR/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H3:LHX0/e;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I3:Lgk0/a;

    .line 65
    .line 66
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 71
    .line 72
    new-instance p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;

    .line 73
    .line 74
    const/4 p2, 0x1

    .line 75
    const/4 p3, 0x0

    .line 76
    const/4 p4, 0x0

    .line 77
    invoke-direct {p1, p4, p2, p3}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;-><init>(ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 78
    .line 79
    .line 80
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 85
    .line 86
    sget-object p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$a;->a:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$a;

    .line 87
    .line 88
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 93
    .line 94
    invoke-virtual/range {p24 .. p24}, LA7/a;->a()Lv7/b;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->A5:Lv7/b;

    .line 99
    .line 100
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lw30/k;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v2:Lw30/k;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lw30/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y2:Lw30/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S1:Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic E3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->a4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic G3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/games_section/api/models/OneXGamesPromoType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S2:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H3:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S3:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic K3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->g4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->h4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->j4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lq50/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->q4(Lq50/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->s4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->u4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic R3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic S3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S2:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic T3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S3:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic U3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x4(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final Y3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;

    .line 9
    .line 10
    iget-object v3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I2:Lcom/xbet/onexuser/domain/user/c;

    .line 11
    .line 12
    invoke-virtual {v3}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 13
    .line 14
    .line 15
    move-result v3

    .line 16
    invoke-virtual {v2, v3}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;->a(Z)Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-eqz v1, :cond_0

    .line 25
    .line 26
    return-void
.end method

.method public static final b4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/m;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/promo/presentation/m;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    const/4 p1, 0x0

    .line 12
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w5:Z

    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final c4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->u4()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final h4()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->a4()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w4()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->Y3()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->t4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->c4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->b4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->z4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private static final t4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->W3(Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final u4()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->P2:LSX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v7, 0x1c

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$b;

    .line 19
    .line 20
    invoke-direct {v1, v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->s4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final synthetic v3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LJT/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F1:LJT/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private final v4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H4:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$subscribeToConnectionState$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$subscribeToConnectionState$1;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$subscribeToConnectionState$2;

    .line 30
    .line 31
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$subscribeToConnectionState$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H4:Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    return-void
.end method

.method public static final synthetic w3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lak/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V2:Lak/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LxX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F2:LxX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final x4(Ljava/util/List;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x2:Lw30/s;

    .line 18
    .line 19
    invoke-interface {v0}, Lw30/s;->invoke()J

    .line 20
    .line 21
    .line 22
    move-result-wide v3

    .line 23
    sget-object v5, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 26
    .line 27
    invoke-interface {v0}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 28
    .line 29
    .line 30
    move-result-object v6

    .line 31
    new-instance v7, Lorg/xbet/games_section/feature/promo/presentation/k;

    .line 32
    .line 33
    invoke-direct {v7, p0}, Lorg/xbet/games_section/feature/promo/presentation/k;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V

    .line 34
    .line 35
    .line 36
    new-instance v8, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateGamesWorkStatus$2;

    .line 37
    .line 38
    const/4 v0, 0x0

    .line 39
    invoke-direct {v8, p0, p1, v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateGamesWorkStatus$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)V

    .line 40
    .line 41
    .line 42
    const/16 v10, 0x20

    .line 43
    .line 44
    const/4 v11, 0x0

    .line 45
    const/4 v9, 0x0

    .line 46
    invoke-static/range {v2 .. v11}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->P(Lkotlinx/coroutines/N;JLjava/util/concurrent/TimeUnit;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    return-void
.end method

.method public static final synthetic y3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/ui_common/utils/M;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final y4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/n;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/games_section/feature/promo/presentation/n;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lgk0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I3:Lgk0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final z4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final V3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I3:Lgk0/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lgk0/a;->invoke()Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lp50/a;->a(Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0
.end method

.method public final W3(Ljava/util/List;)Ljava/util/List;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LTv/f;",
            ">;)",
            "Ljava/util/List<",
            "LTv/f;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    return-object p1

    .line 14
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_2

    .line 23
    .line 24
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    check-cast v1, LTv/f;

    .line 29
    .line 30
    invoke-virtual {v1}, LTv/f;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    sget-object v2, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->BONUS:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 35
    .line 36
    if-ne v1, v2, :cond_1

    .line 37
    .line 38
    new-instance v0, LTv/f;

    .line 39
    .line 40
    new-instance v1, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;

    .line 41
    .line 42
    sget-object v7, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->BONUS_INFO:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 43
    .line 44
    invoke-virtual {v7}, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->getId()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    iget-object v3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H3:LHX0/e;

    .line 49
    .line 50
    sget v4, Lpb/k;->bonuses_info_name:I

    .line 51
    .line 52
    const/4 v5, 0x0

    .line 53
    new-array v6, v5, [Ljava/lang/Object;

    .line 54
    .line 55
    invoke-interface {v3, v4, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    iget-object v4, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H3:LHX0/e;

    .line 60
    .line 61
    sget v6, Lpb/k;->bonuses_info_description:I

    .line 62
    .line 63
    new-array v5, v5, [Ljava/lang/Object;

    .line 64
    .line 65
    invoke-interface {v4, v6, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v4

    .line 69
    const/4 v5, 0x0

    .line 70
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object v6

    .line 74
    invoke-direct/range {v1 .. v6}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;-><init>(ILjava/lang/String;Ljava/lang/String;ZLjava/util/List;)V

    .line 75
    .line 76
    .line 77
    invoke-direct {v0, v1, v7}, LTv/f;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    .line 78
    .line 79
    .line 80
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->a1(Ljava/util/Collection;Ljava/lang/Object;)Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    :cond_2
    return-object p1
.end method

.method public final X3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a$a;->a:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final Z3(Ljava/lang/String;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$d;->a:[I

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    :pswitch_0
    return-void

    .line 13
    :pswitch_1
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_INFO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :pswitch_2
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :pswitch_3
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :pswitch_4
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_JACKPOT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :pswitch_5
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_TOURNAMENT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :pswitch_6
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_QUEST_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 29
    .line 30
    goto :goto_0

    .line 31
    :pswitch_7
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 32
    .line 33
    :goto_0
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->i4(Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X2:LUR/a;

    .line 37
    .line 38
    invoke-static {p2}, Lp50/c;->a(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)Lorg/xbet/fatmananalytics/api/logger/promo/models/PromoSectionType;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    invoke-interface {v0, p1, p2}, LUR/a;->d(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/logger/promo/models/PromoSectionType;)V

    .line 43
    .line 44
    .line 45
    return-void

    .line 46
    nop

    .line 47
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_0
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public final a4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/j;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/promo/presentation/j;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final d4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lq50/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 54
    .line 55
    invoke-interface {p2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    new-instance v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;

    .line 60
    .line 61
    const/4 v4, 0x0

    .line 62
    invoke-direct {v2, p0, p1, v4}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)V

    .line 63
    .line 64
    .line 65
    iput v3, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$1;->label:I

    .line 66
    .line 67
    invoke-static {p2, v2, v0}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    if-ne p1, v1, :cond_3

    .line 72
    .line 73
    return-object v1

    .line 74
    :cond_3
    :goto_1
    iput-boolean v3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w5:Z

    .line 75
    .line 76
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 77
    .line 78
    return-object p1
.end method

.method public final i4(Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;)V
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$d;->b:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :pswitch_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v1:LDg/c;

    .line 14
    .line 15
    invoke-virtual {v0, p1}, LDg/c;->v(Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public final j4(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V1:Li8/j;

    .line 54
    .line 55
    invoke-interface {p1}, Li8/j;->invoke()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object v2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->b2:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 60
    .line 61
    sget-object v4, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 62
    .line 63
    invoke-virtual {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v4

    .line 67
    iput v3, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$luckyWheelEnabled$1;->label:I

    .line 68
    .line 69
    invoke-virtual {v2, v4, v5, p1, v0}, Lorg/xbet/core/domain/usecases/game_info/n;->a(JLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    if-ne p1, v1, :cond_3

    .line 74
    .line 75
    return-object v1

    .line 76
    :cond_3
    :goto_1
    check-cast p1, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 77
    .line 78
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getEnable()Z

    .line 79
    .line 80
    .line 81
    move-result p1

    .line 82
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    return-object p1
.end method

.method public final k4()V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S3:Z

    .line 3
    .line 4
    return-void
.end method

.method public final l4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final m4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H4:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final n4()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->v4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final o4(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x1:Lorg/xbet/analytics/domain/scope/I;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->OneXPromo:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/I;->d(Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->F3:LAR/a;

    .line 9
    .line 10
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->ONE_X_PROMO:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 11
    .line 12
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-interface {v0, p1, v1}, LAR/a;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 24
    .line 25
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    new-instance v3, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$1;

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 32
    .line 33
    invoke-direct {v3, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$1;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    new-instance v7, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;

    .line 37
    .line 38
    const/4 p1, 0x0

    .line 39
    invoke-direct {v7, p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 40
    .line 41
    .line 42
    const/16 v8, 0xa

    .line 43
    .line 44
    const/4 v9, 0x0

    .line 45
    const/4 v4, 0x0

    .line 46
    const/4 v6, 0x0

    .line 47
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public final p4(Ljava/lang/String;Lq50/a;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lq50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->q4(Lq50/a;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p2}, Lq50/a;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 5
    .line 6
    .line 7
    move-result-object p2

    .line 8
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->Z3(Ljava/lang/String;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final q4(Lq50/a;)V
    .locals 9

    .line 1
    invoke-virtual {p1}, Lq50/a;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$d;->a:[I

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    aget v0, v1, v0

    .line 12
    .line 13
    packed-switch v0, :pswitch_data_0

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :pswitch_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 20
    .line 21
    invoke-interface {v0}, LwX0/a;->E()Lq4/q;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :pswitch_1
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 32
    .line 33
    invoke-interface {v0}, LwX0/a;->m()Lq4/q;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :pswitch_2
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 46
    .line 47
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    new-instance v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$promoScreenSpecified$1;

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 54
    .line 55
    invoke-direct {v2, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$promoScreenSpecified$1;-><init>(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    new-instance v6, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$promoScreenSpecified$2;

    .line 59
    .line 60
    const/4 p1, 0x0

    .line 61
    invoke-direct {v6, p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$promoScreenSpecified$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 62
    .line 63
    .line 64
    const/16 v7, 0xa

    .line 65
    .line 66
    const/4 v8, 0x0

    .line 67
    const/4 v3, 0x0

    .line 68
    const/4 v5, 0x0

    .line 69
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :pswitch_3
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 76
    .line 77
    invoke-interface {v0}, LwX0/a;->u()Lq4/q;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 82
    .line 83
    .line 84
    return-void

    .line 85
    :pswitch_4
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 86
    .line 87
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 88
    .line 89
    invoke-virtual {p1}, Lq50/a;->a()Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getName()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iget-object v2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->A5:Lv7/b;

    .line 98
    .line 99
    invoke-virtual {v2}, Lv7/b;->T()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v2

    .line 103
    iget-object v3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->A5:Lv7/b;

    .line 104
    .line 105
    invoke-virtual {v3}, Lv7/b;->U()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v3

    .line 109
    const/4 v4, 0x1

    .line 110
    invoke-interface {v1, v4, p1, v2, v3}, LwX0/a;->G(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lq4/q;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 115
    .line 116
    .line 117
    return-void

    .line 118
    :pswitch_5
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 119
    .line 120
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 121
    .line 122
    invoke-interface {v0}, LwX0/a;->H()Lq4/q;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :pswitch_6
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H1:LwX0/c;

    .line 131
    .line 132
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->y1:LwX0/a;

    .line 133
    .line 134
    invoke-interface {v0}, LwX0/a;->o()Lq4/q;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 139
    .line 140
    .line 141
    return-void

    .line 142
    nop

    .line 143
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final r4(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 4
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V2:Lak/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lak/a;->p()Lfk/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 8
    .line 9
    invoke-interface {v0, v1, p1}, Lfk/b;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V2:Lak/a;

    .line 13
    .line 14
    invoke-interface {v0}, Lak/a;->J()Lfk/u;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 19
    .line 20
    .line 21
    move-result-wide v2

    .line 22
    invoke-interface {v0, v2, v3, v1}, Lfk/u;->a(JLorg/xbet/balance/model/BalanceScreenType;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w4()V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final s4(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/l;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xbet/games_section/feature/promo/presentation/l;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$send$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$send$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final w4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->H2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateBalance$1;

    .line 12
    .line 13
    iget-object v2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I1:Lorg/xbet/ui_common/utils/M;

    .line 14
    .line 15
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateBalance$1;-><init>(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    new-instance v5, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateBalance$2;

    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$updateBalance$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/16 v6, 0xa

    .line 25
    .line 26
    const/4 v7, 0x0

    .line 27
    const/4 v4, 0x0

    .line 28
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method
