.class public final synthetic LL21/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL21/h;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LL21/h;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;->g(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentInactive;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
