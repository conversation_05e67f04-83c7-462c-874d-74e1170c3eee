.class public final LuY0/b$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LuY0/a;
.implements LIY0/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LuY0/b;->a(Landroid/graphics/Canvas;ILIY0/d;LRY0/a;LtY0/a;Landroid/graphics/RectF;FLorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;)LuY0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0005\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u000b*\u0001\u0000\u0008\n\u0018\u00002\u00020\u00012\u00020\u0002J\u000f\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0018\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0003H\u0096\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J \u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\tH\u0096\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0018\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\n\u001a\u00020\tH\u0096\u0001\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J$\u0010\u0013\u001a\u00028\u0000\"\n\u0008\u0000\u0010\u0012*\u0004\u0018\u00010\t2\u0006\u0010\n\u001a\u00020\tH\u0096\u0001\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J$\u0010\u0015\u001a\u00028\u0000\"\n\u0008\u0000\u0010\u0012*\u0004\u0018\u00010\t2\u0006\u0010\n\u001a\u00020\tH\u0096\u0001\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J \u0010\u0016\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\tH\u0096\u0003\u00a2\u0006\u0004\u0008\u0016\u0010\u000eJ$\u0010\u0017\u001a\u00028\u0000\"\n\u0008\u0000\u0010\u0012*\u0004\u0018\u00010\t2\u0006\u0010\n\u001a\u00020\tH\u0096\u0003\u00a2\u0006\u0004\u0008\u0017\u0010\u0014R\u001a\u0010\u001c\u001a\u00020\u00188\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u0019\u0010\u001bR\"\u0010#\u001a\u00020\u001d8\u0016@\u0016X\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008\u001e\u0010 \"\u0004\u0008!\u0010\"R\u001a\u0010)\u001a\u00020$8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001c\u0010/\u001a\u0004\u0018\u00010*8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001a\u00102\u001a\u00020\u00038\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0015\u00100\u001a\u0004\u00081\u0010\u0005R\u001a\u00108\u001a\u0002038\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107R\u001a\u00109\u001a\u00020\u00038\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0013\u00100\u001a\u0004\u00084\u0010\u0005R\u0014\u0010:\u001a\u00020\u00188\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008\u0012\u0010\u001bR\u0014\u0010>\u001a\u00020;8\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008<\u0010=R\u0014\u0010@\u001a\u00020\u00038\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008?\u0010\u0005R\u0014\u0010B\u001a\u00020\u00038\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008A\u0010\u0005R\u0014\u0010E\u001a\u00020\u000f8\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008C\u0010DR\u0014\u0010G\u001a\u00020\u000f8\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008F\u0010DR\u0014\u0010K\u001a\u00020H8\u0016X\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008I\u0010JR\u0014\u0010M\u001a\u00020\u00038VX\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008L\u0010\u0005R\u0018\u0010O\u001a\u00020\u0003*\u00020\u00038VX\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008N\u0010\u0008R\u0018\u0010R\u001a\u00020$*\u00020\u00038VX\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\u0008P\u0010Q\u00a8\u0006S"
    }
    d2 = {
        "uY0/b$a",
        "LuY0/a;",
        "LIY0/d;",
        "",
        "l",
        "()F",
        "dp",
        "Q",
        "(F)F",
        "",
        "key",
        "value",
        "",
        "a",
        "(Ljava/lang/Object;Ljava/lang/Object;)V",
        "",
        "k",
        "(Ljava/lang/Object;)Z",
        "T",
        "h",
        "(Ljava/lang/Object;)Ljava/lang/Object;",
        "f",
        "set",
        "get",
        "Landroid/graphics/RectF;",
        "b",
        "Landroid/graphics/RectF;",
        "()Landroid/graphics/RectF;",
        "chartBounds",
        "Landroid/graphics/Canvas;",
        "c",
        "Landroid/graphics/Canvas;",
        "()Landroid/graphics/Canvas;",
        "setCanvas",
        "(Landroid/graphics/Canvas;)V",
        "canvas",
        "",
        "d",
        "I",
        "i",
        "()I",
        "elevationOverlayColor",
        "LRY0/a;",
        "e",
        "LRY0/a;",
        "getMarkerTouchPoint-FMb8k-c",
        "()LRY0/a;",
        "markerTouchPoint",
        "F",
        "P",
        "chartScale",
        "LtY0/a;",
        "g",
        "LtY0/a;",
        "j",
        "()LtY0/a;",
        "horizontalDimensions",
        "horizontalScroll",
        "canvasBounds",
        "LyY0/c;",
        "U",
        "()LyY0/c;",
        "chartValuesManager",
        "getDensity",
        "density",
        "M",
        "fontScale",
        "S",
        "()Z",
        "isLtr",
        "R",
        "isHorizontalScrollEnabled",
        "LxY0/a;",
        "V",
        "()LxY0/a;",
        "horizontalLayout",
        "O",
        "layoutDirectionMultiplier",
        "N",
        "pixels",
        "W",
        "(F)I",
        "wholePixels",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LIY0/d;

.field public final b:Landroid/graphics/RectF;

.field public c:Landroid/graphics/Canvas;

.field public final d:I

.field public final e:LRY0/a;

.field public final f:F

.field public final g:LtY0/a;

.field public final h:F

.field public final synthetic i:LIY0/d;

.field public final synthetic j:Landroid/graphics/RectF;

.field public final synthetic k:LtY0/a;

.field public final synthetic l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;


# direct methods
.method public constructor <init>(LIY0/d;Landroid/graphics/RectF;Landroid/graphics/Canvas;ILRY0/a;LtY0/a;FLorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;)V
    .locals 0

    .line 1
    iput-object p1, p0, LuY0/b$a;->i:LIY0/d;

    .line 2
    .line 3
    iput-object p2, p0, LuY0/b$a;->j:Landroid/graphics/RectF;

    .line 4
    .line 5
    iput-object p6, p0, LuY0/b$a;->k:LtY0/a;

    .line 6
    .line 7
    iput-object p8, p0, LuY0/b$a;->l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;

    .line 8
    .line 9
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 10
    .line 11
    .line 12
    iput-object p1, p0, LuY0/b$a;->a:LIY0/d;

    .line 13
    .line 14
    iput-object p2, p0, LuY0/b$a;->b:Landroid/graphics/RectF;

    .line 15
    .line 16
    iput-object p3, p0, LuY0/b$a;->c:Landroid/graphics/Canvas;

    .line 17
    .line 18
    iput p4, p0, LuY0/b$a;->d:I

    .line 19
    .line 20
    iput-object p5, p0, LuY0/b$a;->e:LRY0/a;

    .line 21
    .line 22
    invoke-virtual {p0}, LuY0/b$a;->l()F

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    iput p1, p0, LuY0/b$a;->f:F

    .line 27
    .line 28
    invoke-virtual {p0}, LuY0/b$a;->P()F

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    invoke-interface {p6, p1}, LtY0/a;->c(F)LtY0/a;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LuY0/b$a;->g:LtY0/a;

    .line 37
    .line 38
    iput p7, p0, LuY0/b$a;->h:F

    .line 39
    .line 40
    return-void
.end method


# virtual methods
.method public M()F
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->M()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public N(F)F
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/d;->N(F)F

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public O()F
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->O()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public P()F
    .locals 1

    .line 1
    iget v0, p0, LuY0/b$a;->f:F

    .line 2
    .line 3
    return v0
.end method

.method public Q(F)F
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/d;->Q(F)F

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public R()Z
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->R()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public S()Z
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->S()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public T()Landroid/graphics/RectF;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->T()Landroid/graphics/RectF;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public U()LyY0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->U()LyY0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public V()LxY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->V()LxY0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public W(F)I
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/d;->W(F)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public a(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, LIY0/c;->a(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b()Landroid/graphics/RectF;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->b:Landroid/graphics/RectF;

    .line 2
    .line 3
    return-object v0
.end method

.method public c()Landroid/graphics/Canvas;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->c:Landroid/graphics/Canvas;

    .line 2
    .line 3
    return-object v0
.end method

.method public d(I)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, LuY0/a$a;->a(LuY0/a;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public e(FFFF)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LuY0/a$a;->b(LuY0/a;FFFF)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public f(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/c;->f(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public g()F
    .locals 1

    .line 1
    iget v0, p0, LuY0/b$a;->h:F

    .line 2
    .line 3
    return v0
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/c;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public getDensity()F
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0}, LIY0/d;->getDensity()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public h(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/c;->h(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public i()I
    .locals 1

    .line 1
    iget v0, p0, LuY0/b$a;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public j()LtY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->g:LtY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public k(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LIY0/c;->k(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public final l()F
    .locals 5

    .line 1
    iget-object v0, p0, LuY0/b$a;->k:LtY0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LuY0/b$a;->U()LyY0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LyY0/c;->a()LyY0/b;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-interface {v1}, LyY0/b;->c()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-interface {v0, v1}, LtY0/a;->d(I)F

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    iget-object v1, p0, LuY0/b$a;->j:Landroid/graphics/RectF;

    .line 20
    .line 21
    invoke-virtual {v1}, Landroid/graphics/RectF;->width()F

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    const/4 v2, 0x0

    .line 26
    const/4 v3, 0x1

    .line 27
    cmpg-float v1, v0, v1

    .line 28
    .line 29
    if-gez v1, :cond_0

    .line 30
    .line 31
    iget-object v1, p0, LuY0/b$a;->l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;

    .line 32
    .line 33
    sget-object v4, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;->None:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/chart/scale/AutoScaleUp;

    .line 34
    .line 35
    if-ne v1, v4, :cond_0

    .line 36
    .line 37
    const/4 v1, 0x1

    .line 38
    goto :goto_0

    .line 39
    :cond_0
    const/4 v1, 0x0

    .line 40
    :goto_0
    invoke-virtual {p0}, LuY0/b$a;->R()Z

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    if-eqz v4, :cond_1

    .line 45
    .line 46
    iget-object v4, p0, LuY0/b$a;->j:Landroid/graphics/RectF;

    .line 47
    .line 48
    invoke-virtual {v4}, Landroid/graphics/RectF;->width()F

    .line 49
    .line 50
    .line 51
    move-result v4

    .line 52
    cmpl-float v4, v0, v4

    .line 53
    .line 54
    if-ltz v4, :cond_1

    .line 55
    .line 56
    const/4 v2, 0x1

    .line 57
    :cond_1
    if-nez v1, :cond_3

    .line 58
    .line 59
    if-eqz v2, :cond_2

    .line 60
    .line 61
    goto :goto_1

    .line 62
    :cond_2
    iget-object v1, p0, LuY0/b$a;->j:Landroid/graphics/RectF;

    .line 63
    .line 64
    invoke-virtual {v1}, Landroid/graphics/RectF;->width()F

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    div-float/2addr v1, v0

    .line 69
    return v1

    .line 70
    :cond_3
    :goto_1
    iget-object v0, p0, LuY0/b$a;->i:LIY0/d;

    .line 71
    .line 72
    invoke-interface {v0}, LIY0/d;->P()F

    .line 73
    .line 74
    .line 75
    move-result v0

    .line 76
    return v0
.end method

.method public set(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 1
    iget-object v0, p0, LuY0/b$a;->a:LIY0/d;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, LIY0/c;->set(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
