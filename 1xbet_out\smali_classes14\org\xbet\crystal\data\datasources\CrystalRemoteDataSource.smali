.class public final Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010$\n\u0002\u0010 \n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J2\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0008\u0010\u000b\u001a\u0004\u0018\u00010\n2\u0006\u0010\r\u001a\u00020\u000cH\u0086@\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J.\u0010\u0013\u001a\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u000c\u0012\u000c\u0012\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00120\u00112\u0006\u0010\r\u001a\u00020\u000cH\u0086@\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R\u001a\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001c"
    }
    d2 = {
        "Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lf8/g;)V",
        "",
        "betSum",
        "",
        "activeId",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "bonus",
        "",
        "token",
        "LVx/a;",
        "d",
        "(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "",
        "c",
        "(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "Lf8/g;",
        "Lkotlin/Function0;",
        "LTx/a;",
        "b",
        "Lkotlin/jvm/functions/Function0;",
        "crystalApi",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "LTx/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->a:Lf8/g;

    .line 5
    .line 6
    new-instance p1, Lorg/xbet/crystal/data/datasources/b;

    .line 7
    .line 8
    invoke-direct {p1, p0}, Lorg/xbet/crystal/data/datasources/b;-><init>(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;)V

    .line 9
    .line 10
    .line 11
    iput-object p1, p0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 12
    .line 13
    return-void
.end method

.method public static synthetic a(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;)LTx/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->b(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;)LTx/a;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;)LTx/a;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->a:Lf8/g;

    .line 2
    .line 3
    const-class v0, LTx/a;

    .line 4
    .line 5
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LTx/a;

    .line 14
    .line 15
    return-object p0
.end method


# virtual methods
.method public final c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/util/List<",
            "Ljava/lang/Double;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;-><init>(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p2, p0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    check-cast p2, LTx/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$getCoeffs$1;->label:I

    .line 62
    .line 63
    invoke-interface {p2, p1, v0}, LTx/a;->a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    if-ne p2, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p2, Lorg/xbet/core/data/j;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final d(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 14
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(DJ",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LVx/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p7

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;

    .line 23
    .line 24
    invoke-direct {v1, p0, v0}, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;-><init>(Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object v0, v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    iget v3, v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;->label:I

    .line 34
    .line 35
    const/4 v4, 0x1

    .line 36
    if-eqz v3, :cond_2

    .line 37
    .line 38
    if-ne v3, v4, :cond_1

    .line 39
    .line 40
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_3

    .line 44
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw v0

    .line 52
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 56
    .line 57
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, LTx/a;

    .line 62
    .line 63
    if-eqz p5, :cond_3

    .line 64
    .line 65
    invoke-virtual/range {p5 .. p5}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v5

    .line 69
    :goto_1
    move-wide v12, v5

    .line 70
    goto :goto_2

    .line 71
    :cond_3
    const-wide/16 v5, 0x0

    .line 72
    .line 73
    goto :goto_1

    .line 74
    :goto_2
    new-instance v7, LWx/a;

    .line 75
    .line 76
    move-wide v10, p1

    .line 77
    move-wide/from16 v8, p3

    .line 78
    .line 79
    invoke-direct/range {v7 .. v13}, LWx/a;-><init>(JDJ)V

    .line 80
    .line 81
    .line 82
    iput v4, v1, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource$makeBetGame$1;->label:I

    .line 83
    .line 84
    move-object/from16 v3, p6

    .line 85
    .line 86
    invoke-interface {v0, v3, v7, v1}, LTx/a;->b(Ljava/lang/String;LWx/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    if-ne v0, v2, :cond_4

    .line 91
    .line 92
    return-object v2

    .line 93
    :cond_4
    :goto_3
    check-cast v0, Lorg/xbet/core/data/j;

    .line 94
    .line 95
    invoke-virtual {v0}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    return-object v0
.end method
