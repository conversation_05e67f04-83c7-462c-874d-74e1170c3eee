.class public final LJb1/i$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LJb1/k$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LJb1/i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LJb1/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LJb1/i$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lc81/a;Ltf0/a;Lak/a;Lak/b;LWa0/a;ZLTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)LJb1/k;
    .locals 44

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    invoke-static/range {p20 .. p20}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    invoke-static/range {p21 .. p21}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    invoke-static/range {p22 .. p22}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    invoke-static/range {p23 .. p23}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    invoke-static/range {p24 .. p24}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    invoke-static/range {p25 .. p25}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    invoke-static/range {p26 .. p26}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    invoke-static/range {p27 .. p27}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    invoke-static/range {p28 .. p28}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    invoke-static/range {p29 .. p29}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    invoke-static/range {p30 .. p30}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    invoke-static/range {p31 .. p31}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    invoke-static/range {p32 .. p32}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    invoke-static/range {p33 .. p33}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    invoke-static/range {p34 .. p34}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    invoke-static/range {p35 .. p35}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    invoke-static/range {p36 .. p36}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    invoke-static/range {p37 .. p37}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    invoke-static/range {p38 .. p38}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    invoke-static/range {p39 .. p39}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    invoke-static/range {p40 .. p40}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    invoke-static/range {p41 .. p41}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    new-instance v1, LJb1/i$b;

    .line 129
    .line 130
    invoke-static/range {p7 .. p7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 131
    .line 132
    .line 133
    move-result-object v8

    .line 134
    const/16 v43, 0x0

    .line 135
    .line 136
    move-object/from16 v2, p1

    .line 137
    .line 138
    move-object/from16 v4, p2

    .line 139
    .line 140
    move-object/from16 v3, p3

    .line 141
    .line 142
    move-object/from16 v5, p4

    .line 143
    .line 144
    move-object/from16 v6, p5

    .line 145
    .line 146
    move-object/from16 v7, p6

    .line 147
    .line 148
    move-object/from16 v9, p8

    .line 149
    .line 150
    move-object/from16 v10, p9

    .line 151
    .line 152
    move-object/from16 v11, p10

    .line 153
    .line 154
    move-object/from16 v12, p11

    .line 155
    .line 156
    move-object/from16 v13, p12

    .line 157
    .line 158
    move-object/from16 v14, p13

    .line 159
    .line 160
    move-object/from16 v15, p14

    .line 161
    .line 162
    move-object/from16 v16, p15

    .line 163
    .line 164
    move-object/from16 v17, p16

    .line 165
    .line 166
    move-object/from16 v18, p17

    .line 167
    .line 168
    move-object/from16 v19, p18

    .line 169
    .line 170
    move-object/from16 v20, p19

    .line 171
    .line 172
    move-object/from16 v21, p20

    .line 173
    .line 174
    move-object/from16 v22, p21

    .line 175
    .line 176
    move-object/from16 v23, p22

    .line 177
    .line 178
    move-object/from16 v24, p23

    .line 179
    .line 180
    move-object/from16 v25, p24

    .line 181
    .line 182
    move-object/from16 v26, p25

    .line 183
    .line 184
    move-object/from16 v27, p26

    .line 185
    .line 186
    move-object/from16 v28, p27

    .line 187
    .line 188
    move-object/from16 v29, p28

    .line 189
    .line 190
    move-object/from16 v30, p29

    .line 191
    .line 192
    move-object/from16 v31, p30

    .line 193
    .line 194
    move-object/from16 v32, p31

    .line 195
    .line 196
    move-object/from16 v33, p32

    .line 197
    .line 198
    move-object/from16 v34, p33

    .line 199
    .line 200
    move-object/from16 v35, p34

    .line 201
    .line 202
    move-object/from16 v36, p35

    .line 203
    .line 204
    move-object/from16 v37, p36

    .line 205
    .line 206
    move-object/from16 v38, p37

    .line 207
    .line 208
    move-object/from16 v39, p38

    .line 209
    .line 210
    move-object/from16 v40, p39

    .line 211
    .line 212
    move-object/from16 v41, p40

    .line 213
    .line 214
    move-object/from16 v42, p41

    .line 215
    .line 216
    invoke-direct/range {v1 .. v43}, LJb1/i$b;-><init>(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;LJb1/j;)V

    .line 217
    .line 218
    .line 219
    return-object v1
.end method
