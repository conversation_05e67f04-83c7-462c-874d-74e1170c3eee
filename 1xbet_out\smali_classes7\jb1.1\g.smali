.class public final Ljb1/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/g$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a9\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\n0\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u001f\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "Lk81/a;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;",
        "aggregatorTournamentPrizesStyle",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "",
        "currencySymbol",
        "LHX0/e;",
        "resourceManager",
        "",
        "LV21/a;",
        "b",
        "(Lk81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;",
        "",
        "position",
        "LL11/c;",
        "a",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;I)LL11/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;I)LL11/c;
    .locals 4

    .line 1
    sget-object v0, Ljb1/g$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const/4 v1, 0x3

    .line 10
    const/4 v2, 0x2

    .line 11
    const/4 v3, 0x1

    .line 12
    if-eq v0, v3, :cond_4

    .line 13
    .line 14
    if-eq v0, v2, :cond_4

    .line 15
    .line 16
    if-eq v0, v1, :cond_4

    .line 17
    .line 18
    const/4 p0, 0x4

    .line 19
    if-ne v0, p0, :cond_3

    .line 20
    .line 21
    if-eq p1, v3, :cond_2

    .line 22
    .line 23
    if-eq p1, v2, :cond_1

    .line 24
    .line 25
    if-eq p1, v1, :cond_0

    .line 26
    .line 27
    const-string p0, "Illustration_Other.webp"

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const-string p0, "Illustration_Bronze.webp"

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const-string p0, "Illustration_Silver.webp"

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_2
    const-string p0, "Illustration_Gold.webp"

    .line 37
    .line 38
    :goto_0
    sget-object p1, LCX0/l;->a:LCX0/l;

    .line 39
    .line 40
    new-instance v0, Ljava/lang/StringBuilder;

    .line 41
    .line 42
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 43
    .line 44
    .line 45
    const-string v1, "/static/img/android/casino/alt_design/aggregator_tournament_prize/illustration/"

    .line 46
    .line 47
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 48
    .line 49
    .line 50
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    invoke-virtual {p1, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    return-object p0

    .line 70
    :cond_3
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 71
    .line 72
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 73
    .line 74
    .line 75
    throw p0

    .line 76
    :cond_4
    if-eq p1, v3, :cond_8

    .line 77
    .line 78
    if-eq p1, v2, :cond_7

    .line 79
    .line 80
    if-eq p1, v1, :cond_6

    .line 81
    .line 82
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;->NUMBER:Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;

    .line 83
    .line 84
    if-ne p0, p1, :cond_5

    .line 85
    .line 86
    const-string p0, "Number_Other_Star.webp"

    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_5
    const-string p0, "Number_Other.webp"

    .line 90
    .line 91
    goto :goto_1

    .line 92
    :cond_6
    const-string p0, "Number_Bronze.webp"

    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_7
    const-string p0, "Number_Silver.webp"

    .line 96
    .line 97
    goto :goto_1

    .line 98
    :cond_8
    const-string p0, "Number_Gold.webp"

    .line 99
    .line 100
    :goto_1
    sget-object p1, LCX0/l;->a:LCX0/l;

    .line 101
    .line 102
    new-instance v0, Ljava/lang/StringBuilder;

    .line 103
    .line 104
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 105
    .line 106
    .line 107
    const-string v1, "/static/img/android/casino/alt_design/aggregator_tournament_prize/icon_s_icon_l_number/"

    .line 108
    .line 109
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 110
    .line 111
    .line 112
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 113
    .line 114
    .line 115
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object p0

    .line 119
    invoke-virtual {p1, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 120
    .line 121
    .line 122
    move-result-object p0

    .line 123
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 124
    .line 125
    .line 126
    move-result-object p0

    .line 127
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 128
    .line 129
    .line 130
    move-result-object p0

    .line 131
    return-object p0
.end method

.method public static final b(Lk81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;
    .locals 16
    .param p0    # Lk81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk81/a;",
            "Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Ljava/lang/String;",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "LV21/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p4

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, Lk81/a;->a()Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;->PLACES_COUNT:Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    const/4 v1, 0x1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v1, 0x0

    .line 15
    :goto_0
    sget v2, Lpb/k;->fs:I

    .line 16
    .line 17
    new-array v4, v3, [Ljava/lang/Object;

    .line 18
    .line 19
    invoke-interface {v0, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual/range {p0 .. p0}, Lk81/a;->d()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    new-instance v5, Ljava/util/ArrayList;

    .line 28
    .line 29
    const/16 v6, 0xa

    .line 30
    .line 31
    invoke-static {v4, v6}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 32
    .line 33
    .line 34
    move-result v6

    .line 35
    invoke-direct {v5, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 36
    .line 37
    .line 38
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    const/4 v6, 0x0

    .line 43
    :goto_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result v7

    .line 47
    if-eqz v7, :cond_6

    .line 48
    .line 49
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v7

    .line 53
    add-int/lit8 v8, v6, 0x1

    .line 54
    .line 55
    if-gez v6, :cond_1

    .line 56
    .line 57
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 58
    .line 59
    .line 60
    :cond_1
    check-cast v7, Lk81/b;

    .line 61
    .line 62
    const-string v6, " "

    .line 63
    .line 64
    if-eqz v1, :cond_3

    .line 65
    .line 66
    sget v9, Lpb/k;->place:I

    .line 67
    .line 68
    new-array v10, v3, [Ljava/lang/Object;

    .line 69
    .line 70
    invoke-interface {v0, v9, v10}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v9

    .line 74
    invoke-virtual {v7}, Lk81/b;->d()I

    .line 75
    .line 76
    .line 77
    move-result v10

    .line 78
    invoke-virtual {v7}, Lk81/b;->e()I

    .line 79
    .line 80
    .line 81
    move-result v11

    .line 82
    if-ne v10, v11, :cond_2

    .line 83
    .line 84
    new-instance v11, Ljava/lang/StringBuilder;

    .line 85
    .line 86
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 87
    .line 88
    .line 89
    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 93
    .line 94
    .line 95
    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 96
    .line 97
    .line 98
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v9

    .line 102
    :goto_2
    move-object v13, v9

    .line 103
    goto :goto_3

    .line 104
    :cond_2
    new-instance v12, Ljava/lang/StringBuilder;

    .line 105
    .line 106
    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    .line 107
    .line 108
    .line 109
    invoke-virtual {v12, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 110
    .line 111
    .line 112
    const-string v10, "-"

    .line 113
    .line 114
    invoke-virtual {v12, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 115
    .line 116
    .line 117
    invoke-virtual {v12, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 118
    .line 119
    .line 120
    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 121
    .line 122
    .line 123
    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 124
    .line 125
    .line 126
    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 127
    .line 128
    .line 129
    move-result-object v9

    .line 130
    goto :goto_2

    .line 131
    :cond_3
    sget v9, Lpb/k;->stocks_prizes:I

    .line 132
    .line 133
    new-array v10, v3, [Ljava/lang/Object;

    .line 134
    .line 135
    invoke-interface {v0, v9, v10}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 136
    .line 137
    .line 138
    move-result-object v9

    .line 139
    invoke-virtual {v7}, Lk81/b;->g()Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object v10

    .line 143
    new-instance v11, Ljava/lang/StringBuilder;

    .line 144
    .line 145
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 146
    .line 147
    .line 148
    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 149
    .line 150
    .line 151
    const-string v9, ": "

    .line 152
    .line 153
    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 154
    .line 155
    .line 156
    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 157
    .line 158
    .line 159
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v9

    .line 163
    goto :goto_2

    .line 164
    :goto_3
    invoke-virtual {v7}, Lk81/b;->b()I

    .line 165
    .line 166
    .line 167
    move-result v9

    .line 168
    if-lez v9, :cond_4

    .line 169
    .line 170
    invoke-virtual {v7}, Lk81/b;->b()I

    .line 171
    .line 172
    .line 173
    move-result v9

    .line 174
    new-instance v10, Ljava/lang/StringBuilder;

    .line 175
    .line 176
    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    .line 177
    .line 178
    .line 179
    invoke-virtual {v10, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 180
    .line 181
    .line 182
    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 183
    .line 184
    .line 185
    invoke-virtual {v10, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 186
    .line 187
    .line 188
    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 189
    .line 190
    .line 191
    move-result-object v9

    .line 192
    :goto_4
    move-object v15, v9

    .line 193
    goto :goto_5

    .line 194
    :cond_4
    const-string v9, ""

    .line 195
    .line 196
    goto :goto_4

    .line 197
    :goto_5
    sget-object v9, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 198
    .line 199
    move-object/from16 v10, p2

    .line 200
    .line 201
    if-eq v10, v9, :cond_5

    .line 202
    .line 203
    invoke-virtual {v7}, Lk81/b;->f()Ljava/lang/String;

    .line 204
    .line 205
    .line 206
    move-result-object v6

    .line 207
    move-object/from16 v12, p3

    .line 208
    .line 209
    :goto_6
    move-object v14, v6

    .line 210
    goto :goto_7

    .line 211
    :cond_5
    sget-object v9, Ll8/j;->a:Ll8/j;

    .line 212
    .line 213
    invoke-virtual {v7}, Lk81/b;->a()D

    .line 214
    .line 215
    .line 216
    move-result-wide v11

    .line 217
    sget-object v14, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 218
    .line 219
    invoke-virtual {v9, v11, v12, v14}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 220
    .line 221
    .line 222
    move-result-object v9

    .line 223
    new-instance v11, Ljava/lang/StringBuilder;

    .line 224
    .line 225
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 226
    .line 227
    .line 228
    move-object/from16 v12, p3

    .line 229
    .line 230
    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 231
    .line 232
    .line 233
    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 234
    .line 235
    .line 236
    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 237
    .line 238
    .line 239
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 240
    .line 241
    .line 242
    move-result-object v6

    .line 243
    goto :goto_6

    .line 244
    :goto_7
    new-instance v10, LV21/a;

    .line 245
    .line 246
    invoke-virtual {v7}, Lk81/b;->c()Ljava/lang/String;

    .line 247
    .line 248
    .line 249
    move-result-object v11

    .line 250
    move-object/from16 v6, p1

    .line 251
    .line 252
    invoke-static {v6, v8}, Ljb1/g;->a(Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;I)LL11/c;

    .line 253
    .line 254
    .line 255
    move-result-object v12

    .line 256
    invoke-direct/range {v10 .. v15}, LV21/a;-><init>(Ljava/lang/String;LL11/c;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 257
    .line 258
    .line 259
    invoke-interface {v5, v10}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 260
    .line 261
    .line 262
    move v6, v8

    .line 263
    goto/16 :goto_1

    .line 264
    .line 265
    :cond_6
    return-object v5
.end method
