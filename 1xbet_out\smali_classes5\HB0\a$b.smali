.class public final LHB0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LHB0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LHB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LHB0/a$b$p;,
        LHB0/a$b$b;,
        LHB0/a$b$m;,
        LHB0/a$b$g;,
        LHB0/a$b$f;,
        LHB0/a$b$x;,
        LHB0/a$b$i;,
        LHB0/a$b$l;,
        LHB0/a$b$d;,
        LHB0/a$b$h;,
        LHB0/a$b$j;,
        LHB0/a$b$o;,
        LHB0/a$b$k;,
        LHB0/a$b$q;,
        LHB0/a$b$w;,
        LHB0/a$b$r;,
        LHB0/a$b$t;,
        LHB0/a$b$a;,
        LHB0/a$b$v;,
        LHB0/a$b$n;,
        LHB0/a$b$c;,
        LHB0/a$b$s;,
        LHB0/a$b$u;,
        LHB0/a$b$e;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LEP/b;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/c;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/m;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/n;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/c;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/scenarios/ObserveInsightsMarketsScenario;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/l;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/q;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBi0/a;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/z;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/i;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/m;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/f;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/h;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/c;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lzg/a;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwR/a;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBi0/d;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNP/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LzX0/k;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/feed/subscriptions/domain/scenarios/b;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LAX0/b;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/n;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LIj0/a;

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Lsw/a;

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ltw/b;",
            ">;"
        }
    .end annotation
.end field

.field public final f:Lqa0/a;

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ltw/j;",
            ">;"
        }
    .end annotation
.end field

.field public final g:Ld90/a;

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lll/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LHB0/a$b;

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LEP/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/g;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHR/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LyB0/b;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/j;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljo/a;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lra0/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/a;",
            ">;"
        }
    .end annotation
.end field

.field public n0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lk8/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/e;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/data/repositories/InsightsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public p0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqP/c;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/betting/core/tax/domain/usecase/e;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public r0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/g;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;"
        }
    .end annotation
.end field

.field public s0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public t0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Leu/l;",
            ">;"
        }
    .end annotation
.end field

.field public u0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAu/b;",
            ">;"
        }
    .end annotation
.end field

.field public v0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le90/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQn/a;",
            ">;"
        }
    .end annotation
.end field

.field public w0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqR/a;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQn/b;",
            ">;"
        }
    .end annotation
.end field

.field public x0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/MarketsViewModelDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/FetchInsightsMarketsUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public y0:Lorg/xbet/sportgame/markets/impl/presentation/insights/l;

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/s;",
            ">;"
        }
    .end annotation
.end field

.field public z0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHB0/h;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V
    .locals 5

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LHB0/a$b;->h:LHB0/a$b;

    move-object/from16 v0, p27

    .line 4
    iput-object v0, p0, LHB0/a$b;->a:LTZ0/a;

    move-object/from16 v1, p15

    .line 5
    iput-object v1, p0, LHB0/a$b;->b:LzX0/k;

    move-object/from16 v2, p16

    .line 6
    iput-object v2, p0, LHB0/a$b;->c:LAX0/b;

    move-object/from16 v3, p11

    .line 7
    iput-object v3, p0, LHB0/a$b;->d:LIj0/a;

    .line 8
    iput-object p3, p0, LHB0/a$b;->e:Lsw/a;

    move-object/from16 v4, p17

    .line 9
    iput-object v4, p0, LHB0/a$b;->f:Lqa0/a;

    .line 10
    iput-object p9, p0, LHB0/a$b;->g:Ld90/a;

    .line 11
    invoke-virtual/range {p0 .. p46}, LHB0/a$b;->b(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V

    .line 12
    invoke-virtual/range {p0 .. p46}, LHB0/a$b;->c(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V

    .line 13
    invoke-virtual/range {p0 .. p46}, LHB0/a$b;->d(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;LHB0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p46}, LHB0/a$b;-><init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LHB0/a$b;->e(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;)Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V
    .locals 1

    .line 1
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, LHB0/a$b;->i:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, LHB0/a$b;->j:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p1}, LyB0/c;->a(LBc/a;)LyB0/c;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LHB0/a$b;->k:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {}, LHB0/g;->a()LHB0/g;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {p1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    iput-object p1, p0, LHB0/a$b;->l:Ldagger/internal/h;

    .line 28
    .line 29
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iput-object p1, p0, LHB0/a$b;->m:Ldagger/internal/h;

    .line 34
    .line 35
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iput-object p1, p0, LHB0/a$b;->n:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static/range {p33 .. p33}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, LHB0/a$b;->o:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object p3, p0, LHB0/a$b;->k:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object p4, p0, LHB0/a$b;->l:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object p5, p0, LHB0/a$b;->m:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object p6, p0, LHB0/a$b;->n:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p3, p4, p5, p6, p1}, Lorg/xbet/sportgame/markets/impl/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/data/repositories/a;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput-object p1, p0, LHB0/a$b;->p:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iput-object p1, p0, LHB0/a$b;->q:Ldagger/internal/h;

    .line 66
    .line 67
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    iput-object p1, p0, LHB0/a$b;->r:Ldagger/internal/h;

    .line 72
    .line 73
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iput-object p1, p0, LHB0/a$b;->s:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p3, p0, LHB0/a$b;->r:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static {p3, p1}, Lcom/xbet/onexuser/domain/usecases/b;->a(LBc/a;LBc/a;)Lcom/xbet/onexuser/domain/usecases/b;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LHB0/a$b;->t:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p46 .. p46}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LHB0/a$b;->u:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static/range {p45 .. p45}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LHB0/a$b;->v:Ldagger/internal/h;

    .line 98
    .line 99
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iput-object p1, p0, LHB0/a$b;->w:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static/range {p30 .. p30}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    iput-object p1, p0, LHB0/a$b;->x:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object p3, p0, LHB0/a$b;->p:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object p4, p0, LHB0/a$b;->q:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p5, p0, LHB0/a$b;->t:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p6, p0, LHB0/a$b;->u:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p7, p0, LHB0/a$b;->v:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v0, p0, LHB0/a$b;->w:Ldagger/internal/h;

    .line 122
    .line 123
    move-object p9, p1

    .line 124
    move-object p8, v0

    .line 125
    invoke-static/range {p3 .. p9}, Lorg/xbet/sportgame/markets/impl/domain/usecases/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/a;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LHB0/a$b;->y:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object p1, p0, LHB0/a$b;->p:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/t;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/t;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, LHB0/a$b;->z:Ldagger/internal/h;

    .line 138
    .line 139
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    iput-object p1, p0, LHB0/a$b;->A:Ldagger/internal/h;

    .line 144
    .line 145
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/d;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/d;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    iput-object p1, p0, LHB0/a$b;->B:Ldagger/internal/h;

    .line 150
    .line 151
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    iput-object p1, p0, LHB0/a$b;->C:Ldagger/internal/h;

    .line 156
    .line 157
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/n;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/n;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    iput-object p1, p0, LHB0/a$b;->D:Ldagger/internal/h;

    .line 162
    .line 163
    new-instance p1, LHB0/a$b$p;

    .line 164
    .line 165
    invoke-direct {p1, p2}, LHB0/a$b$p;-><init>(LKA0/c;)V

    .line 166
    .line 167
    .line 168
    iput-object p1, p0, LHB0/a$b;->E:Ldagger/internal/h;

    .line 169
    .line 170
    new-instance p1, LHB0/a$b$b;

    .line 171
    .line 172
    invoke-direct {p1, p2}, LHB0/a$b$b;-><init>(LKA0/c;)V

    .line 173
    .line 174
    .line 175
    iput-object p1, p0, LHB0/a$b;->F:Ldagger/internal/h;

    .line 176
    .line 177
    iget-object p2, p0, LHB0/a$b;->z:Ldagger/internal/h;

    .line 178
    .line 179
    iget-object p3, p0, LHB0/a$b;->B:Ldagger/internal/h;

    .line 180
    .line 181
    iget-object p4, p0, LHB0/a$b;->D:Ldagger/internal/h;

    .line 182
    .line 183
    iget-object p5, p0, LHB0/a$b;->E:Ldagger/internal/h;

    .line 184
    .line 185
    invoke-static {p2, p3, p4, p5, p1}, Lorg/xbet/sportgame/markets/impl/domain/scenarios/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/scenarios/a;

    .line 186
    .line 187
    .line 188
    move-result-object p1

    .line 189
    iput-object p1, p0, LHB0/a$b;->G:Ldagger/internal/h;

    .line 190
    .line 191
    return-void
.end method

.method public final c(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V
    .locals 0

    .line 1
    new-instance p9, LHB0/a$b$m;

    .line 2
    .line 3
    invoke-direct {p9, p2}, LHB0/a$b$m;-><init>(LKA0/c;)V

    .line 4
    .line 5
    .line 6
    iput-object p9, p0, LHB0/a$b;->H:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 9
    .line 10
    .line 11
    move-result-object p9

    .line 12
    iput-object p9, p0, LHB0/a$b;->I:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p9}, Lorg/xbet/sportgame/markets/impl/domain/usecases/r;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/r;

    .line 15
    .line 16
    .line 17
    move-result-object p9

    .line 18
    iput-object p9, p0, LHB0/a$b;->J:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p9

    .line 24
    iput-object p9, p0, LHB0/a$b;->K:Ldagger/internal/h;

    .line 25
    .line 26
    new-instance p9, LHB0/a$b$g;

    .line 27
    .line 28
    invoke-direct {p9, p1}, LHB0/a$b$g;-><init>(LQW0/c;)V

    .line 29
    .line 30
    .line 31
    iput-object p9, p0, LHB0/a$b;->L:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, LHB0/a$b;->M:Ldagger/internal/h;

    .line 38
    .line 39
    new-instance p1, LHB0/a$b$f;

    .line 40
    .line 41
    invoke-direct {p1, p4}, LHB0/a$b$f;-><init>(LAi0/a;)V

    .line 42
    .line 43
    .line 44
    iput-object p1, p0, LHB0/a$b;->N:Ldagger/internal/h;

    .line 45
    .line 46
    new-instance p1, LHB0/a$b$x;

    .line 47
    .line 48
    invoke-direct {p1, p2}, LHB0/a$b$x;-><init>(LKA0/c;)V

    .line 49
    .line 50
    .line 51
    iput-object p1, p0, LHB0/a$b;->O:Ldagger/internal/h;

    .line 52
    .line 53
    new-instance p1, LHB0/a$b$i;

    .line 54
    .line 55
    invoke-direct {p1, p7}, LHB0/a$b$i;-><init>(Lll/a;)V

    .line 56
    .line 57
    .line 58
    iput-object p1, p0, LHB0/a$b;->P:Ldagger/internal/h;

    .line 59
    .line 60
    new-instance p1, LHB0/a$b$l;

    .line 61
    .line 62
    invoke-direct {p1, p7}, LHB0/a$b$l;-><init>(Lll/a;)V

    .line 63
    .line 64
    .line 65
    iput-object p1, p0, LHB0/a$b;->Q:Ldagger/internal/h;

    .line 66
    .line 67
    new-instance p1, LHB0/a$b$d;

    .line 68
    .line 69
    invoke-direct {p1, p7}, LHB0/a$b$d;-><init>(Lll/a;)V

    .line 70
    .line 71
    .line 72
    iput-object p1, p0, LHB0/a$b;->R:Ldagger/internal/h;

    .line 73
    .line 74
    new-instance p1, LHB0/a$b$h;

    .line 75
    .line 76
    invoke-direct {p1, p7}, LHB0/a$b$h;-><init>(Lll/a;)V

    .line 77
    .line 78
    .line 79
    iput-object p1, p0, LHB0/a$b;->S:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static/range {p40 .. p40}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LHB0/a$b;->T:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p41 .. p41}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LHB0/a$b;->U:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p2, p0, LHB0/a$b;->T:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object p7, p0, LHB0/a$b;->o:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object p9, p0, LHB0/a$b;->q:Ldagger/internal/h;

    .line 98
    .line 99
    invoke-static {p2, p7, p1, p9}, Lzg/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lzg/b;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iput-object p1, p0, LHB0/a$b;->V:Ldagger/internal/h;

    .line 104
    .line 105
    new-instance p1, LHB0/a$b$j;

    .line 106
    .line 107
    invoke-direct {p1, p8}, LHB0/a$b$j;-><init>(LiR/a;)V

    .line 108
    .line 109
    .line 110
    iput-object p1, p0, LHB0/a$b;->W:Ldagger/internal/h;

    .line 111
    .line 112
    new-instance p1, LHB0/a$b$o;

    .line 113
    .line 114
    invoke-direct {p1, p4}, LHB0/a$b$o;-><init>(LAi0/a;)V

    .line 115
    .line 116
    .line 117
    iput-object p1, p0, LHB0/a$b;->X:Ldagger/internal/h;

    .line 118
    .line 119
    new-instance p1, LHB0/a$b$k;

    .line 120
    .line 121
    invoke-direct {p1, p10}, LHB0/a$b$k;-><init>(Lak/a;)V

    .line 122
    .line 123
    .line 124
    iput-object p1, p0, LHB0/a$b;->Y:Ldagger/internal/h;

    .line 125
    .line 126
    new-instance p1, LHB0/a$b$q;

    .line 127
    .line 128
    invoke-direct {p1, p10}, LHB0/a$b$q;-><init>(Lak/a;)V

    .line 129
    .line 130
    .line 131
    iput-object p1, p0, LHB0/a$b;->Z:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static/range {p36 .. p36}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, LHB0/a$b;->a0:Ldagger/internal/h;

    .line 138
    .line 139
    new-instance p1, LHB0/a$b$w;

    .line 140
    .line 141
    invoke-direct {p1, p5}, LHB0/a$b$w;-><init>(LDZ/m;)V

    .line 142
    .line 143
    .line 144
    iput-object p1, p0, LHB0/a$b;->b0:Ldagger/internal/h;

    .line 145
    .line 146
    new-instance p1, LHB0/a$b$r;

    .line 147
    .line 148
    invoke-direct {p1, p10}, LHB0/a$b$r;-><init>(Lak/a;)V

    .line 149
    .line 150
    .line 151
    iput-object p1, p0, LHB0/a$b;->c0:Ldagger/internal/h;

    .line 152
    .line 153
    new-instance p1, LHB0/a$b$t;

    .line 154
    .line 155
    invoke-direct {p1, p6}, LHB0/a$b$t;-><init>(Ldk0/p;)V

    .line 156
    .line 157
    .line 158
    iput-object p1, p0, LHB0/a$b;->d0:Ldagger/internal/h;

    .line 159
    .line 160
    new-instance p1, LHB0/a$b$a;

    .line 161
    .line 162
    invoke-direct {p1, p3}, LHB0/a$b$a;-><init>(Lsw/a;)V

    .line 163
    .line 164
    .line 165
    iput-object p1, p0, LHB0/a$b;->e0:Ldagger/internal/h;

    .line 166
    .line 167
    new-instance p1, LHB0/a$b$v;

    .line 168
    .line 169
    invoke-direct {p1, p3}, LHB0/a$b$v;-><init>(Lsw/a;)V

    .line 170
    .line 171
    .line 172
    iput-object p1, p0, LHB0/a$b;->f0:Ldagger/internal/h;

    .line 173
    .line 174
    return-void
.end method

.method public final d(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Ld90/a;Lak/a;LIj0/a;Lmo/f;LwX0/c;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LAu/b;Leu/l;)V
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p8

    .line 1
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->g0:Ldagger/internal/h;

    .line 2
    invoke-static/range {p43 .. p43}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->h0:Ldagger/internal/h;

    .line 3
    invoke-static {v2}, Lorg/xbet/sportgame/markets/impl/domain/usecases/h;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/h;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->i0:Ldagger/internal/h;

    .line 4
    new-instance v2, LHB0/a$b$n;

    invoke-direct {v2, v1}, LHB0/a$b$n;-><init>(LiR/a;)V

    iput-object v2, v0, LHB0/a$b;->j0:Ldagger/internal/h;

    .line 5
    iget-object v2, v0, LHB0/a$b;->q:Ldagger/internal/h;

    iget-object v3, v0, LHB0/a$b;->T:Ldagger/internal/h;

    iget-object v4, v0, LHB0/a$b;->o:Ldagger/internal/h;

    iget-object v5, v0, LHB0/a$b;->U:Ldagger/internal/h;

    invoke-static {v2, v3, v4, v5}, LDg/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;)LDg/b;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->k0:Ldagger/internal/h;

    .line 6
    new-instance v2, LHB0/a$b$c;

    move-object/from16 v3, p10

    invoke-direct {v2, v3}, LHB0/a$b$c;-><init>(Lak/a;)V

    iput-object v2, v0, LHB0/a$b;->l0:Ldagger/internal/h;

    .line 7
    invoke-static/range {p37 .. p37}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->m0:Ldagger/internal/h;

    .line 8
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->n0:Ldagger/internal/h;

    .line 9
    invoke-static {v2}, Lorg/xbet/sportgame/markets/impl/domain/usecases/f;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/f;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->o0:Ldagger/internal/h;

    .line 10
    invoke-static/range {p38 .. p38}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->p0:Ldagger/internal/h;

    .line 11
    new-instance v2, LHB0/a$b$s;

    move-object/from16 v3, p12

    invoke-direct {v2, v3}, LHB0/a$b$s;-><init>(Lmo/f;)V

    iput-object v2, v0, LHB0/a$b;->q0:Ldagger/internal/h;

    .line 12
    invoke-static/range {p44 .. p44}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->r0:Ldagger/internal/h;

    .line 13
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->s0:Ldagger/internal/h;

    .line 14
    invoke-static/range {p39 .. p39}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->t0:Ldagger/internal/h;

    .line 15
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    move-result-object v2

    iput-object v2, v0, LHB0/a$b;->u0:Ldagger/internal/h;

    .line 16
    new-instance v2, LHB0/a$b$u;

    move-object/from16 v3, p9

    invoke-direct {v2, v3}, LHB0/a$b$u;-><init>(Ld90/a;)V

    iput-object v2, v0, LHB0/a$b;->v0:Ldagger/internal/h;

    .line 17
    new-instance v2, LHB0/a$b$e;

    invoke-direct {v2, v1}, LHB0/a$b$e;-><init>(LiR/a;)V

    iput-object v2, v0, LHB0/a$b;->w0:Ldagger/internal/h;

    .line 18
    iget-object v1, v0, LHB0/a$b;->N:Ldagger/internal/h;

    iget-object v3, v0, LHB0/a$b;->O:Ldagger/internal/h;

    iget-object v4, v0, LHB0/a$b;->P:Ldagger/internal/h;

    iget-object v5, v0, LHB0/a$b;->Q:Ldagger/internal/h;

    iget-object v6, v0, LHB0/a$b;->R:Ldagger/internal/h;

    iget-object v7, v0, LHB0/a$b;->S:Ldagger/internal/h;

    iget-object v8, v0, LHB0/a$b;->L:Ldagger/internal/h;

    iget-object v9, v0, LHB0/a$b;->K:Ldagger/internal/h;

    iget-object v10, v0, LHB0/a$b;->V:Ldagger/internal/h;

    iget-object v11, v0, LHB0/a$b;->W:Ldagger/internal/h;

    iget-object v12, v0, LHB0/a$b;->X:Ldagger/internal/h;

    iget-object v13, v0, LHB0/a$b;->Y:Ldagger/internal/h;

    iget-object v14, v0, LHB0/a$b;->Z:Ldagger/internal/h;

    iget-object v15, v0, LHB0/a$b;->a0:Ldagger/internal/h;

    move-object/from16 p1, v1

    iget-object v1, v0, LHB0/a$b;->b0:Ldagger/internal/h;

    move-object/from16 p15, v1

    iget-object v1, v0, LHB0/a$b;->c0:Ldagger/internal/h;

    move-object/from16 p16, v1

    iget-object v1, v0, LHB0/a$b;->d0:Ldagger/internal/h;

    move-object/from16 p17, v1

    iget-object v1, v0, LHB0/a$b;->e0:Ldagger/internal/h;

    move-object/from16 p18, v1

    iget-object v1, v0, LHB0/a$b;->f0:Ldagger/internal/h;

    move-object/from16 p19, v1

    iget-object v1, v0, LHB0/a$b;->g0:Ldagger/internal/h;

    move-object/from16 p20, v1

    iget-object v1, v0, LHB0/a$b;->i0:Ldagger/internal/h;

    move-object/from16 p21, v1

    iget-object v1, v0, LHB0/a$b;->j0:Ldagger/internal/h;

    move-object/from16 p22, v1

    iget-object v1, v0, LHB0/a$b;->k0:Ldagger/internal/h;

    move-object/from16 p23, v1

    iget-object v1, v0, LHB0/a$b;->l0:Ldagger/internal/h;

    move-object/from16 p24, v1

    iget-object v1, v0, LHB0/a$b;->m0:Ldagger/internal/h;

    move-object/from16 p25, v1

    iget-object v1, v0, LHB0/a$b;->o0:Ldagger/internal/h;

    move-object/from16 p26, v1

    iget-object v1, v0, LHB0/a$b;->p0:Ldagger/internal/h;

    move-object/from16 p27, v1

    iget-object v1, v0, LHB0/a$b;->q0:Ldagger/internal/h;

    move-object/from16 p28, v1

    iget-object v1, v0, LHB0/a$b;->M:Ldagger/internal/h;

    move-object/from16 p29, v1

    iget-object v1, v0, LHB0/a$b;->r0:Ldagger/internal/h;

    move-object/from16 p30, v1

    iget-object v1, v0, LHB0/a$b;->s0:Ldagger/internal/h;

    move-object/from16 p31, v1

    iget-object v1, v0, LHB0/a$b;->t0:Ldagger/internal/h;

    move-object/from16 p32, v1

    iget-object v1, v0, LHB0/a$b;->u0:Ldagger/internal/h;

    move-object/from16 p33, v1

    iget-object v1, v0, LHB0/a$b;->v0:Ldagger/internal/h;

    move-object/from16 p34, v1

    move-object/from16 p35, v2

    move-object/from16 p2, v3

    move-object/from16 p3, v4

    move-object/from16 p4, v5

    move-object/from16 p5, v6

    move-object/from16 p6, v7

    move-object/from16 p7, v8

    move-object/from16 p8, v9

    move-object/from16 p9, v10

    move-object/from16 p10, v11

    move-object/from16 p11, v12

    move-object/from16 p12, v13

    move-object/from16 p13, v14

    move-object/from16 p14, v15

    invoke-static/range {p1 .. p35}, Lorg/xbet/sportgame/markets/impl/presentation/base/n;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/presentation/base/n;

    move-result-object v1

    iput-object v1, v0, LHB0/a$b;->x0:Ldagger/internal/h;

    .line 19
    iget-object v2, v0, LHB0/a$b;->i:Ldagger/internal/h;

    iget-object v3, v0, LHB0/a$b;->y:Ldagger/internal/h;

    iget-object v4, v0, LHB0/a$b;->G:Ldagger/internal/h;

    iget-object v5, v0, LHB0/a$b;->H:Ldagger/internal/h;

    iget-object v6, v0, LHB0/a$b;->F:Ldagger/internal/h;

    iget-object v7, v0, LHB0/a$b;->J:Ldagger/internal/h;

    iget-object v8, v0, LHB0/a$b;->K:Ldagger/internal/h;

    iget-object v9, v0, LHB0/a$b;->L:Ldagger/internal/h;

    iget-object v10, v0, LHB0/a$b;->M:Ldagger/internal/h;

    iget-object v11, v0, LHB0/a$b;->u0:Ldagger/internal/h;

    move-object/from16 p11, v1

    move-object/from16 p2, v2

    move-object/from16 p3, v3

    move-object/from16 p4, v4

    move-object/from16 p5, v5

    move-object/from16 p6, v6

    move-object/from16 p7, v7

    move-object/from16 p8, v8

    move-object/from16 p9, v9

    move-object/from16 p10, v10

    move-object/from16 p12, v11

    invoke-static/range {p2 .. p12}, Lorg/xbet/sportgame/markets/impl/presentation/insights/l;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/presentation/insights/l;

    move-result-object v1

    iput-object v1, v0, LHB0/a$b;->y0:Lorg/xbet/sportgame/markets/impl/presentation/insights/l;

    .line 20
    invoke-static {v1}, LHB0/i;->c(Lorg/xbet/sportgame/markets/impl/presentation/insights/l;)Ldagger/internal/h;

    move-result-object v1

    iput-object v1, v0, LHB0/a$b;->z0:Ldagger/internal/h;

    return-void
.end method

.method public final e(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;)Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LHB0/a$b;->a:LTZ0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->a(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LTZ0/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LHB0/a$b;->b:LzX0/k;

    .line 7
    .line 8
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->b(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LzX0/k;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, LHB0/a$b;->c:LAX0/b;

    .line 12
    .line 13
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->c(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LAX0/b;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, LHB0/a$b;->d:LIj0/a;

    .line 17
    .line 18
    invoke-interface {v0}, LIj0/a;->c()LMj0/b;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, LMj0/b;

    .line 27
    .line 28
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/insights/e;->d(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;LMj0/b;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, LHB0/a$b;->e:Lsw/a;

    .line 32
    .line 33
    invoke-interface {v0}, Lsw/a;->n()Lww/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, Lww/a;

    .line 42
    .line 43
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/insights/e;->a(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;Lww/a;)V

    .line 44
    .line 45
    .line 46
    iget-object v0, p0, LHB0/a$b;->f:Lqa0/a;

    .line 47
    .line 48
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/insights/e;->c(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;Lqa0/a;)V

    .line 49
    .line 50
    .line 51
    iget-object v0, p0, LHB0/a$b;->g:Ld90/a;

    .line 52
    .line 53
    invoke-interface {v0}, Ld90/a;->b()Lf90/a;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, Lf90/a;

    .line 62
    .line 63
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/insights/e;->b(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;Lf90/a;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, LHB0/a$b;->z0:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    check-cast v0, LHB0/h;

    .line 73
    .line 74
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/insights/e;->e(Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;LHB0/h;)V

    .line 75
    .line 76
    .line 77
    return-object p1
.end method
