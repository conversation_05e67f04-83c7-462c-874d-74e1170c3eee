.class public final synthetic LTZ0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements La01/p;


# instance fields
.field public final synthetic a:LTZ0/h;


# direct methods
.method public synthetic constructor <init>(LTZ0/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTZ0/g;->a:LTZ0/h;

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/g;->a:LTZ0/h;

    invoke-static {v0, p1, p2}, LTZ0/h;->N(LTZ0/h;Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;Z)V

    return-void
.end method
