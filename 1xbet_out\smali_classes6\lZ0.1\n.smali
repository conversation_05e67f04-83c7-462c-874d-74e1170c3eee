.class public final LlZ0/n;
.super Ljava/lang/Object;


# static fields
.field public static Astrabet_Dark:I = 0x7f14002b

.field public static Astrabet_Light:I = 0x7f14002c

.field public static DSButton:I = 0x7f140158

.field public static DSButton_ExtraSmall:I = 0x7f140159

.field public static DSButton_ExtraSmall_Commerce:I = 0x7f14015a

.field public static DSButton_ExtraSmall_Commerce_IconCircle:I = 0x7f14015b

.field public static DSButton_ExtraSmall_Commerce_IconLeft:I = 0x7f14015c

.field public static DSButton_ExtraSmall_Commerce_IconRectangle:I = 0x7f14015d

.field public static DSButton_ExtraSmall_Commerce_IconRight:I = 0x7f14015e

.field public static DSButton_ExtraSmall_Primary:I = 0x7f14015f

.field public static DSButton_ExtraSmall_Primary_IconCircle:I = 0x7f140160

.field public static DSButton_ExtraSmall_Primary_IconLeft:I = 0x7f140161

.field public static DSButton_ExtraSmall_Primary_IconRectangle:I = 0x7f140162

.field public static DSButton_ExtraSmall_Primary_IconRight:I = 0x7f140163

.field public static DSButton_ExtraSmall_Quaternary:I = 0x7f140164

.field public static DSButton_ExtraSmall_Quaternary_IconCircle:I = 0x7f140165

.field public static DSButton_ExtraSmall_Quaternary_IconLeft:I = 0x7f140166

.field public static DSButton_ExtraSmall_Quaternary_IconRectangle:I = 0x7f140167

.field public static DSButton_ExtraSmall_Quaternary_IconRight:I = 0x7f140168

.field public static DSButton_ExtraSmall_Secondary:I = 0x7f140169

.field public static DSButton_ExtraSmall_Secondary_IconCircle:I = 0x7f14016a

.field public static DSButton_ExtraSmall_Secondary_IconLeft:I = 0x7f14016b

.field public static DSButton_ExtraSmall_Secondary_IconRectangle:I = 0x7f14016c

.field public static DSButton_ExtraSmall_Secondary_IconRight:I = 0x7f14016d

.field public static DSButton_ExtraSmall_Tertiary:I = 0x7f14016e

.field public static DSButton_ExtraSmall_Tertiary_IconCircle:I = 0x7f14016f

.field public static DSButton_ExtraSmall_Tertiary_IconLeft:I = 0x7f140170

.field public static DSButton_ExtraSmall_Tertiary_IconRectangle:I = 0x7f140171

.field public static DSButton_ExtraSmall_Tertiary_IconRight:I = 0x7f140172

.field public static DSButton_ExtraSmall_Warning:I = 0x7f140173

.field public static DSButton_ExtraSmall_WarningStatic:I = 0x7f140178

.field public static DSButton_ExtraSmall_WarningStatic_IconCircle:I = 0x7f140179

.field public static DSButton_ExtraSmall_WarningStatic_IconLeft:I = 0x7f14017a

.field public static DSButton_ExtraSmall_WarningStatic_IconRectangle:I = 0x7f14017b

.field public static DSButton_ExtraSmall_WarningStatic_IconRight:I = 0x7f14017c

.field public static DSButton_ExtraSmall_Warning_IconCircle:I = 0x7f140174

.field public static DSButton_ExtraSmall_Warning_IconLeft:I = 0x7f140175

.field public static DSButton_ExtraSmall_Warning_IconRectangle:I = 0x7f140176

.field public static DSButton_ExtraSmall_Warning_IconRight:I = 0x7f140177

.field public static DSButton_Large:I = 0x7f14017d

.field public static DSButton_Large_Commerce:I = 0x7f14017e

.field public static DSButton_Large_Commerce_IconCircle:I = 0x7f14017f

.field public static DSButton_Large_Commerce_IconLeft:I = 0x7f140180

.field public static DSButton_Large_Commerce_IconRectangle:I = 0x7f140181

.field public static DSButton_Large_Commerce_IconRight:I = 0x7f140182

.field public static DSButton_Large_Primary:I = 0x7f140183

.field public static DSButton_Large_Primary_IconCircle:I = 0x7f140184

.field public static DSButton_Large_Primary_IconLeft:I = 0x7f140185

.field public static DSButton_Large_Primary_IconRectangle:I = 0x7f140186

.field public static DSButton_Large_Primary_IconRight:I = 0x7f140187

.field public static DSButton_Large_Quaternary:I = 0x7f140188

.field public static DSButton_Large_Quaternary_IconCircle:I = 0x7f140189

.field public static DSButton_Large_Quaternary_IconLeft:I = 0x7f14018a

.field public static DSButton_Large_Quaternary_IconRectangle:I = 0x7f14018b

.field public static DSButton_Large_Quaternary_IconRight:I = 0x7f14018c

.field public static DSButton_Large_Secondary:I = 0x7f14018d

.field public static DSButton_Large_Secondary_IconCircle:I = 0x7f14018e

.field public static DSButton_Large_Secondary_IconLeft:I = 0x7f14018f

.field public static DSButton_Large_Secondary_IconRectangle:I = 0x7f140190

.field public static DSButton_Large_Secondary_IconRight:I = 0x7f140191

.field public static DSButton_Large_Tertiary:I = 0x7f140192

.field public static DSButton_Large_Tertiary_IconCircle:I = 0x7f140193

.field public static DSButton_Large_Tertiary_IconLeft:I = 0x7f140194

.field public static DSButton_Large_Tertiary_IconRectangle:I = 0x7f140195

.field public static DSButton_Large_Tertiary_IconRight:I = 0x7f140196

.field public static DSButton_Large_Warning:I = 0x7f140197

.field public static DSButton_Large_WarningStatic:I = 0x7f14019c

.field public static DSButton_Large_WarningStatic_IconCircle:I = 0x7f14019d

.field public static DSButton_Large_WarningStatic_IconLeft:I = 0x7f14019e

.field public static DSButton_Large_WarningStatic_IconRectangle:I = 0x7f14019f

.field public static DSButton_Large_WarningStatic_IconRight:I = 0x7f1401a0

.field public static DSButton_Large_Warning_IconCircle:I = 0x7f140198

.field public static DSButton_Large_Warning_IconLeft:I = 0x7f140199

.field public static DSButton_Large_Warning_IconRectangle:I = 0x7f14019a

.field public static DSButton_Large_Warning_IconRight:I = 0x7f14019b

.field public static DSButton_Medium:I = 0x7f1401a1

.field public static DSButton_Medium_Commerce:I = 0x7f1401a2

.field public static DSButton_Medium_Commerce_IconCircle:I = 0x7f1401a3

.field public static DSButton_Medium_Commerce_IconLeft:I = 0x7f1401a4

.field public static DSButton_Medium_Commerce_IconRectangle:I = 0x7f1401a5

.field public static DSButton_Medium_Commerce_IconRight:I = 0x7f1401a6

.field public static DSButton_Medium_Primary:I = 0x7f1401a7

.field public static DSButton_Medium_Primary_IconCircle:I = 0x7f1401a8

.field public static DSButton_Medium_Primary_IconLeft:I = 0x7f1401a9

.field public static DSButton_Medium_Primary_IconRectangle:I = 0x7f1401aa

.field public static DSButton_Medium_Primary_IconRight:I = 0x7f1401ab

.field public static DSButton_Medium_Quaternary:I = 0x7f1401ac

.field public static DSButton_Medium_Quaternary_IconCircle:I = 0x7f1401ad

.field public static DSButton_Medium_Quaternary_IconLeft:I = 0x7f1401ae

.field public static DSButton_Medium_Quaternary_IconRectangle:I = 0x7f1401af

.field public static DSButton_Medium_Quaternary_IconRight:I = 0x7f1401b0

.field public static DSButton_Medium_Secondary:I = 0x7f1401b1

.field public static DSButton_Medium_Secondary_IconCircle:I = 0x7f1401b2

.field public static DSButton_Medium_Secondary_IconLeft:I = 0x7f1401b3

.field public static DSButton_Medium_Secondary_IconRectangle:I = 0x7f1401b4

.field public static DSButton_Medium_Secondary_IconRight:I = 0x7f1401b5

.field public static DSButton_Medium_Tertiary:I = 0x7f1401b6

.field public static DSButton_Medium_Tertiary_IconCircle:I = 0x7f1401b7

.field public static DSButton_Medium_Tertiary_IconLeft:I = 0x7f1401b8

.field public static DSButton_Medium_Tertiary_IconRectangle:I = 0x7f1401b9

.field public static DSButton_Medium_Tertiary_IconRight:I = 0x7f1401ba

.field public static DSButton_Medium_Warning:I = 0x7f1401bb

.field public static DSButton_Medium_WarningStatic:I = 0x7f1401c0

.field public static DSButton_Medium_WarningStatic_IconCircle:I = 0x7f1401c1

.field public static DSButton_Medium_WarningStatic_IconLeft:I = 0x7f1401c2

.field public static DSButton_Medium_WarningStatic_IconRectangle:I = 0x7f1401c3

.field public static DSButton_Medium_WarningStatic_IconRight:I = 0x7f1401c4

.field public static DSButton_Medium_Warning_IconCircle:I = 0x7f1401bc

.field public static DSButton_Medium_Warning_IconLeft:I = 0x7f1401bd

.field public static DSButton_Medium_Warning_IconRectangle:I = 0x7f1401be

.field public static DSButton_Medium_Warning_IconRight:I = 0x7f1401bf

.field public static DSButton_Small:I = 0x7f1401c5

.field public static DSButton_Small_Commerce:I = 0x7f1401c6

.field public static DSButton_Small_Commerce_IconCircle:I = 0x7f1401c7

.field public static DSButton_Small_Commerce_IconLeft:I = 0x7f1401c8

.field public static DSButton_Small_Commerce_IconRectangle:I = 0x7f1401c9

.field public static DSButton_Small_Commerce_IconRight:I = 0x7f1401ca

.field public static DSButton_Small_Primary:I = 0x7f1401cb

.field public static DSButton_Small_Primary_IconCircle:I = 0x7f1401cc

.field public static DSButton_Small_Primary_IconLeft:I = 0x7f1401cd

.field public static DSButton_Small_Primary_IconRectangle:I = 0x7f1401ce

.field public static DSButton_Small_Primary_IconRight:I = 0x7f1401cf

.field public static DSButton_Small_Quaternary:I = 0x7f1401d0

.field public static DSButton_Small_Quaternary_IconCircle:I = 0x7f1401d1

.field public static DSButton_Small_Quaternary_IconLeft:I = 0x7f1401d2

.field public static DSButton_Small_Quaternary_IconRectangle:I = 0x7f1401d3

.field public static DSButton_Small_Quaternary_IconRight:I = 0x7f1401d4

.field public static DSButton_Small_Secondary:I = 0x7f1401d5

.field public static DSButton_Small_Secondary_IconCircle:I = 0x7f1401d6

.field public static DSButton_Small_Secondary_IconLeft:I = 0x7f1401d7

.field public static DSButton_Small_Secondary_IconRectangle:I = 0x7f1401d8

.field public static DSButton_Small_Secondary_IconRight:I = 0x7f1401d9

.field public static DSButton_Small_Tertiary:I = 0x7f1401da

.field public static DSButton_Small_Tertiary_IconCircle:I = 0x7f1401db

.field public static DSButton_Small_Tertiary_IconLeft:I = 0x7f1401dc

.field public static DSButton_Small_Tertiary_IconRectangle:I = 0x7f1401dd

.field public static DSButton_Small_Tertiary_IconRight:I = 0x7f1401de

.field public static DSButton_Small_Warning:I = 0x7f1401df

.field public static DSButton_Small_WarningStatic:I = 0x7f1401e4

.field public static DSButton_Small_WarningStatic_IconCircle:I = 0x7f1401e5

.field public static DSButton_Small_WarningStatic_IconLeft:I = 0x7f1401e6

.field public static DSButton_Small_WarningStatic_IconRectangle:I = 0x7f1401e7

.field public static DSButton_Small_WarningStatic_IconRight:I = 0x7f1401e8

.field public static DSButton_Small_Warning_IconCircle:I = 0x7f1401e0

.field public static DSButton_Small_Warning_IconLeft:I = 0x7f1401e1

.field public static DSButton_Small_Warning_IconRectangle:I = 0x7f1401e2

.field public static DSButton_Small_Warning_IconRight:I = 0x7f1401e3

.field public static DSChip:I = 0x7f1401e9

.field public static DSChip_Overlay:I = 0x7f1401ea

.field public static DSChip_Overlay_Value:I = 0x7f1401eb

.field public static DSChip_Primary:I = 0x7f1401ec

.field public static DSChip_Primary_Value:I = 0x7f1401ed

.field public static DSChip_Secondary:I = 0x7f1401ee

.field public static DSChip_Secondary_Value:I = 0x7f1401ef

.field public static DSChip_Tertiary:I = 0x7f1401f0

.field public static DSChip_Tertiary_Value:I = 0x7f1401f1

.field public static LordBetting_Light:I = 0x7f14022c

.field public static ShapeAppearance_Circle:I = 0x7f140295

.field public static ShapeAppearance_Radius10:I = 0x7f1402c1

.field public static ShapeAppearance_Radius12:I = 0x7f1402c2

.field public static ShapeAppearance_Radius16:I = 0x7f1402c3

.field public static ShapeAppearance_Radius20:I = 0x7f1402c4

.field public static ShapeAppearance_Radius24:I = 0x7f1402c5

.field public static ShapeAppearance_Radius4:I = 0x7f1402c6

.field public static ShapeAppearance_Radius6:I = 0x7f1402c7

.field public static ShapeAppearance_Radius8:I = 0x7f1402c8

.field public static ShapeAppearance_RadiusTop0Bottom12:I = 0x7f1402c9

.field public static ShapeAppearance_RadiusTop16Bottom0:I = 0x7f1402ca

.field public static ShapeAppearance_RadiusTop16Bottom12:I = 0x7f1402cb

.field public static ShapeAppearance_RadiusTop20:I = 0x7f1402cc

.field public static ShapeAppearance_RadiusTopRightBottomRight16:I = 0x7f1402cd

.field public static ShapeAppearance_SearchField_TextInputLayout:I = 0x7f1402d2

.field public static TextAppearance_Bold:I = 0x7f140371

.field public static TextAppearance_Caption_Bold_Caps_M:I = 0x7f140372

.field public static TextAppearance_Caption_Bold_L:I = 0x7f140373

.field public static TextAppearance_Caption_Bold_M:I = 0x7f140374

.field public static TextAppearance_Caption_Bold_S:I = 0x7f140375

.field public static TextAppearance_Caption_Medium_Caps_M:I = 0x7f140376

.field public static TextAppearance_Caption_Medium_L:I = 0x7f140377

.field public static TextAppearance_Caption_Medium_M:I = 0x7f140378

.field public static TextAppearance_Caption_Medium_S:I = 0x7f140379

.field public static TextAppearance_Caption_Regular_L:I = 0x7f14037a

.field public static TextAppearance_Caption_Regular_M:I = 0x7f14037b

.field public static TextAppearance_Headline_Bold:I = 0x7f14038c

.field public static TextAppearance_Headline_Medium:I = 0x7f14038d

.field public static TextAppearance_Headline_Regular:I = 0x7f14038e

.field public static TextAppearance_Medium:I = 0x7f1403c4

.field public static TextAppearance_Regular:I = 0x7f1403c5

.field public static TextAppearance_Text_Bold:I = 0x7f1403d3

.field public static TextAppearance_Text_Medium:I = 0x7f1403d4

.field public static TextAppearance_Text_Regular:I = 0x7f1403d5

.field public static TextAppearance_Title_Bold_2XL:I = 0x7f1403d6

.field public static TextAppearance_Title_Bold_L:I = 0x7f1403d7

.field public static TextAppearance_Title_Bold_M:I = 0x7f1403d8

.field public static TextAppearance_Title_Bold_S:I = 0x7f1403d9

.field public static TextAppearance_Title_Bold_XL:I = 0x7f1403da

.field public static TextAppearance_Title_Medium_L:I = 0x7f1403db

.field public static TextAppearance_Title_Medium_M:I = 0x7f1403dc

.field public static TextAppearance_Title_Medium_S:I = 0x7f1403dd

.field public static TextAppearance_Title_Medium_XL:I = 0x7f1403de

.field public static TextAppearance_Title_Regular_M:I = 0x7f1403df

.field public static TextAppearance_Title_Regular_S:I = 0x7f1403e0

.field public static TextStyle_Caption_Bold_Caps_M:I = 0x7f1403e4

.field public static TextStyle_Caption_Bold_Caps_M_Primary:I = 0x7f1403e5

.field public static TextStyle_Caption_Bold_Caps_M_TextPrimary:I = 0x7f1403e6

.field public static TextStyle_Caption_Bold_Caps_M_TextSecondary:I = 0x7f1403e7

.field public static TextStyle_Caption_Bold_L:I = 0x7f1403e8

.field public static TextStyle_Caption_Bold_L_Primary:I = 0x7f1403e9

.field public static TextStyle_Caption_Bold_L_StaticWhite:I = 0x7f1403ea

.field public static TextStyle_Caption_Bold_L_TextPrimary:I = 0x7f1403eb

.field public static TextStyle_Caption_Bold_M:I = 0x7f1403ec

.field public static TextStyle_Caption_Bold_M_Primary:I = 0x7f1403ed

.field public static TextStyle_Caption_Bold_M_TextPrimary:I = 0x7f1403ee

.field public static TextStyle_Caption_Bold_S:I = 0x7f1403ef

.field public static TextStyle_Caption_Bold_S_Primary:I = 0x7f1403f0

.field public static TextStyle_Caption_Bold_S_PrimaryForeground:I = 0x7f1403f1

.field public static TextStyle_Caption_Bold_S_StaticBlack:I = 0x7f1403f2

.field public static TextStyle_Caption_Bold_S_StaticWhite:I = 0x7f1403f3

.field public static TextStyle_Caption_Bold_S_TextPrimary:I = 0x7f1403f4

.field public static TextStyle_Caption_Medium_Caps_M:I = 0x7f1403f5

.field public static TextStyle_Caption_Medium_Caps_M_TextPrimary:I = 0x7f1403f6

.field public static TextStyle_Caption_Medium_L:I = 0x7f1403f7

.field public static TextStyle_Caption_Medium_L_Primary:I = 0x7f1403f8

.field public static TextStyle_Caption_Medium_L_Secondary:I = 0x7f1403f9

.field public static TextStyle_Caption_Medium_L_Shrink:I = 0x7f1403fa

.field public static TextStyle_Caption_Medium_L_Shrink_TextPrimary:I = 0x7f1403fb

.field public static TextStyle_Caption_Medium_L_StaticGreen:I = 0x7f1403fc

.field public static TextStyle_Caption_Medium_L_StaticWhite:I = 0x7f1403fd

.field public static TextStyle_Caption_Medium_L_TabItem:I = 0x7f1403fe

.field public static TextStyle_Caption_Medium_L_TextPrimary:I = 0x7f1403ff

.field public static TextStyle_Caption_Medium_M:I = 0x7f140400

.field public static TextStyle_Caption_Medium_M_Primary:I = 0x7f140401

.field public static TextStyle_Caption_Medium_M_Secondary:I = 0x7f140402

.field public static TextStyle_Caption_Medium_M_TabItem:I = 0x7f140403

.field public static TextStyle_Caption_Medium_M_TextPrimary:I = 0x7f140404

.field public static TextStyle_Caption_Medium_S:I = 0x7f140405

.field public static TextStyle_Caption_Medium_S_Primary:I = 0x7f140406

.field public static TextStyle_Caption_Medium_S_TextPrimary:I = 0x7f140407

.field public static TextStyle_Caption_Regular_L:I = 0x7f140408

.field public static TextStyle_Caption_Regular_L_Primary:I = 0x7f140409

.field public static TextStyle_Caption_Regular_L_Secondary:I = 0x7f14040a

.field public static TextStyle_Caption_Regular_L_SecondarySelector:I = 0x7f14040b

.field public static TextStyle_Caption_Regular_L_StaticGreen:I = 0x7f14040c

.field public static TextStyle_Caption_Regular_L_StaticWhite:I = 0x7f14040d

.field public static TextStyle_Caption_Regular_L_StaticWhite80:I = 0x7f14040e

.field public static TextStyle_Caption_Regular_L_TextPrimary:I = 0x7f14040f

.field public static TextStyle_Caption_Regular_L_Warning:I = 0x7f140410

.field public static TextStyle_Caption_Regular_M:I = 0x7f140411

.field public static TextStyle_Caption_Regular_M_Primary:I = 0x7f140412

.field public static TextStyle_Caption_Regular_M_Secondary:I = 0x7f140413

.field public static TextStyle_Caption_Regular_M_StaticWhite:I = 0x7f140414

.field public static TextStyle_Caption_Regular_M_TextPrimary:I = 0x7f140415

.field public static TextStyle_Cells_Right_Label:I = 0x7f140416

.field public static TextStyle_Cells_Right_MediumLabel:I = 0x7f140417

.field public static TextStyle_Cells_Right_Status:I = 0x7f140418

.field public static TextStyle_Headline_Bold:I = 0x7f140419

.field public static TextStyle_Headline_Bold_Primary:I = 0x7f14041a

.field public static TextStyle_Headline_Bold_Secondary:I = 0x7f14041b

.field public static TextStyle_Headline_Bold_Shrink_Alt:I = 0x7f14041c

.field public static TextStyle_Headline_Bold_Shrink_Alt_Primary:I = 0x7f14041d

.field public static TextStyle_Headline_Bold_Shrink_Alt_TextPrimary:I = 0x7f14041e

.field public static TextStyle_Headline_Bold_StaticWhite:I = 0x7f14041f

.field public static TextStyle_Headline_Bold_TextPrimary:I = 0x7f140420

.field public static TextStyle_Headline_Medium:I = 0x7f140421

.field public static TextStyle_Headline_Medium_Primary:I = 0x7f140422

.field public static TextStyle_Headline_Medium_Shrink:I = 0x7f140423

.field public static TextStyle_Headline_Medium_Shrink_StaticWhite:I = 0x7f140424

.field public static TextStyle_Headline_Medium_Shrink_TextPrimary:I = 0x7f140425

.field public static TextStyle_Headline_Medium_StaticWhite:I = 0x7f140426

.field public static TextStyle_Headline_Medium_TextPrimary:I = 0x7f140427

.field public static TextStyle_Headline_Regular:I = 0x7f140428

.field public static TextStyle_Headline_Regular_Primary:I = 0x7f140429

.field public static TextStyle_Headline_Regular_Secondary:I = 0x7f14042a

.field public static TextStyle_Headline_Regular_StaticWhite:I = 0x7f14042b

.field public static TextStyle_Headline_Regular_TextPrimary:I = 0x7f14042c

.field public static TextStyle_Text_Bold:I = 0x7f14042d

.field public static TextStyle_Text_Bold_Coefficient:I = 0x7f14042e

.field public static TextStyle_Text_Bold_Primary:I = 0x7f14042f

.field public static TextStyle_Text_Bold_StaticWhite:I = 0x7f140430

.field public static TextStyle_Text_Bold_TextPrimary:I = 0x7f140431

.field public static TextStyle_Text_Medium:I = 0x7f140432

.field public static TextStyle_Text_Medium_Primary:I = 0x7f140433

.field public static TextStyle_Text_Medium_Secondary:I = 0x7f140434

.field public static TextStyle_Text_Medium_Shrink:I = 0x7f140435

.field public static TextStyle_Text_Medium_Shrink_TextPrimary:I = 0x7f140436

.field public static TextStyle_Text_Medium_StaticBlack:I = 0x7f140437

.field public static TextStyle_Text_Medium_StaticWhite:I = 0x7f140438

.field public static TextStyle_Text_Medium_TextPrimary:I = 0x7f140439

.field public static TextStyle_Text_Regular:I = 0x7f14043a

.field public static TextStyle_Text_Regular_Primary:I = 0x7f14043b

.field public static TextStyle_Text_Regular_Secondary:I = 0x7f14043c

.field public static TextStyle_Text_Regular_StaticWhite:I = 0x7f14043d

.field public static TextStyle_Text_Regular_StaticWhite80:I = 0x7f14043e

.field public static TextStyle_Text_Regular_TextPrimary:I = 0x7f14043f

.field public static TextStyle_Title_Bold_2XL:I = 0x7f140440

.field public static TextStyle_Title_Bold_2XL_TextPrimary:I = 0x7f140441

.field public static TextStyle_Title_Bold_2XL_TextPrimary_Shrink:I = 0x7f140442

.field public static TextStyle_Title_Bold_L:I = 0x7f140443

.field public static TextStyle_Title_Bold_L_Shrink:I = 0x7f140444

.field public static TextStyle_Title_Bold_L_Shrink_StaticWhite:I = 0x7f140445

.field public static TextStyle_Title_Bold_L_TextPrimary:I = 0x7f140446

.field public static TextStyle_Title_Bold_M:I = 0x7f140447

.field public static TextStyle_Title_Bold_M_Shrink:I = 0x7f140448

.field public static TextStyle_Title_Bold_M_Shrink_Alt:I = 0x7f140449

.field public static TextStyle_Title_Bold_M_Shrink_Alt_TextPrimary:I = 0x7f14044a

.field public static TextStyle_Title_Bold_M_Shrink_Alt_TextStaticWhite:I = 0x7f14044b

.field public static TextStyle_Title_Bold_M_Shrink_StaticWhite:I = 0x7f14044c

.field public static TextStyle_Title_Bold_M_Shrink_TextPrimary:I = 0x7f14044d

.field public static TextStyle_Title_Bold_M_StaticWhite:I = 0x7f14044e

.field public static TextStyle_Title_Bold_M_TextBackground:I = 0x7f14044f

.field public static TextStyle_Title_Bold_M_TextPrimary:I = 0x7f140450

.field public static TextStyle_Title_Bold_S:I = 0x7f140451

.field public static TextStyle_Title_Bold_S_TextPrimary:I = 0x7f140452

.field public static TextStyle_Title_Bold_XL:I = 0x7f140453

.field public static TextStyle_Title_Bold_XL_Shrink:I = 0x7f140454

.field public static TextStyle_Title_Bold_XL_Shrink_StaticWhite:I = 0x7f140455

.field public static TextStyle_Title_Bold_XL_StaticWhite:I = 0x7f140456

.field public static TextStyle_Title_Bold_XL_TextPrimary:I = 0x7f140457

.field public static TextStyle_Title_Medium_L:I = 0x7f140458

.field public static TextStyle_Title_Medium_L_TextPrimary:I = 0x7f140459

.field public static TextStyle_Title_Medium_M:I = 0x7f14045a

.field public static TextStyle_Title_Medium_M_Primary:I = 0x7f14045b

.field public static TextStyle_Title_Medium_M_Shrink:I = 0x7f14045c

.field public static TextStyle_Title_Medium_M_Shrink_Secondary:I = 0x7f14045d

.field public static TextStyle_Title_Medium_M_Shrink_TextPrimary:I = 0x7f14045e

.field public static TextStyle_Title_Medium_M_StaticWhite:I = 0x7f14045f

.field public static TextStyle_Title_Medium_M_TextPrimary:I = 0x7f140460

.field public static TextStyle_Title_Medium_S:I = 0x7f140461

.field public static TextStyle_Title_Medium_S_TextPrimary:I = 0x7f140462

.field public static TextStyle_Title_Medium_XL:I = 0x7f140463

.field public static TextStyle_Title_Medium_XL_Secondary:I = 0x7f140464

.field public static TextStyle_Title_Medium_XL_Shrink:I = 0x7f140465

.field public static TextStyle_Title_Medium_XL_Shrink_TextPrimary:I = 0x7f140466

.field public static TextStyle_Title_Medium_XL_TextPrimary:I = 0x7f140467

.field public static TextStyle_Title_Regular_M:I = 0x7f140468

.field public static TextStyle_Title_Regular_M_TextPrimary:I = 0x7f140469

.field public static TextStyle_Title_Regular_S:I = 0x7f14046a

.field public static TextStyle_Title_Regular_S_TextPrimary:I = 0x7f14046b

.field public static ThemeOverlay_SearchField_TextInputLayout:I = 0x7f140567

.field public static ThemeOverlay_SearchField_TextInputLayout_Overlay:I = 0x7f140568

.field public static ThemeOverlay_Tabs:I = 0x7f140569

.field public static ThemeOverlay_TextInputLayout_Basic:I = 0x7f14056a

.field public static ThemeOverlay_TextInputLayout_Filled:I = 0x7f14056b

.field public static ThemeOverlay_TextInputLayout_Static:I = 0x7f14056c

.field public static ThemeOverlay_Toolbar:I = 0x7f14056d

.field public static ThemeOverlay_Toolbar_Games:I = 0x7f14056e

.field public static ThemeOverlay_Toolbar_Static:I = 0x7f14056f

.field public static Tonybet_Light:I = 0x7f140570

.field public static UiKit:I = 0x7f140572

.field public static UiKit_Dark:I = 0x7f140573

.field public static UiKit_Light:I = 0x7f140574

.field public static UiKit_Night:I = 0x7f140575

.field public static Widget_Accordion:I = 0x7f140583

.field public static Widget_Accordion_Clear:I = 0x7f140584

.field public static Widget_Accordion_Primary:I = 0x7f140585

.field public static Widget_Accordion_Secondary:I = 0x7f140586

.field public static Widget_AccountInfo_Large:I = 0x7f140587

.field public static Widget_AccountInfo_Large_WithButton:I = 0x7f140588

.field public static Widget_AccountInfo_Small:I = 0x7f140589

.field public static Widget_AccountInfo_Small_WithButton:I = 0x7f14058a

.field public static Widget_AccountInfo_Small_WithButton_Right:I = 0x7f14058b

.field public static Widget_AccountSelection_Fivefold:I = 0x7f14058c

.field public static Widget_AccountSelection_Primary:I = 0x7f14058d

.field public static Widget_AccountSelection_Quaternary:I = 0x7f14058e

.field public static Widget_AccountSelection_Secondary:I = 0x7f14058f

.field public static Widget_AccountSelection_Tertiary:I = 0x7f140590

.field public static Widget_Badge:I = 0x7f140640

.field public static Widget_Badge_Authenticator:I = 0x7f140641

.field public static Widget_Badge_Championship_New:I = 0x7f140642

.field public static Widget_Badge_Championship_Popular:I = 0x7f140643

.field public static Widget_Badge_Counter:I = 0x7f140644

.field public static Widget_Badge_Custom:I = 0x7f140645

.field public static Widget_Badge_Dot:I = 0x7f140646

.field public static Widget_Badge_Live:I = 0x7f140647

.field public static Widget_Badge_Live_Header:I = 0x7f140648

.field public static Widget_Badge_Market:I = 0x7f140649

.field public static Widget_Badge_Market_Block:I = 0x7f14064a

.field public static Widget_Badge_Market_Coupon:I = 0x7f14064b

.field public static Widget_Badge_Market_Popular:I = 0x7f14064c

.field public static Widget_Badge_Market_Track:I = 0x7f14064d

.field public static Widget_Badge_Prominent_L:I = 0x7f14064e

.field public static Widget_Badge_Prominent_S:I = 0x7f14064f

.field public static Widget_Badge_Status:I = 0x7f140650

.field public static Widget_BalanceViewGroup:I = 0x7f140651

.field public static Widget_BannerBonuses:I = 0x7f140652

.field public static Widget_BannerCollection_CardHorizontal:I = 0x7f140653

.field public static Widget_BannerCollection_RectangleHorizontal:I = 0x7f140654

.field public static Widget_BannerCollection_RectangleVertical:I = 0x7f140655

.field public static Widget_BannerCollection_SquareL:I = 0x7f140656

.field public static Widget_BannerCollection_SquareS:I = 0x7f140657

.field public static Widget_BottomBar:I = 0x7f140658

.field public static Widget_Cell:I = 0x7f140659

.field public static Widget_Cell_Left_Call:I = 0x7f14065a

.field public static Widget_Cell_Left_Icon:I = 0x7f14065b

.field public static Widget_Cell_Left_Icon_Back:I = 0x7f14065c

.field public static Widget_Cell_Left_Icon_Back_StaticWhite:I = 0x7f14065d

.field public static Widget_Cell_Left_Icon_Flag:I = 0x7f14065e

.field public static Widget_Cell_Left_Icon_Flag_Dynamic:I = 0x7f14065f

.field public static Widget_Cell_Left_Icon_Flag_Static:I = 0x7f140660

.field public static Widget_Cell_Left_Icon_Tintless:I = 0x7f140661

.field public static Widget_Cell_Left_PasswordRequirement:I = 0x7f140662

.field public static Widget_Cell_Menu:I = 0x7f140663

.field public static Widget_Cell_MenuCompact:I = 0x7f140665

.field public static Widget_Cell_Menu_Banner_Large:I = 0x7f140664

.field public static Widget_Cell_Middle:I = 0x7f140666

.field public static Widget_Cell_Middle_Subtitle_Title_L:I = 0x7f140667

.field public static Widget_Cell_Middle_Subtitle_Title_L_Caption:I = 0x7f140668

.field public static Widget_Cell_Middle_Title:I = 0x7f140669

.field public static Widget_Cell_Middle_Title_L:I = 0x7f14066a

.field public static Widget_Cell_Middle_Title_S:I = 0x7f14066b

.field public static Widget_Cell_Middle_Title_S_Foreground:I = 0x7f14066c

.field public static Widget_Cell_Middle_Title_S_Overlay:I = 0x7f14066d

.field public static Widget_Cell_Middle_Title_S_Sport:I = 0x7f14066e

.field public static Widget_Cell_Middle_Title_S_Static:I = 0x7f14066f

.field public static Widget_Cell_Right:I = 0x7f140670

.field public static Widget_Cell_Right_Accordion:I = 0x7f140671

.field public static Widget_Cell_Right_Banner:I = 0x7f140672

.field public static Widget_Cell_Right_BaseLabel:I = 0x7f140673

.field public static Widget_Cell_Right_Button:I = 0x7f140674

.field public static Widget_Cell_Right_Button_Call:I = 0x7f140675

.field public static Widget_Cell_Right_Button_Icon:I = 0x7f140676

.field public static Widget_Cell_Right_Button_Label:I = 0x7f140677

.field public static Widget_Cell_Right_Counter:I = 0x7f140678

.field public static Widget_Cell_Right_Counter_Icon:I = 0x7f140679

.field public static Widget_Cell_Right_DragAndDrop:I = 0x7f14067a

.field public static Widget_Cell_Right_DragAndDrop_Button:I = 0x7f14067b

.field public static Widget_Cell_Right_DragAndDrop_CheckBox:I = 0x7f14067c

.field public static Widget_Cell_Right_Icon:I = 0x7f14067d

.field public static Widget_Cell_Right_Label:I = 0x7f14067e

.field public static Widget_Cell_Right_Label_Icon:I = 0x7f14067f

.field public static Widget_Cell_Right_ListCheckBox:I = 0x7f140680

.field public static Widget_Cell_Right_MediumLabel:I = 0x7f140681

.field public static Widget_Cell_Right_RadioButton:I = 0x7f140682

.field public static Widget_Cell_Right_Status:I = 0x7f140683

.field public static Widget_Cell_Right_Switch:I = 0x7f140684

.field public static Widget_Cell_Settings:I = 0x7f140685

.field public static Widget_Cell_Shimmers:I = 0x7f140686

.field public static Widget_Cell_Shimmers_Menu:I = 0x7f140687

.field public static Widget_Cell_Shimmers_Settings:I = 0x7f140688

.field public static Widget_Checkbox:I = 0x7f140689

.field public static Widget_Chips_Commerce:I = 0x7f14068a

.field public static Widget_Chips_Overlay:I = 0x7f14068b

.field public static Widget_Chips_Primary:I = 0x7f14068c

.field public static Widget_Chips_Quaternary:I = 0x7f14068d

.field public static Widget_Chips_Secondary:I = 0x7f14068e

.field public static Widget_Chips_Tertiary:I = 0x7f14068f

.field public static Widget_Counter:I = 0x7f140692

.field public static Widget_Counter_BadgeRounded:I = 0x7f140693

.field public static Widget_Counter_Clear:I = 0x7f140694

.field public static Widget_Counter_Primary:I = 0x7f140695

.field public static Widget_Counter_Red:I = 0x7f140696

.field public static Widget_Counter_RedCard_Static:I = 0x7f140697

.field public static Widget_Counter_Rounded:I = 0x7f140698

.field public static Widget_Counter_Secondary:I = 0x7f140699

.field public static Widget_Counter_Secondary_Accordion:I = 0x7f14069a

.field public static Widget_Counter_Stroke:I = 0x7f14069b

.field public static Widget_Counter_Tertiary:I = 0x7f14069c

.field public static Widget_Counter_White_Static:I = 0x7f14069d

.field public static Widget_DSCheckbox:I = 0x7f14069e

.field public static Widget_DSListCheckbox:I = 0x7f14069f

.field public static Widget_DSListCheckbox_Overlay:I = 0x7f1406a0

.field public static Widget_DSListCheckbox_Primary:I = 0x7f1406a1

.field public static Widget_DSTextField:I = 0x7f1406a2

.field public static Widget_DSTextField_Basic:I = 0x7f1406a3

.field public static Widget_DSTextField_Basic_Chevron:I = 0x7f1406a4

.field public static Widget_DSTextField_Basic_Chevron_Editable:I = 0x7f1406a5

.field public static Widget_DSTextField_Basic_Chevron_PhoneTextFieldCode:I = 0x7f1406a6

.field public static Widget_DSTextField_Basic_Icon:I = 0x7f1406a7

.field public static Widget_DSTextField_Basic_Multiline:I = 0x7f1406a8

.field public static Widget_DSTextField_Basic_Multiline_Chevron:I = 0x7f1406a9

.field public static Widget_DSTextField_Basic_Multiline_Chevron_Editable:I = 0x7f1406aa

.field public static Widget_DSTextField_Basic_Multiline_Icon:I = 0x7f1406ab

.field public static Widget_DSTextField_Basic_PhoneTextFieldCode:I = 0x7f1406ac

.field public static Widget_DSTextField_Basic_PhoneTextFieldPhone:I = 0x7f1406ad

.field public static Widget_DSTextField_Filled:I = 0x7f1406ae

.field public static Widget_DSTextField_Filled_Chevron:I = 0x7f1406af

.field public static Widget_DSTextField_Filled_Chevron_Editable:I = 0x7f1406b0

.field public static Widget_DSTextField_Filled_Icon:I = 0x7f1406b1

.field public static Widget_DSTextField_Filled_Multiline:I = 0x7f1406b2

.field public static Widget_DSTextField_Filled_Multiline_Chevron:I = 0x7f1406b3

.field public static Widget_DSTextField_Filled_Multiline_Chevron_Editable:I = 0x7f1406b4

.field public static Widget_DSTextField_Filled_Multiline_Icon:I = 0x7f1406b5

.field public static Widget_DSTextField_Filled_Multiline_Stepper:I = 0x7f1406b6

.field public static Widget_DSTextField_Filled_Stepper:I = 0x7f1406b7

.field public static Widget_DSTextField_Password:I = 0x7f1406b8

.field public static Widget_Dialog_AlertDialog:I = 0x7f1406c4

.field public static Widget_Dialog_AlertDialogAnimation:I = 0x7f1406c5

.field public static Widget_Dialog_AlertDialogExitAnimation:I = 0x7f1406c6

.field public static Widget_Dialog_AlertDialogSecondary:I = 0x7f1406c7

.field public static Widget_DsPromoStoreCollection:I = 0x7f1406c8

.field public static Widget_DsPromoStoreCollectionItem:I = 0x7f1406cb

.field public static Widget_DsPromoStoreCollection_Horizontal:I = 0x7f1406c9

.field public static Widget_DsPromoStoreCollection_Vertical:I = 0x7f1406ca

.field public static Widget_Footer:I = 0x7f1406ed

.field public static Widget_Header:I = 0x7f1406f3

.field public static Widget_Header_MarketStatic:I = 0x7f1406f4

.field public static Widget_Loader:I = 0x7f1406f5

.field public static Widget_Market:I = 0x7f1406f6

.field public static Widget_Market_Button:I = 0x7f1406f7

.field public static Widget_Market_Button_Blocked:I = 0x7f1406f8

.field public static Widget_Market_Button_ShowMore:I = 0x7f1406f9

.field public static Widget_Market_Coupon_Blocked:I = 0x7f1406fa

.field public static Widget_Market_Coupon_Default:I = 0x7f1406fb

.field public static Widget_Market_Coupon_Dependent:I = 0x7f1406fc

.field public static Widget_Market_Coupon_Disable:I = 0x7f1406fd

.field public static Widget_Market_Coupon_Unavailable:I = 0x7f1406fe

.field public static Widget_Market_Dynamic:I = 0x7f1406ff

.field public static Widget_Market_Static:I = 0x7f140700

.field public static Widget_NavigationBarItem:I = 0x7f14082a

.field public static Widget_PageControl:I = 0x7f14082b

.field public static Widget_PageControl_Overlay:I = 0x7f14082c

.field public static Widget_PageControl_Overlay_Background:I = 0x7f14082d

.field public static Widget_PageControl_Primary:I = 0x7f14082e

.field public static Widget_PageControl_Primary_Background:I = 0x7f14082f

.field public static Widget_PasswordRequirement:I = 0x7f140830

.field public static Widget_PasswordRequirement_Dynamic:I = 0x7f140831

.field public static Widget_PasswordRequirement_Dynamic_Background:I = 0x7f140832

.field public static Widget_PasswordRequirement_Dynamic_ContentBackground:I = 0x7f140833

.field public static Widget_PasswordRequirement_Static:I = 0x7f140834

.field public static Widget_PasswordRequirement_Static_Background:I = 0x7f140835

.field public static Widget_PasswordRequirement_Static_ContentBackground:I = 0x7f140836

.field public static Widget_RadioButton:I = 0x7f140837

.field public static Widget_RollingCalendar:I = 0x7f140838

.field public static Widget_RollingCalendar_Date:I = 0x7f140839

.field public static Widget_RollingCalendar_Date_Background:I = 0x7f14083a

.field public static Widget_RollingCalendar_Date_ContentBackground:I = 0x7f14083b

.field public static Widget_RollingCalendar_Item:I = 0x7f14083c

.field public static Widget_RollingCalendar_Year:I = 0x7f14083d

.field public static Widget_RollingCalendar_Year_Background:I = 0x7f14083e

.field public static Widget_RollingCalendar_Year_ContentBackground:I = 0x7f14083f

.field public static Widget_Score_Large:I = 0x7f140889

.field public static Widget_Score_Large_Static:I = 0x7f14088a

.field public static Widget_Score_Small:I = 0x7f14088b

.field public static Widget_Score_Small_Static:I = 0x7f14088c

.field public static Widget_SearchField:I = 0x7f14088f

.field public static Widget_SearchField_Static:I = 0x7f140890

.field public static Widget_SearchField_TextInputLayout:I = 0x7f140891

.field public static Widget_SearchField_TextInputLayout_Overlay:I = 0x7f140892

.field public static Widget_SegmentedGroup:I = 0x7f140893

.field public static Widget_SegmentedGroup_Camera:I = 0x7f140894

.field public static Widget_SegmentedGroup_Static:I = 0x7f140896

.field public static Widget_SegmentedItem:I = 0x7f140897

.field public static Widget_SegmentedItem_Camera:I = 0x7f140898

.field public static Widget_SegmentedItem_Static:I = 0x7f14089a

.field public static Widget_Separator:I = 0x7f14089b

.field public static Widget_Separator_Cell:I = 0x7f14089c

.field public static Widget_Separator_Separator60:I = 0x7f14089d

.field public static Widget_Separator_Separator60_Vertical:I = 0x7f14089e

.field public static Widget_Separator_Shimmer:I = 0x7f14089f

.field public static Widget_Separator_Short60:I = 0x7f1408a0

.field public static Widget_Separator_Static_White:I = 0x7f1408a1

.field public static Widget_Separator_Static_White20:I = 0x7f1408a2

.field public static Widget_Separator_Static_White40:I = 0x7f1408a3

.field public static Widget_Shimmer:I = 0x7f1408a4

.field public static Widget_Shimmer_Games:I = 0x7f1408a5

.field public static Widget_Shimmer_Primary:I = 0x7f1408a6

.field public static Widget_Shimmer_Static_Dark:I = 0x7f1408a7

.field public static Widget_Shimmer_Static_Light:I = 0x7f1408a8

.field public static Widget_Single_GameHorizontalItem:I = 0x7f1408a9

.field public static Widget_SnackBar_ColoredBackgroundNoIcon:I = 0x7f1408aa

.field public static Widget_SnackBar_ColoredBackgroundNoIcon_Info:I = 0x7f1408ab

.field public static Widget_SnackBar_ColoredBackgroundNoIcon_Success:I = 0x7f1408ac

.field public static Widget_SnackBar_ColoredBackgroundNoIcon_Warning:I = 0x7f1408ad

.field public static Widget_SnackBar_Monochrome:I = 0x7f1408ae

.field public static Widget_SnackBar_Monochrome_MonochromeNoIcon:I = 0x7f1408af

.field public static Widget_SnackBar_Monochrome_MonochromeWithColoredIcon:I = 0x7f1408b0

.field public static Widget_SnackBar_Monochrome_MonochromeWithColoredIcon_Info:I = 0x7f1408b1

.field public static Widget_SnackBar_Monochrome_MonochromeWithColoredIcon_Success:I = 0x7f1408b2

.field public static Widget_SnackBar_Monochrome_MonochromeWithColoredIcon_Warning:I = 0x7f1408b3

.field public static Widget_SnackBar_Monochrome_MonochromeWithWhiteIcon:I = 0x7f1408b4

.field public static Widget_SnackBar_Monochrome_MonochromeWithWhiteIcon_Info:I = 0x7f1408b5

.field public static Widget_SnackBar_Monochrome_MonochromeWithWhiteIcon_Success:I = 0x7f1408b6

.field public static Widget_SnackBar_Monochrome_MonochromeWithWhiteIcon_Warning:I = 0x7f1408b7

.field public static Widget_Switch:I = 0x7f1408dd

.field public static Widget_TabBarItem:I = 0x7f1408de

.field public static Widget_TabLayout:I = 0x7f1408df

.field public static Widget_TabLayout_Collapsed:I = 0x7f1408e0

.field public static Widget_Tag:I = 0x7f1408e1

.field public static Widget_Tag_Cyber:I = 0x7f1408e2

.field public static Widget_Tag_RectangularL:I = 0x7f1408e3

.field public static Widget_Tag_RectangularL_BetConstructor:I = 0x7f1408e4

.field public static Widget_Tag_RectangularL_Blue:I = 0x7f1408e5

.field public static Widget_Tag_RectangularL_DarkGray:I = 0x7f1408e6

.field public static Widget_Tag_RectangularL_DarkOrange:I = 0x7f1408e7

.field public static Widget_Tag_RectangularL_DarkOrangeTransparent:I = 0x7f1408e8

.field public static Widget_Tag_RectangularL_DarkPink:I = 0x7f1408e9

.field public static Widget_Tag_RectangularL_Gray:I = 0x7f1408ea

.field public static Widget_Tag_RectangularL_Green:I = 0x7f1408eb

.field public static Widget_Tag_RectangularL_GreenTransparent:I = 0x7f1408ec

.field public static Widget_Tag_RectangularL_Light:I = 0x7f1408ed

.field public static Widget_Tag_RectangularL_Orange:I = 0x7f1408ee

.field public static Widget_Tag_RectangularL_Outlined:I = 0x7f1408ef

.field public static Widget_Tag_RectangularL_Outlined_Blue:I = 0x7f1408f0

.field public static Widget_Tag_RectangularL_Outlined_DarkGray:I = 0x7f1408f1

.field public static Widget_Tag_RectangularL_Outlined_DarkOrange:I = 0x7f1408f2

.field public static Widget_Tag_RectangularL_Outlined_DarkPink:I = 0x7f1408f3

.field public static Widget_Tag_RectangularL_Outlined_Gray:I = 0x7f1408f4

.field public static Widget_Tag_RectangularL_Outlined_Green:I = 0x7f1408f5

.field public static Widget_Tag_RectangularL_Outlined_Orange:I = 0x7f1408f6

.field public static Widget_Tag_RectangularL_Outlined_Pink:I = 0x7f1408f7

.field public static Widget_Tag_RectangularL_Outlined_Primary:I = 0x7f1408f8

.field public static Widget_Tag_RectangularL_Outlined_Purple:I = 0x7f1408f9

.field public static Widget_Tag_RectangularL_Outlined_Red:I = 0x7f1408fa

.field public static Widget_Tag_RectangularL_Outlined_Secondary:I = 0x7f1408fb

.field public static Widget_Tag_RectangularL_Outlined_Teal:I = 0x7f1408fc

.field public static Widget_Tag_RectangularL_Outlined_Violet:I = 0x7f1408fd

.field public static Widget_Tag_RectangularL_Outlined_White:I = 0x7f1408fe

.field public static Widget_Tag_RectangularL_Outlined_Yellow:I = 0x7f1408ff

.field public static Widget_Tag_RectangularL_Pink:I = 0x7f140900

.field public static Widget_Tag_RectangularL_Primary:I = 0x7f140901

.field public static Widget_Tag_RectangularL_Purple:I = 0x7f140902

.field public static Widget_Tag_RectangularL_Red:I = 0x7f140903

.field public static Widget_Tag_RectangularL_RedTransparent:I = 0x7f140904

.field public static Widget_Tag_RectangularL_Secondary:I = 0x7f140905

.field public static Widget_Tag_RectangularL_Teal:I = 0x7f140906

.field public static Widget_Tag_RectangularL_Violet:I = 0x7f140907

.field public static Widget_Tag_RectangularL_White:I = 0x7f140908

.field public static Widget_Tag_RectangularL_WhiteTransparent:I = 0x7f140909

.field public static Widget_Tag_RectangularL_Yellow:I = 0x7f14090a

.field public static Widget_Tag_RectangularS:I = 0x7f14090b

.field public static Widget_Tag_RectangularS_Blue:I = 0x7f14090c

.field public static Widget_Tag_RectangularS_DarkGray:I = 0x7f14090d

.field public static Widget_Tag_RectangularS_DarkOrange:I = 0x7f14090e

.field public static Widget_Tag_RectangularS_DarkOrangeTransparent:I = 0x7f14090f

.field public static Widget_Tag_RectangularS_DarkPink:I = 0x7f140910

.field public static Widget_Tag_RectangularS_Gray:I = 0x7f140911

.field public static Widget_Tag_RectangularS_Green:I = 0x7f140912

.field public static Widget_Tag_RectangularS_GreenTransparent:I = 0x7f140913

.field public static Widget_Tag_RectangularS_Light:I = 0x7f140914

.field public static Widget_Tag_RectangularS_Orange:I = 0x7f140915

.field public static Widget_Tag_RectangularS_Pink:I = 0x7f140916

.field public static Widget_Tag_RectangularS_Primary:I = 0x7f140917

.field public static Widget_Tag_RectangularS_Purple:I = 0x7f140918

.field public static Widget_Tag_RectangularS_Red:I = 0x7f140919

.field public static Widget_Tag_RectangularS_RedTransparent:I = 0x7f14091a

.field public static Widget_Tag_RectangularS_Secondary:I = 0x7f14091b

.field public static Widget_Tag_RectangularS_Teal:I = 0x7f14091c

.field public static Widget_Tag_RectangularS_Violet:I = 0x7f14091d

.field public static Widget_Tag_RectangularS_White:I = 0x7f14091e

.field public static Widget_Tag_RectangularS_WhiteTransparent:I = 0x7f14091f

.field public static Widget_Tag_RectangularS_Yellow:I = 0x7f140920

.field public static Widget_Tag_Rounded:I = 0x7f140921

.field public static Widget_Tag_Rounded_Blue:I = 0x7f140922

.field public static Widget_Tag_Rounded_DarkGray:I = 0x7f140923

.field public static Widget_Tag_Rounded_DarkOrange:I = 0x7f140924

.field public static Widget_Tag_Rounded_DarkPink:I = 0x7f140925

.field public static Widget_Tag_Rounded_Gray:I = 0x7f140926

.field public static Widget_Tag_Rounded_Green:I = 0x7f140927

.field public static Widget_Tag_Rounded_Light:I = 0x7f140928

.field public static Widget_Tag_Rounded_Orange:I = 0x7f140929

.field public static Widget_Tag_Rounded_Pink:I = 0x7f14092a

.field public static Widget_Tag_Rounded_Primary:I = 0x7f14092b

.field public static Widget_Tag_Rounded_Purple:I = 0x7f14092c

.field public static Widget_Tag_Rounded_Red:I = 0x7f14092d

.field public static Widget_Tag_Rounded_Secondary:I = 0x7f14092e

.field public static Widget_Tag_Rounded_Teal:I = 0x7f14092f

.field public static Widget_Tag_Rounded_Violet:I = 0x7f140930

.field public static Widget_Tag_Rounded_White:I = 0x7f140931

.field public static Widget_Tag_Rounded_Yellow:I = 0x7f140932

.field public static Widget_TeamLogo:I = 0x7f140933

.field public static Widget_TeamLogo_Size16:I = 0x7f140934

.field public static Widget_TeamLogo_Size16_Static:I = 0x7f140935

.field public static Widget_TeamLogo_Size20:I = 0x7f140936

.field public static Widget_TeamLogo_Size20_Static:I = 0x7f140937

.field public static Widget_TeamLogo_Size24:I = 0x7f140938

.field public static Widget_TeamLogo_Size24_Static:I = 0x7f140939

.field public static Widget_TeamLogo_Size28:I = 0x7f14093a

.field public static Widget_TeamLogo_Size28_Static:I = 0x7f14093b

.field public static Widget_TeamLogo_Size32:I = 0x7f14093c

.field public static Widget_TeamLogo_Size32_Static:I = 0x7f14093d

.field public static Widget_TeamLogo_Size36:I = 0x7f14093e

.field public static Widget_TeamLogo_Size36_Static:I = 0x7f14093f

.field public static Widget_TeamLogo_Size40:I = 0x7f140940

.field public static Widget_TeamLogo_Size40_Static:I = 0x7f140941

.field public static Widget_TeamLogo_Size44:I = 0x7f140942

.field public static Widget_TeamLogo_Size44_Static:I = 0x7f140943

.field public static Widget_TeamLogo_Size48:I = 0x7f140944

.field public static Widget_TeamLogo_Size48_Static:I = 0x7f140945

.field public static Widget_TeamLogo_Size56:I = 0x7f140946

.field public static Widget_TeamLogo_Size56_Static:I = 0x7f140947

.field public static Widget_TeamLogo_Size64:I = 0x7f140948

.field public static Widget_TeamLogo_Size64_Static:I = 0x7f140949

.field public static Widget_TeamLogo_Size72:I = 0x7f14094a

.field public static Widget_TeamLogo_Size72_Static:I = 0x7f14094b

.field public static Widget_TeamLogo_Size80:I = 0x7f14094c

.field public static Widget_TeamLogo_Size80_Static:I = 0x7f14094d

.field public static Widget_TeamLogo_Size96:I = 0x7f14094e

.field public static Widget_TeamLogo_Size96_Static:I = 0x7f14094f

.field public static Widget_TextInputEditText:I = 0x7f140950

.field public static Widget_TextInputEditText_Basic:I = 0x7f140951

.field public static Widget_TextInputEditText_Filled:I = 0x7f140952

.field public static Widget_TextInputEditText_Static:I = 0x7f140953

.field public static Widget_TextInputLayout:I = 0x7f140954

.field public static Widget_TextInputLayout_Basic:I = 0x7f140955

.field public static Widget_TextInputLayout_Basic_Chevron:I = 0x7f140956

.field public static Widget_TextInputLayout_Basic_Icon:I = 0x7f140957

.field public static Widget_TextInputLayout_Filled:I = 0x7f140958

.field public static Widget_TextInputLayout_Filled_Chevron:I = 0x7f140959

.field public static Widget_TextInputLayout_Filled_Icon:I = 0x7f14095a

.field public static Widget_TextInputLayout_Filled_Stepper:I = 0x7f14095b

.field public static Widget_TextInputLayout_Static:I = 0x7f14095c

.field public static Widget_Timer:I = 0x7f14095d

.field public static Widget_Timer_Decrementing:I = 0x7f14095e

.field public static Widget_Timer_Decrementing_Static:I = 0x7f14095f

.field public static Widget_Timer_Extended:I = 0x7f140960

.field public static Widget_Timer_Extended_Static:I = 0x7f140961

.field public static Widget_Timer_Static:I = 0x7f140962

.field public static Widget_Toolbar:I = 0x7f140963

.field public static Widget_Toolbar_Games:I = 0x7f140964

.field public static Widget_Toolbar_Static:I = 0x7f140965

.field public static Widget_Toolbar_Static_Overlay:I = 0x7f140966

.field public static Widget_VictoryIndicator_Center_Cyber:I = 0x7f140967

.field public static Widget_VictoryIndicator_Center_Primary:I = 0x7f140968

.field public static Widget_VictoryIndicator_Left_Cyber:I = 0x7f140969

.field public static Widget_VictoryIndicator_Left_Primary:I = 0x7f14096a

.field public static Widget_VictoryIndicator_Right_Cyber:I = 0x7f14096b

.field public static Widget_VictoryIndicator_Right_Primary:I = 0x7f14096c

.field public static Widgets_AccountControl:I = 0x7f14096d

.field public static Widgets_AccountControl_Primary:I = 0x7f14096e

.field public static Widgets_AccountControl_Quaternary:I = 0x7f14096f

.field public static Widgets_AccountControl_Quinary:I = 0x7f140970

.field public static Widgets_AccountControl_Secondary:I = 0x7f140971

.field public static Widgets_AccountControl_Senary:I = 0x7f140972

.field public static Widgets_AccountControl_Tertiary:I = 0x7f140973

.field public static Widgets_BasicNavigationBar:I = 0x7f140974

.field public static Widgets_BasicNavigationBar_Pretitle:I = 0x7f140975

.field public static Widgets_BasicNavigationBar_Subtitle:I = 0x7f140976

.field public static Widgets_BasicNavigationBar_Title:I = 0x7f140977

.field public static Widgets_DSNavigationBarButton:I = 0x7f140978

.field public static Widgets_DSNavigationBarButton_Active:I = 0x7f140979

.field public static Widgets_DSNavigationBarButton_Inactive:I = 0x7f14097a

.field public static Widgets_DSNavigationBarButton_Statical:I = 0x7f14097b

.field public static Widgets_PopularNavigationBar:I = 0x7f14097c

.field public static Widgets_PopularNavigationBar_LogoCenter:I = 0x7f14097d

.field public static Widgets_PopularNavigationBar_LogoLeft:I = 0x7f14097e

.field public static Widgets_PopularNavigationBar_LogoRight:I = 0x7f14097f

.field public static Widgets_PopularToolbar:I = 0x7f140980

.field public static Widgets_PopularToolbar_LogoCenter:I = 0x7f140981

.field public static Widgets_PopularToolbar_LogoLeft:I = 0x7f140982

.field public static Widgets_PopularToolbar_LogoRight:I = 0x7f140983

.field public static Widgets_StaticNavigationBar:I = 0x7f140984

.field public static Widgets_StaticNavigationBar_Pretitle:I = 0x7f140985

.field public static Widgets_StaticNavigationBar_Subtitle:I = 0x7f140986

.field public static Widgets_StaticNavigationBar_Title:I = 0x7f140987


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
