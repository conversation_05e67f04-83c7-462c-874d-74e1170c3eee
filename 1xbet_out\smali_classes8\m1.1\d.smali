.class public final Lm1/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\t\u0008\u0000\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ1\u0010\u0010\u001a\u00028\u0000\"\u0008\u0008\u0000\u0010\u000b*\u00020\n2\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u000c2\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "Lm1/d;",
        "",
        "Landroidx/lifecycle/g0;",
        "store",
        "Landroidx/lifecycle/e0$c;",
        "factory",
        "Lm1/a;",
        "extras",
        "<init>",
        "(Landroidx/lifecycle/g0;Landroidx/lifecycle/e0$c;Lm1/a;)V",
        "Landroidx/lifecycle/b0;",
        "T",
        "Lkotlin/reflect/d;",
        "modelClass",
        "",
        "key",
        "a",
        "(Lkotlin/reflect/d;Ljava/lang/String;)Landroidx/lifecycle/b0;",
        "Landroidx/lifecycle/g0;",
        "b",
        "Landroidx/lifecycle/e0$c;",
        "c",
        "Lm1/a;",
        "lifecycle-viewmodel_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroidx/lifecycle/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroidx/lifecycle/e0$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lm1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/lifecycle/g0;Landroidx/lifecycle/e0$c;Lm1/a;)V
    .locals 0
    .param p1    # Landroidx/lifecycle/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/e0$c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lm1/d;->a:Landroidx/lifecycle/g0;

    .line 5
    .line 6
    iput-object p2, p0, Lm1/d;->b:Landroidx/lifecycle/e0$c;

    .line 7
    .line 8
    iput-object p3, p0, Lm1/d;->c:Lm1/a;

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic b(Lm1/d;Lkotlin/reflect/d;Ljava/lang/String;ILjava/lang/Object;)Landroidx/lifecycle/b0;
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    sget-object p2, Ln1/g;->a:Ln1/g;

    .line 6
    .line 7
    invoke-virtual {p2, p1}, Ln1/g;->c(Lkotlin/reflect/d;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    :cond_0
    invoke-virtual {p0, p1, p2}, Lm1/d;->a(Lkotlin/reflect/d;Ljava/lang/String;)Landroidx/lifecycle/b0;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    return-object p0
.end method


# virtual methods
.method public final a(Lkotlin/reflect/d;Ljava/lang/String;)Landroidx/lifecycle/b0;
    .locals 2
    .param p1    # Lkotlin/reflect/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Landroidx/lifecycle/b0;",
            ">(",
            "Lkotlin/reflect/d<",
            "TT;>;",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lm1/d;->a:Landroidx/lifecycle/g0;

    .line 2
    .line 3
    invoke-virtual {v0, p2}, Landroidx/lifecycle/g0;->b(Ljava/lang/String;)Landroidx/lifecycle/b0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {p1, v0}, Lkotlin/reflect/d;->w(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    iget-object p1, p0, Lm1/d;->b:Landroidx/lifecycle/e0$c;

    .line 14
    .line 15
    instance-of p2, p1, Landroidx/lifecycle/e0$e;

    .line 16
    .line 17
    if-eqz p2, :cond_0

    .line 18
    .line 19
    check-cast p1, Landroidx/lifecycle/e0$e;

    .line 20
    .line 21
    invoke-virtual {p1, v0}, Landroidx/lifecycle/e0$e;->onRequery(Landroidx/lifecycle/b0;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    return-object v0

    .line 25
    :cond_1
    new-instance v0, Lm1/b;

    .line 26
    .line 27
    iget-object v1, p0, Lm1/d;->c:Lm1/a;

    .line 28
    .line 29
    invoke-direct {v0, v1}, Lm1/b;-><init>(Lm1/a;)V

    .line 30
    .line 31
    .line 32
    sget-object v1, Ln1/g$a;->a:Ln1/g$a;

    .line 33
    .line 34
    invoke-virtual {v0, v1, p2}, Lm1/b;->c(Lm1/a$b;Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    iget-object v1, p0, Lm1/d;->b:Landroidx/lifecycle/e0$c;

    .line 38
    .line 39
    invoke-static {v1, p1, v0}, Lm1/e;->a(Landroidx/lifecycle/e0$c;Lkotlin/reflect/d;Lm1/a;)Landroidx/lifecycle/b0;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object v0, p0, Lm1/d;->a:Landroidx/lifecycle/g0;

    .line 44
    .line 45
    invoke-virtual {v0, p2, p1}, Landroidx/lifecycle/g0;->d(Ljava/lang/String;Landroidx/lifecycle/b0;)V

    .line 46
    .line 47
    .line 48
    return-object p1
.end method
