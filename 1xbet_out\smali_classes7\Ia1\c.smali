.class public final LIa1/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LIa1/b;",
        ">;"
    }
.end annotation


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LAR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LZR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LzX0/k;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lz81/a;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWa0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LN91/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LJT/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lkc1/c;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc81/c;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LGg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lau/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LTZ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/b;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lej0/d;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LN91/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LJT/a;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;",
            "LBc/a<",
            "Lkc1/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lc81/c;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "LGg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LQW0/c;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lau/a;",
            ">;",
            "LBc/a<",
            "LTZ0/a;",
            ">;",
            "LBc/a<",
            "Lak/b;",
            ">;",
            "LBc/a<",
            "Lej0/d;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;",
            "LBc/a<",
            "LAR/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "LZR/a;",
            ">;",
            "LBc/a<",
            "Lo9/a;",
            ">;",
            "LBc/a<",
            "LzX0/k;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lz81/a;",
            ">;",
            "LBc/a<",
            "LWa0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LIa1/c;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LIa1/c;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, LIa1/c;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, LIa1/c;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, LIa1/c;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, LIa1/c;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, LIa1/c;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, LIa1/c;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, LIa1/c;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, LIa1/c;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, LIa1/c;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, LIa1/c;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, LIa1/c;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, LIa1/c;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, LIa1/c;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LIa1/c;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LIa1/c;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LIa1/c;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LIa1/c;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LIa1/c;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LIa1/c;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LIa1/c;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, LIa1/c;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, LIa1/c;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, LIa1/c;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, LIa1/c;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, LIa1/c;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, LIa1/c;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, LIa1/c;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, LIa1/c;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, LIa1/c;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, LIa1/c;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, LIa1/c;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, LIa1/c;->H:LBc/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, LIa1/c;->I:LBc/a;

    .line 113
    .line 114
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LIa1/c;
    .locals 36
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LN91/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LJT/a;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;",
            "LBc/a<",
            "Lkc1/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lc81/c;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "LGg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LQW0/c;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lau/a;",
            ">;",
            "LBc/a<",
            "LTZ0/a;",
            ">;",
            "LBc/a<",
            "Lak/b;",
            ">;",
            "LBc/a<",
            "Lej0/d;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;",
            "LBc/a<",
            "LAR/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "LZR/a;",
            ">;",
            "LBc/a<",
            "Lo9/a;",
            ">;",
            "LBc/a<",
            "LzX0/k;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lz81/a;",
            ">;",
            "LBc/a<",
            "LWa0/a;",
            ">;)",
            "LIa1/c;"
        }
    .end annotation

    .line 1
    new-instance v0, LIa1/c;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    invoke-direct/range {v0 .. v35}, LIa1/c;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method

.method public static c(LN91/e;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;Lak/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lorg/xbet/ui_common/utils/M;LQW0/c;LwX0/C;LSX0/c;LwX0/a;LHX0/e;Lau/a;LTZ0/a;Lak/b;Lej0/d;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;Lz81/a;LWa0/a;)LIa1/b;
    .locals 36

    .line 1
    new-instance v0, LIa1/b;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    invoke-direct/range {v0 .. v35}, LIa1/b;-><init>(LN91/e;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;Lak/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lorg/xbet/ui_common/utils/M;LQW0/c;LwX0/C;LSX0/c;LwX0/a;LHX0/e;Lau/a;LTZ0/a;Lak/b;Lej0/d;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;Lz81/a;LWa0/a;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method


# virtual methods
.method public b()LIa1/b;
    .locals 37

    move-object/from16 v0, p0

    .line 1
    iget-object v1, v0, LIa1/c;->a:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, LN91/e;

    iget-object v1, v0, LIa1/c;->b:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v3, v1

    check-cast v3, Lorg/xbet/analytics/domain/scope/g0;

    iget-object v1, v0, LIa1/c;->c:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, LJT/a;

    iget-object v1, v0, LIa1/c;->d:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lf8/g;

    iget-object v1, v0, LIa1/c;->e:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, LfX/b;

    iget-object v1, v0, LIa1/c;->f:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v7, v1

    check-cast v7, Lcom/xbet/onexuser/domain/user/c;

    iget-object v1, v0, LIa1/c;->g:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v8, v1

    check-cast v8, Lkc1/c;

    iget-object v1, v0, LIa1/c;->h:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    iget-object v1, v0, LIa1/c;->i:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v10, v1

    check-cast v10, Lorg/xbet/ui_common/utils/internet/a;

    iget-object v1, v0, LIa1/c;->j:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v11, v1

    check-cast v11, LP91/b;

    iget-object v1, v0, LIa1/c;->k:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v12, v1

    check-cast v12, Lc81/c;

    iget-object v1, v0, LIa1/c;->l:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v13, v1

    check-cast v13, LxX0/a;

    iget-object v1, v0, LIa1/c;->m:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v14, v1

    check-cast v14, Lak/a;

    iget-object v1, v0, LIa1/c;->n:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v15, v1

    check-cast v15, LGg/a;

    iget-object v1, v0, LIa1/c;->o:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v16, v1

    check-cast v16, Lorg/xbet/analytics/domain/scope/I;

    iget-object v1, v0, LIa1/c;->p:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v17, v1

    check-cast v17, Lorg/xbet/ui_common/utils/M;

    iget-object v1, v0, LIa1/c;->q:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v18, v1

    check-cast v18, LQW0/c;

    iget-object v1, v0, LIa1/c;->r:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v19, v1

    check-cast v19, LwX0/C;

    iget-object v1, v0, LIa1/c;->s:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v20, v1

    check-cast v20, LSX0/c;

    iget-object v1, v0, LIa1/c;->t:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v21, v1

    check-cast v21, LwX0/a;

    iget-object v1, v0, LIa1/c;->u:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v22, v1

    check-cast v22, LHX0/e;

    iget-object v1, v0, LIa1/c;->v:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v23, v1

    check-cast v23, Lau/a;

    iget-object v1, v0, LIa1/c;->w:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v24, v1

    check-cast v24, LTZ0/a;

    iget-object v1, v0, LIa1/c;->x:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v25, v1

    check-cast v25, Lak/b;

    iget-object v1, v0, LIa1/c;->y:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v26, v1

    check-cast v26, Lej0/d;

    iget-object v1, v0, LIa1/c;->z:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v27, v1

    check-cast v27, Li8/j;

    iget-object v1, v0, LIa1/c;->A:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v28, v1

    check-cast v28, LAR/a;

    iget-object v1, v0, LIa1/c;->B:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v29, v1

    check-cast v29, Lorg/xbet/remoteconfig/domain/usecases/i;

    iget-object v1, v0, LIa1/c;->C:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v30, v1

    check-cast v30, LZR/a;

    iget-object v1, v0, LIa1/c;->D:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v31, v1

    check-cast v31, Lo9/a;

    iget-object v1, v0, LIa1/c;->E:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v32, v1

    check-cast v32, LzX0/k;

    iget-object v1, v0, LIa1/c;->F:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v33, v1

    check-cast v33, Lgk0/a;

    iget-object v1, v0, LIa1/c;->G:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v34, v1

    check-cast v34, LnR/a;

    iget-object v1, v0, LIa1/c;->H:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v35, v1

    check-cast v35, Lz81/a;

    iget-object v1, v0, LIa1/c;->I:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v36, v1

    check-cast v36, LWa0/a;

    invoke-static/range {v2 .. v36}, LIa1/c;->c(LN91/e;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;Lak/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lorg/xbet/ui_common/utils/M;LQW0/c;LwX0/C;LSX0/c;LwX0/a;LHX0/e;Lau/a;LTZ0/a;Lak/b;Lej0/d;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;Lz81/a;LWa0/a;)LIa1/b;

    move-result-object v1

    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LIa1/c;->b()LIa1/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
