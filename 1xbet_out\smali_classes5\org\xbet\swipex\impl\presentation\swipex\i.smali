.class public final synthetic Lorg/xbet/swipex/impl/presentation/swipex/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/i;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/i;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->y2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Z

    move-result v0

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
