.class public final synthetic LD01/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LD01/c;->a:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LD01/c;->a:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;

    invoke-static {v0}, Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;->f(Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoPrimaryView;)Z

    move-result v0

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
