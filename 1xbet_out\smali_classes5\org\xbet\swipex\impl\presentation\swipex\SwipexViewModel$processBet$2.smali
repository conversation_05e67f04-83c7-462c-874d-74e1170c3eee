.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$processBet$2"
    f = "SwipexViewModel.kt"
    l = {
        0xd1,
        0xd3,
        0xdd,
        0xe9,
        0xec,
        0xf2
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->J4(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $swipeType:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    iput-object p2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->$swipeType:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;

    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    iget-object v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->$swipeType:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    iget v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    packed-switch v0, :pswitch_data_0

    .line 9
    .line 10
    .line 11
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 12
    .line 13
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 14
    .line 15
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    throw p1

    .line 19
    :pswitch_0
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$2:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v0, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 22
    .line 23
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 24
    .line 25
    check-cast v1, Ljava/lang/String;

    .line 26
    .line 27
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    move-object v8, p0

    .line 31
    goto/16 :goto_a

    .line 32
    .line 33
    :pswitch_1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$3:Ljava/lang/Object;

    .line 34
    .line 35
    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 36
    .line 37
    iget-object v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$2:Ljava/lang/Object;

    .line 38
    .line 39
    check-cast v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 40
    .line 41
    iget-object v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 42
    .line 43
    iget-object v5, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v5, LDS0/c;

    .line 46
    .line 47
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    move-object v8, p0

    .line 51
    goto/16 :goto_8

    .line 52
    .line 53
    :pswitch_2
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 54
    .line 55
    iget-object v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 56
    .line 57
    check-cast v3, LDS0/c;

    .line 58
    .line 59
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    move-object v8, p0

    .line 63
    goto/16 :goto_7

    .line 64
    .line 65
    :pswitch_3
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    move-object v3, v0

    .line 68
    check-cast v3, LDS0/c;

    .line 69
    .line 70
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 71
    .line 72
    .line 73
    move-object v8, p0

    .line 74
    goto/16 :goto_3

    .line 75
    .line 76
    :catchall_0
    move-exception v0

    .line 77
    move-object p1, v0

    .line 78
    move-object v8, p0

    .line 79
    goto/16 :goto_5

    .line 80
    .line 81
    :pswitch_4
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 82
    .line 83
    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 84
    .line 85
    iget-object v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 86
    .line 87
    check-cast v3, LDS0/c;

    .line 88
    .line 89
    :try_start_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 90
    .line 91
    .line 92
    move-object v8, p0

    .line 93
    goto :goto_1

    .line 94
    :pswitch_5
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 95
    .line 96
    check-cast v0, Lkotlinx/coroutines/N;

    .line 97
    .line 98
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 99
    .line 100
    .line 101
    goto :goto_0

    .line 102
    :pswitch_6
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 103
    .line 104
    .line 105
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 106
    .line 107
    check-cast p1, Lkotlinx/coroutines/N;

    .line 108
    .line 109
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 110
    .line 111
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 112
    .line 113
    .line 114
    move-result-object v0

    .line 115
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    const/4 p1, 0x1

    .line 118
    iput p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 119
    .line 120
    invoke-virtual {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->h(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    if-ne p1, v1, :cond_0

    .line 125
    .line 126
    move-object v8, p0

    .line 127
    goto/16 :goto_9

    .line 128
    .line 129
    :cond_0
    :goto_0
    move-object v3, p1

    .line 130
    check-cast v3, LDS0/c;

    .line 131
    .line 132
    if-nez v3, :cond_1

    .line 133
    .line 134
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 135
    .line 136
    return-object p1

    .line 137
    :cond_1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 138
    .line 139
    :try_start_2
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 140
    .line 141
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->z3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lfk/j;

    .line 142
    .line 143
    .line 144
    move-result-object v4

    .line 145
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    check-cast p1, LnS0/c;

    .line 154
    .line 155
    invoke-virtual {p1}, LnS0/c;->c()J

    .line 156
    .line 157
    .line 158
    move-result-wide v5

    .line 159
    iput-object v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 160
    .line 161
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 162
    .line 163
    const/4 p1, 0x2

    .line 164
    iput p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_3

    .line 165
    .line 166
    const/4 v7, 0x0

    .line 167
    const/4 v9, 0x2

    .line 168
    const/4 v10, 0x0

    .line 169
    move-object v8, p0

    .line 170
    :try_start_3
    invoke-static/range {v4 .. v10}, Lfk/j$a;->a(Lfk/j;JLorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object p1

    .line 174
    if-ne p1, v1, :cond_2

    .line 175
    .line 176
    goto/16 :goto_9

    .line 177
    .line 178
    :cond_2
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 179
    .line 180
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getMoney()D

    .line 181
    .line 182
    .line 183
    move-result-wide v4

    .line 184
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 185
    .line 186
    .line 187
    move-result-object p1

    .line 188
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 189
    .line 190
    .line 191
    move-result-object p1

    .line 192
    check-cast p1, LnS0/c;

    .line 193
    .line 194
    invoke-virtual {p1}, LnS0/c;->d()D

    .line 195
    .line 196
    .line 197
    move-result-wide v6

    .line 198
    cmpg-double p1, v4, v6

    .line 199
    .line 200
    if-gez p1, :cond_3

    .line 201
    .line 202
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    sget-object v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;

    .line 207
    .line 208
    invoke-virtual {p1, v4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 209
    .line 210
    .line 211
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 212
    .line 213
    .line 214
    move-result-object p1

    .line 215
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$g;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$g;

    .line 216
    .line 217
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 218
    .line 219
    .line 220
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 221
    .line 222
    return-object p1

    .line 223
    :catchall_1
    move-exception v0

    .line 224
    :goto_2
    move-object p1, v0

    .line 225
    goto :goto_5

    .line 226
    :cond_3
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->T3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/x0;

    .line 227
    .line 228
    .line 229
    move-result-object p1

    .line 230
    invoke-static {p1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 231
    .line 232
    .line 233
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 234
    .line 235
    .line 236
    move-result-object p1

    .line 237
    sget-object v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$j;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$j;

    .line 238
    .line 239
    invoke-virtual {p1, v4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 240
    .line 241
    .line 242
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->J3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;

    .line 243
    .line 244
    .line 245
    move-result-object v4

    .line 246
    invoke-static {v3}, LKS0/a;->a(LDS0/c;)Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 247
    .line 248
    .line 249
    move-result-object v5

    .line 250
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->B3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/usecases/j;

    .line 251
    .line 252
    .line 253
    move-result-object p1

    .line 254
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/domain/usecases/j;->a()Lorg/xbet/domain/betting/api/models/EnCoefCheck;

    .line 255
    .line 256
    .line 257
    move-result-object v6

    .line 258
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 259
    .line 260
    .line 261
    move-result-object p1

    .line 262
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 263
    .line 264
    .line 265
    move-result-object p1

    .line 266
    check-cast p1, LnS0/c;

    .line 267
    .line 268
    invoke-virtual {p1}, LnS0/c;->d()D

    .line 269
    .line 270
    .line 271
    move-result-wide v9
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 272
    move-object v13, v8

    .line 273
    move-wide v8, v9

    .line 274
    :try_start_4
    sget-object v10, Lcom/xbet/onexuser/domain/betting/PlayersDuelModel$GameWithoutDuel;->INSTANCE:Lcom/xbet/onexuser/domain/betting/PlayersDuelModel$GameWithoutDuel;

    .line 275
    .line 276
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 277
    .line 278
    .line 279
    move-result-object p1

    .line 280
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 281
    .line 282
    .line 283
    move-result-object p1

    .line 284
    check-cast p1, LnS0/c;

    .line 285
    .line 286
    invoke-virtual {p1}, LnS0/c;->c()J

    .line 287
    .line 288
    .line 289
    move-result-wide v11

    .line 290
    iput-object v3, v13, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 291
    .line 292
    iput-object v2, v13, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 293
    .line 294
    const/4 p1, 0x3

    .line 295
    iput p1, v13, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 296
    .line 297
    const/4 v7, 0x0

    .line 298
    invoke-virtual/range {v4 .. v13}, Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;->a(Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lorg/xbet/domain/betting/api/models/EnCoefCheck;ZDLcom/xbet/onexuser/domain/betting/PlayersDuelModel;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 299
    .line 300
    .line 301
    move-result-object p1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    .line 302
    move-object v8, v13

    .line 303
    if-ne p1, v1, :cond_4

    .line 304
    .line 305
    goto/16 :goto_9

    .line 306
    .line 307
    :cond_4
    :goto_3
    :try_start_5
    check-cast p1, Lorg/xbet/domain/betting/api/models/BetResult;

    .line 308
    .line 309
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 310
    .line 311
    .line 312
    move-result-object p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 313
    :goto_4
    move-object v0, p1

    .line 314
    goto :goto_6

    .line 315
    :catchall_2
    move-exception v0

    .line 316
    move-object v8, v13

    .line 317
    goto :goto_2

    .line 318
    :catchall_3
    move-exception v0

    .line 319
    move-object v8, p0

    .line 320
    goto :goto_2

    .line 321
    :goto_5
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 322
    .line 323
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 324
    .line 325
    .line 326
    move-result-object p1

    .line 327
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 328
    .line 329
    .line 330
    move-result-object p1

    .line 331
    goto :goto_4

    .line 332
    :goto_6
    iget-object p1, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 333
    .line 334
    iget-object v4, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->$swipeType:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 335
    .line 336
    invoke-static {v0}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 337
    .line 338
    .line 339
    move-result-object v5

    .line 340
    if-eqz v5, :cond_6

    .line 341
    .line 342
    instance-of v6, v5, Ljava/net/UnknownHostException;

    .line 343
    .line 344
    if-eqz v6, :cond_5

    .line 345
    .line 346
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 347
    .line 348
    .line 349
    move-result-object p1

    .line 350
    sget-object v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;

    .line 351
    .line 352
    invoke-virtual {p1, v4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 353
    .line 354
    .line 355
    goto :goto_7

    .line 356
    :cond_5
    iput-object v3, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 357
    .line 358
    iput-object v0, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 359
    .line 360
    const/4 v6, 0x4

    .line 361
    iput v6, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 362
    .line 363
    invoke-static {p1, v5, v3, v4, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->f4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Ljava/lang/Throwable;LDS0/c;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 364
    .line 365
    .line 366
    move-result-object p1

    .line 367
    if-ne p1, v1, :cond_6

    .line 368
    .line 369
    goto :goto_9

    .line 370
    :cond_6
    :goto_7
    move-object v4, v0

    .line 371
    move-object v5, v3

    .line 372
    iget-object v3, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 373
    .line 374
    iget-object v0, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->$swipeType:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 375
    .line 376
    invoke-static {v4}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 377
    .line 378
    .line 379
    move-result p1

    .line 380
    if-eqz p1, :cond_9

    .line 381
    .line 382
    move-object p1, v4

    .line 383
    check-cast p1, Lorg/xbet/domain/betting/api/models/BetResult;

    .line 384
    .line 385
    iput-object v5, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 386
    .line 387
    iput-object v4, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 388
    .line 389
    iput-object v3, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$2:Ljava/lang/Object;

    .line 390
    .line 391
    iput-object v0, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$3:Ljava/lang/Object;

    .line 392
    .line 393
    const/4 v6, 0x5

    .line 394
    iput v6, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 395
    .line 396
    invoke-static {v3, p1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->b4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lorg/xbet/domain/betting/api/models/BetResult;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 397
    .line 398
    .line 399
    move-result-object p1

    .line 400
    if-ne p1, v1, :cond_7

    .line 401
    .line 402
    goto :goto_9

    .line 403
    :cond_7
    :goto_8
    invoke-static {v3, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->W3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V

    .line 404
    .line 405
    .line 406
    invoke-virtual {v3}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->E4()V

    .line 407
    .line 408
    .line 409
    invoke-static {v3}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 410
    .line 411
    .line 412
    move-result-object v0

    .line 413
    invoke-virtual {v5}, LDS0/c;->c()LDS0/d;

    .line 414
    .line 415
    .line 416
    move-result-object p1

    .line 417
    invoke-virtual {p1}, LDS0/d;->d()Ljava/lang/String;

    .line 418
    .line 419
    .line 420
    move-result-object p1

    .line 421
    iput-object v4, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$0:Ljava/lang/Object;

    .line 422
    .line 423
    iput-object p1, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$1:Ljava/lang/Object;

    .line 424
    .line 425
    iput-object v0, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$2:Ljava/lang/Object;

    .line 426
    .line 427
    iput-object v2, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->L$3:Ljava/lang/Object;

    .line 428
    .line 429
    const/4 v2, 0x6

    .line 430
    iput v2, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->label:I

    .line 431
    .line 432
    invoke-static {v3, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->v3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 433
    .line 434
    .line 435
    move-result-object v2

    .line 436
    if-ne v2, v1, :cond_8

    .line 437
    .line 438
    :goto_9
    return-object v1

    .line 439
    :cond_8
    move-object v1, p1

    .line 440
    move-object p1, v2

    .line 441
    :goto_a
    check-cast p1, Ljava/lang/String;

    .line 442
    .line 443
    new-instance v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;

    .line 444
    .line 445
    invoke-direct {v2, v1, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 446
    .line 447
    .line 448
    invoke-virtual {v0, v2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 449
    .line 450
    .line 451
    :cond_9
    iget-object p1, v8, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$processBet$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 452
    .line 453
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 454
    .line 455
    .line 456
    move-result-object p1

    .line 457
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$k;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$k;

    .line 458
    .line 459
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 460
    .line 461
    .line 462
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 463
    .line 464
    return-object p1

    .line 465
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
