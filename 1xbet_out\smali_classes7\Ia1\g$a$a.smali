.class public final LIa1/g$a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIa1/g$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/h<",
        "Lf81/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LN91/e;


# direct methods
.method public constructor <init>(LN91/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LIa1/g$a$a;->a:LN91/e;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lf81/a;
    .locals 1

    .line 1
    iget-object v0, p0, LIa1/g$a$a;->a:LN91/e;

    .line 2
    .line 3
    invoke-interface {v0}, LN91/e;->l()Lf81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lf81/a;

    .line 12
    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LIa1/g$a$a;->a()Lf81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
