.class public LI0/m$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LI0/m$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LI0/m;->j([LP0/k$b;I)LP0/k$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LI0/m$c<",
        "LP0/k$b;",
        ">;"
    }
.end annotation


# instance fields
.field public final synthetic a:LI0/m;


# direct methods
.method public constructor <init>(LI0/m;)V
    .locals 0

    .line 1
    iput-object p1, p0, LI0/m$a;->a:LI0/m;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;)I
    .locals 0

    .line 1
    check-cast p1, LP0/k$b;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LI0/m$a;->c(LP0/k$b;)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LP0/k$b;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LI0/m$a;->d(LP0/k$b;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public c(LP0/k$b;)I
    .locals 0

    .line 1
    invoke-virtual {p1}, LP0/k$b;->e()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public d(LP0/k$b;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, LP0/k$b;->f()Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
