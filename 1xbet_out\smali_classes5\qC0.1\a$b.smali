.class public final LqC0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LqC0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LqC0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LqC0/a$b$d;,
        LqC0/a$b$b;,
        LqC0/a$b$f;,
        LqC0/a$b$g;,
        LqC0/a$b$h;,
        LqC0/a$b$e;,
        LqC0/a$b$a;,
        LqC0/a$b$c;
    }
.end annotation


# instance fields
.field public final a:LlB0/a;

.field public final b:LuB0/a;

.field public final c:LWb0/a;

.field public final d:LqC0/a$b;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/subgames/api/SubGamesParams;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/o;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/k;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/q;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhB0/p;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LrC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/w;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LYb0/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHR/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LuC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public s:Lorg/xbet/sportgame/subgames/impl/presentation/j;

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqC0/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LqC0/a$b;->d:LqC0/a$b;

    .line 4
    iput-object p7, p0, LqC0/a$b;->a:LlB0/a;

    .line 5
    iput-object p8, p0, LqC0/a$b;->b:LuB0/a;

    .line 6
    iput-object p3, p0, LqC0/a$b;->c:LWb0/a;

    .line 7
    invoke-virtual/range {p0 .. p10}, LqC0/a$b;->b(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;LqC0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p10}, LqC0/a$b;-><init>(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LqC0/a$b;->c(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)V
    .locals 10

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p5

    .line 5
    iput-object p5, p0, LqC0/a$b;->e:Ldagger/internal/h;

    .line 6
    .line 7
    new-instance p5, LqC0/a$b$d;

    .line 8
    .line 9
    invoke-direct {p5, p2}, LqC0/a$b$d;-><init>(LKA0/c;)V

    .line 10
    .line 11
    .line 12
    iput-object p5, p0, LqC0/a$b;->f:Ldagger/internal/h;

    .line 13
    .line 14
    new-instance p5, LqC0/a$b$b;

    .line 15
    .line 16
    invoke-direct {p5, p2}, LqC0/a$b$b;-><init>(LKA0/c;)V

    .line 17
    .line 18
    .line 19
    iput-object p5, p0, LqC0/a$b;->g:Ldagger/internal/h;

    .line 20
    .line 21
    new-instance p5, LqC0/a$b$f;

    .line 22
    .line 23
    invoke-direct {p5, p2}, LqC0/a$b$f;-><init>(LKA0/c;)V

    .line 24
    .line 25
    .line 26
    iput-object p5, p0, LqC0/a$b;->h:Ldagger/internal/h;

    .line 27
    .line 28
    new-instance p5, LqC0/a$b$g;

    .line 29
    .line 30
    invoke-direct {p5, p2}, LqC0/a$b$g;-><init>(LKA0/c;)V

    .line 31
    .line 32
    .line 33
    iput-object p5, p0, LqC0/a$b;->i:Ldagger/internal/h;

    .line 34
    .line 35
    invoke-static {p5}, LrC0/b;->a(LBc/a;)LrC0/b;

    .line 36
    .line 37
    .line 38
    move-result-object p5

    .line 39
    iput-object p5, p0, LqC0/a$b;->j:Ldagger/internal/h;

    .line 40
    .line 41
    new-instance p5, LqC0/a$b$h;

    .line 42
    .line 43
    invoke-direct {p5, p2}, LqC0/a$b$h;-><init>(LKA0/c;)V

    .line 44
    .line 45
    .line 46
    iput-object p5, p0, LqC0/a$b;->k:Ldagger/internal/h;

    .line 47
    .line 48
    new-instance p2, LqC0/a$b$e;

    .line 49
    .line 50
    invoke-direct {p2, p3}, LqC0/a$b$e;-><init>(LWb0/a;)V

    .line 51
    .line 52
    .line 53
    iput-object p2, p0, LqC0/a$b;->l:Ldagger/internal/h;

    .line 54
    .line 55
    new-instance p2, LqC0/a$b$a;

    .line 56
    .line 57
    invoke-direct {p2, p1}, LqC0/a$b$a;-><init>(LQW0/c;)V

    .line 58
    .line 59
    .line 60
    iput-object p2, p0, LqC0/a$b;->m:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, LqC0/a$b;->n:Ldagger/internal/h;

    .line 67
    .line 68
    new-instance p1, LqC0/a$b$c;

    .line 69
    .line 70
    invoke-direct {p1, p4}, LqC0/a$b$c;-><init>(LiR/a;)V

    .line 71
    .line 72
    .line 73
    iput-object p1, p0, LqC0/a$b;->o:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iput-object p1, p0, LqC0/a$b;->p:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object p2, p0, LqC0/a$b;->n:Ldagger/internal/h;

    .line 82
    .line 83
    iget-object p3, p0, LqC0/a$b;->o:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {p2, p3, p1}, LuC0/b;->a(LBc/a;LBc/a;LBc/a;)LuC0/b;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    iput-object p1, p0, LqC0/a$b;->q:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 92
    .line 93
    .line 94
    move-result-object v9

    .line 95
    iput-object v9, p0, LqC0/a$b;->r:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object v0, p0, LqC0/a$b;->e:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object v1, p0, LqC0/a$b;->f:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object v2, p0, LqC0/a$b;->g:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v3, p0, LqC0/a$b;->h:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v4, p0, LqC0/a$b;->j:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v5, p0, LqC0/a$b;->k:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v6, p0, LqC0/a$b;->l:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v7, p0, LqC0/a$b;->m:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v8, p0, LqC0/a$b;->q:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static/range {v0 .. v9}, Lorg/xbet/sportgame/subgames/impl/presentation/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/subgames/impl/presentation/j;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iput-object p1, p0, LqC0/a$b;->s:Lorg/xbet/sportgame/subgames/impl/presentation/j;

    .line 120
    .line 121
    invoke-static {p1}, LqC0/g;->c(Lorg/xbet/sportgame/subgames/impl/presentation/j;)Ldagger/internal/h;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    iput-object p1, p0, LqC0/a$b;->t:Ldagger/internal/h;

    .line 126
    .line 127
    return-void
.end method

.method public final c(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LqC0/a$b;->t:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LqC0/f;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/sportgame/subgames/impl/presentation/h;->d(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;LqC0/f;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LqC0/a$b;->a:LlB0/a;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/sportgame/subgames/impl/presentation/h;->a(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;LlB0/a;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, LqC0/a$b;->d()Lorg/xbet/sportgame/subgames/impl/presentation/g;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-static {p1, v0}, Lorg/xbet/sportgame/subgames/impl/presentation/h;->b(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;Lorg/xbet/sportgame/subgames/impl/presentation/g;)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, LqC0/a$b;->c:LWb0/a;

    .line 25
    .line 26
    invoke-interface {v0}, LWb0/a;->e()Lac0/a;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lac0/a;

    .line 35
    .line 36
    invoke-static {p1, v0}, Lorg/xbet/sportgame/subgames/impl/presentation/h;->c(Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;Lac0/a;)V

    .line 37
    .line 38
    .line 39
    return-object p1
.end method

.method public final d()Lorg/xbet/sportgame/subgames/impl/presentation/g;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/sportgame/subgames/impl/presentation/g;

    .line 2
    .line 3
    iget-object v1, p0, LqC0/a$b;->b:LuB0/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lorg/xbet/sportgame/subgames/impl/presentation/g;-><init>(LuB0/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method
