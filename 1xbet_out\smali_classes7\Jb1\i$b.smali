.class public final LJb1/i$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LJb1/k;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LJb1/i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LJb1/i$b$e;,
        LJb1/i$b$b;,
        LJb1/i$b$a;,
        LJb1/i$b$j;,
        LJb1/i$b$i;,
        LJb1/i$b$f;,
        LJb1/i$b$g;,
        LJb1/i$b$d;,
        LJb1/i$b$c;,
        LJb1/i$b$h;,
        LJb1/i$b$m;,
        LJb1/i$b$l;,
        LJb1/i$b$k;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/r;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/m;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/b;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/f;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/j;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSR/a;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/p;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LnR/d;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ldu/e;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Luf0/a;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LXa0/i;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public final b:LzX0/k;

.field public final c:Lak/b;

.field public final d:LJb1/i$b;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LEb1/d;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LS8/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/g;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/k;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LJb1/i$b;->d:LJb1/i$b;

    .line 4
    iput-object p8, p0, LJb1/i$b;->a:LTZ0/a;

    move-object/from16 v0, p39

    .line 5
    iput-object v0, p0, LJb1/i$b;->b:LzX0/k;

    .line 6
    iput-object p5, p0, LJb1/i$b;->c:Lak/b;

    .line 7
    invoke-virtual/range {p0 .. p41}, LJb1/i$b;->b(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V

    .line 8
    invoke-virtual/range {p0 .. p41}, LJb1/i$b;->c(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;LJb1/j;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p41}, LJb1/i$b;-><init>(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LJb1/i$b;->d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V
    .locals 0

    .line 1
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    iput-object p2, p0, LJb1/i$b;->e:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    iput-object p2, p0, LJb1/i$b;->f:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    iput-object p2, p0, LJb1/i$b;->g:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    iput-object p2, p0, LJb1/i$b;->h:Ldagger/internal/h;

    .line 24
    .line 25
    new-instance p2, LJb1/i$b$e;

    .line 26
    .line 27
    invoke-direct {p2, p1}, LJb1/i$b$e;-><init>(LQW0/c;)V

    .line 28
    .line 29
    .line 30
    iput-object p2, p0, LJb1/i$b;->i:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LJb1/i$b;->j:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LJb1/i$b;->k:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, LEb1/e;->a(LBc/a;)LEb1/e;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LJb1/i$b;->l:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static/range {p36 .. p36}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, LJb1/i$b;->m:Ldagger/internal/h;

    .line 55
    .line 56
    iget-object p1, p0, LJb1/i$b;->i:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object p2, p0, LJb1/i$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    iget-object p5, p0, LJb1/i$b;->l:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {}, LEb1/b;->a()LEb1/b;

    .line 63
    .line 64
    .line 65
    move-result-object p6

    .line 66
    iget-object p7, p0, LJb1/i$b;->m:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p1, p2, p5, p6, p7}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/a;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, LJb1/i$b;->n:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object p2, p0, LJb1/i$b;->h:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {p2, p1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/g;->a(LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/g;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iput-object p1, p0, LJb1/i$b;->o:Ldagger/internal/h;

    .line 81
    .line 82
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iput-object p1, p0, LJb1/i$b;->p:Ldagger/internal/h;

    .line 87
    .line 88
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iput-object p1, p0, LJb1/i$b;->q:Ldagger/internal/h;

    .line 93
    .line 94
    new-instance p1, LJb1/i$b$b;

    .line 95
    .line 96
    invoke-direct {p1, p4}, LJb1/i$b$b;-><init>(Lak/a;)V

    .line 97
    .line 98
    .line 99
    iput-object p1, p0, LJb1/i$b;->r:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LJb1/i$b;->s:Ldagger/internal/h;

    .line 106
    .line 107
    new-instance p1, LJb1/i$b$a;

    .line 108
    .line 109
    invoke-direct {p1, p3}, LJb1/i$b$a;-><init>(Lc81/a;)V

    .line 110
    .line 111
    .line 112
    iput-object p1, p0, LJb1/i$b;->t:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iput-object p1, p0, LJb1/i$b;->u:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    iput-object p1, p0, LJb1/i$b;->v:Ldagger/internal/h;

    .line 125
    .line 126
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    iput-object p1, p0, LJb1/i$b;->w:Ldagger/internal/h;

    .line 131
    .line 132
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    iput-object p1, p0, LJb1/i$b;->x:Ldagger/internal/h;

    .line 137
    .line 138
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    iput-object p1, p0, LJb1/i$b;->y:Ldagger/internal/h;

    .line 143
    .line 144
    new-instance p1, LJb1/i$b$j;

    .line 145
    .line 146
    invoke-direct {p1, p4}, LJb1/i$b$j;-><init>(Lak/a;)V

    .line 147
    .line 148
    .line 149
    iput-object p1, p0, LJb1/i$b;->z:Ldagger/internal/h;

    .line 150
    .line 151
    new-instance p1, LJb1/i$b$i;

    .line 152
    .line 153
    invoke-direct {p1, p3}, LJb1/i$b$i;-><init>(Lc81/a;)V

    .line 154
    .line 155
    .line 156
    iput-object p1, p0, LJb1/i$b;->A:Ldagger/internal/h;

    .line 157
    .line 158
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    iput-object p1, p0, LJb1/i$b;->B:Ldagger/internal/h;

    .line 163
    .line 164
    invoke-static/range {p33 .. p33}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 165
    .line 166
    .line 167
    move-result-object p1

    .line 168
    iput-object p1, p0, LJb1/i$b;->C:Ldagger/internal/h;

    .line 169
    .line 170
    return-void
.end method

.method public final c(LQW0/c;Ltf0/a;Lc81/a;Lak/a;Lak/b;LWa0/a;Ljava/lang/Boolean;LTZ0/a;LwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lorg/xbet/remoteconfig/domain/usecases/i;Lf8/g;Li8/l;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lhf0/a;Lv81/g;Lau/a;Li8/j;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;Leu/i;Ldu/e;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    new-instance v2, LJb1/i$b$f;

    .line 6
    .line 7
    invoke-direct {v2, v1}, LJb1/i$b$f;-><init>(Lc81/a;)V

    .line 8
    .line 9
    .line 10
    iput-object v2, v0, LJb1/i$b;->D:Ldagger/internal/h;

    .line 11
    .line 12
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    iput-object v2, v0, LJb1/i$b;->E:Ldagger/internal/h;

    .line 17
    .line 18
    new-instance v2, LJb1/i$b$g;

    .line 19
    .line 20
    invoke-direct {v2, v1}, LJb1/i$b$g;-><init>(Lc81/a;)V

    .line 21
    .line 22
    .line 23
    iput-object v2, v0, LJb1/i$b;->F:Ldagger/internal/h;

    .line 24
    .line 25
    new-instance v2, LJb1/i$b$d;

    .line 26
    .line 27
    invoke-direct {v2, v1}, LJb1/i$b$d;-><init>(Lc81/a;)V

    .line 28
    .line 29
    .line 30
    iput-object v2, v0, LJb1/i$b;->G:Ldagger/internal/h;

    .line 31
    .line 32
    new-instance v2, LJb1/i$b$c;

    .line 33
    .line 34
    invoke-direct {v2, v1}, LJb1/i$b$c;-><init>(Lc81/a;)V

    .line 35
    .line 36
    .line 37
    iput-object v2, v0, LJb1/i$b;->H:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object v3, v0, LJb1/i$b;->A:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object v4, v0, LJb1/i$b;->B:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v5, v0, LJb1/i$b;->C:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object v6, v0, LJb1/i$b;->D:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object v7, v0, LJb1/i$b;->E:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v8, v0, LJb1/i$b;->F:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object v9, v0, LJb1/i$b;->G:Ldagger/internal/h;

    .line 52
    .line 53
    move-object/from16 p14, v2

    .line 54
    .line 55
    move-object/from16 p7, v3

    .line 56
    .line 57
    move-object/from16 p8, v4

    .line 58
    .line 59
    move-object/from16 p9, v5

    .line 60
    .line 61
    move-object/from16 p10, v6

    .line 62
    .line 63
    move-object/from16 p11, v7

    .line 64
    .line 65
    move-object/from16 p12, v8

    .line 66
    .line 67
    move-object/from16 p13, v9

    .line 68
    .line 69
    invoke-static/range {p7 .. p14}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/e;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/e;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    iput-object v2, v0, LJb1/i$b;->I:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object v2

    .line 79
    iput-object v2, v0, LJb1/i$b;->J:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    iput-object v2, v0, LJb1/i$b;->K:Ldagger/internal/h;

    .line 86
    .line 87
    new-instance v2, LJb1/i$b$h;

    .line 88
    .line 89
    invoke-direct {v2, v1}, LJb1/i$b$h;-><init>(Lc81/a;)V

    .line 90
    .line 91
    .line 92
    iput-object v2, v0, LJb1/i$b;->L:Ldagger/internal/h;

    .line 93
    .line 94
    new-instance v1, LJb1/i$b$m;

    .line 95
    .line 96
    move-object/from16 v2, p4

    .line 97
    .line 98
    invoke-direct {v1, v2}, LJb1/i$b$m;-><init>(Lak/a;)V

    .line 99
    .line 100
    .line 101
    iput-object v1, v0, LJb1/i$b;->M:Ldagger/internal/h;

    .line 102
    .line 103
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    iput-object v1, v0, LJb1/i$b;->N:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static/range {p38 .. p38}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    iput-object v1, v0, LJb1/i$b;->O:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static/range {p41 .. p41}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    iput-object v1, v0, LJb1/i$b;->P:Ldagger/internal/h;

    .line 120
    .line 121
    new-instance v1, LJb1/i$b$l;

    .line 122
    .line 123
    move-object/from16 v2, p2

    .line 124
    .line 125
    invoke-direct {v1, v2}, LJb1/i$b$l;-><init>(Ltf0/a;)V

    .line 126
    .line 127
    .line 128
    iput-object v1, v0, LJb1/i$b;->Q:Ldagger/internal/h;

    .line 129
    .line 130
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    iput-object v1, v0, LJb1/i$b;->R:Ldagger/internal/h;

    .line 135
    .line 136
    new-instance v1, LJb1/i$b$k;

    .line 137
    .line 138
    move-object/from16 v2, p6

    .line 139
    .line 140
    invoke-direct {v1, v2}, LJb1/i$b$k;-><init>(LWa0/a;)V

    .line 141
    .line 142
    .line 143
    iput-object v1, v0, LJb1/i$b;->S:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v1, v0, LJb1/i$b;->e:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object v2, v0, LJb1/i$b;->f:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object v3, v0, LJb1/i$b;->g:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object v4, v0, LJb1/i$b;->o:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v5, v0, LJb1/i$b;->p:Ldagger/internal/h;

    .line 154
    .line 155
    invoke-static {}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;->a()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;

    .line 156
    .line 157
    .line 158
    move-result-object v6

    .line 159
    iget-object v7, v0, LJb1/i$b;->q:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object v8, v0, LJb1/i$b;->r:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object v9, v0, LJb1/i$b;->s:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object v10, v0, LJb1/i$b;->t:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v11, v0, LJb1/i$b;->u:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v12, v0, LJb1/i$b;->v:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v13, v0, LJb1/i$b;->w:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v14, v0, LJb1/i$b;->x:Ldagger/internal/h;

    .line 174
    .line 175
    iget-object v15, v0, LJb1/i$b;->y:Ldagger/internal/h;

    .line 176
    .line 177
    move-object/from16 p1, v1

    .line 178
    .line 179
    iget-object v1, v0, LJb1/i$b;->z:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 p16, v1

    .line 182
    .line 183
    iget-object v1, v0, LJb1/i$b;->I:Ldagger/internal/h;

    .line 184
    .line 185
    move-object/from16 p17, v1

    .line 186
    .line 187
    iget-object v1, v0, LJb1/i$b;->J:Ldagger/internal/h;

    .line 188
    .line 189
    move-object/from16 p18, v1

    .line 190
    .line 191
    iget-object v1, v0, LJb1/i$b;->K:Ldagger/internal/h;

    .line 192
    .line 193
    move-object/from16 p19, v1

    .line 194
    .line 195
    iget-object v1, v0, LJb1/i$b;->L:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p20, v1

    .line 198
    .line 199
    iget-object v1, v0, LJb1/i$b;->B:Ldagger/internal/h;

    .line 200
    .line 201
    move-object/from16 p21, v1

    .line 202
    .line 203
    iget-object v1, v0, LJb1/i$b;->M:Ldagger/internal/h;

    .line 204
    .line 205
    move-object/from16 p22, v1

    .line 206
    .line 207
    iget-object v1, v0, LJb1/i$b;->i:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 p23, v1

    .line 210
    .line 211
    iget-object v1, v0, LJb1/i$b;->N:Ldagger/internal/h;

    .line 212
    .line 213
    move-object/from16 p24, v1

    .line 214
    .line 215
    iget-object v1, v0, LJb1/i$b;->O:Ldagger/internal/h;

    .line 216
    .line 217
    move-object/from16 p25, v1

    .line 218
    .line 219
    iget-object v1, v0, LJb1/i$b;->P:Ldagger/internal/h;

    .line 220
    .line 221
    move-object/from16 p26, v1

    .line 222
    .line 223
    iget-object v1, v0, LJb1/i$b;->Q:Ldagger/internal/h;

    .line 224
    .line 225
    move-object/from16 p27, v1

    .line 226
    .line 227
    iget-object v1, v0, LJb1/i$b;->R:Ldagger/internal/h;

    .line 228
    .line 229
    move-object/from16 p28, v1

    .line 230
    .line 231
    iget-object v1, v0, LJb1/i$b;->S:Ldagger/internal/h;

    .line 232
    .line 233
    move-object/from16 p29, v1

    .line 234
    .line 235
    iget-object v1, v0, LJb1/i$b;->H:Ldagger/internal/h;

    .line 236
    .line 237
    move-object/from16 p30, v1

    .line 238
    .line 239
    move-object/from16 p2, v2

    .line 240
    .line 241
    move-object/from16 p3, v3

    .line 242
    .line 243
    move-object/from16 p4, v4

    .line 244
    .line 245
    move-object/from16 p5, v5

    .line 246
    .line 247
    move-object/from16 p6, v6

    .line 248
    .line 249
    move-object/from16 p7, v7

    .line 250
    .line 251
    move-object/from16 p8, v8

    .line 252
    .line 253
    move-object/from16 p9, v9

    .line 254
    .line 255
    move-object/from16 p10, v10

    .line 256
    .line 257
    move-object/from16 p11, v11

    .line 258
    .line 259
    move-object/from16 p12, v12

    .line 260
    .line 261
    move-object/from16 p13, v13

    .line 262
    .line 263
    move-object/from16 p14, v14

    .line 264
    .line 265
    move-object/from16 p15, v15

    .line 266
    .line 267
    invoke-static/range {p1 .. p30}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;

    .line 268
    .line 269
    .line 270
    move-result-object v1

    .line 271
    iput-object v1, v0, LJb1/i$b;->T:Ldagger/internal/h;

    .line 272
    .line 273
    return-void
.end method

.method public final d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LJb1/i$b;->f()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/h;->d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Landroidx/lifecycle/e0$c;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LJb1/i$b;->a:LTZ0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/h;->a(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;LTZ0/a;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LJb1/i$b;->b:LzX0/k;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/h;->c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;LzX0/k;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, LJb1/i$b;->c:Lak/b;

    .line 19
    .line 20
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    check-cast v0, Lck/a;

    .line 29
    .line 30
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/h;->b(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lck/a;)V

    .line 31
    .line 32
    .line 33
    return-object p1
.end method

.method public final e()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LJb1/i$b;->T:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final f()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LJb1/i$b;->e()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
