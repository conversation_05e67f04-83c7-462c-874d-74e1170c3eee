.class public final Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;
.super Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0001\u0018\u00002\u00020\u0001B9\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001f\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u001a0\u0019H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ1\u0010#\u001a\u00020\u00102\u0006\u0010\u001e\u001a\u00020\u001d2\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u001d2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u001dH\u0016\u00a2\u0006\u0004\u0008#\u0010$J1\u0010%\u001a\u00020\u00102\u0006\u0010\u001e\u001a\u00020\u001d2\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u001d2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u001dH\u0016\u00a2\u0006\u0004\u0008%\u0010$J\u001f\u0010*\u001a\u00020\u00102\u0006\u0010\'\u001a\u00020&2\u0006\u0010)\u001a\u00020(H\u0016\u00a2\u0006\u0004\u0008*\u0010+J\u000f\u0010,\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008,\u0010\u0012J\u001b\u0010.\u001a\u0004\u0018\u00010-2\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u001dH\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u0019\u00101\u001a\u0004\u0018\u0001002\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u00081\u00102R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u00103R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u001a\u0010B\u001a\u0008\u0012\u0004\u0012\u00020?0>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u001a\u0010D\u001a\u0008\u0012\u0004\u0012\u00020\u001a0>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010A\u00a8\u0006E"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "LHX0/e;",
        "resourceManager",
        "Lm8/a;",
        "coroutineDispatchers",
        "Ltw/b;",
        "configureCouponScenario",
        "Le90/a;",
        "makeBetFeatureEnabledScenario",
        "Ltw/j;",
        "replaceCouponEventScenario",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "getStageTableWithExtrasScenario",
        "<init>",
        "(LHX0/e;Lm8/a;Ltw/b;Le90/a;Ltw/j;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;)V",
        "",
        "x",
        "()V",
        "Landroidx/lifecycle/b0;",
        "viewModel",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "e",
        "(Landroidx/lifecycle/b0;Landroidx/lifecycle/Q;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "A1",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "opponentId",
        "gameId",
        "",
        "sportId",
        "champId",
        "c3",
        "(ILjava/lang/Integer;JI)V",
        "J",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "singleBetGame",
        "Lorg/xbet/betting/core/coupon/models/SimpleBetZip;",
        "simpleBetZip",
        "f2",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V",
        "j1",
        "LDy0/c;",
        "v",
        "(Ljava/lang/Integer;)LDy0/c;",
        "LDy0/b;",
        "u",
        "(I)LDy0/b;",
        "LHX0/e;",
        "f",
        "Lm8/a;",
        "g",
        "Ltw/b;",
        "h",
        "Le90/a;",
        "i",
        "Ltw/j;",
        "j",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "Lkotlinx/coroutines/flow/V;",
        "LVy0/a;",
        "k",
        "Lkotlinx/coroutines/flow/V;",
        "stateModel",
        "l",
        "eventState",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Ltw/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Le90/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Ltw/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LVy0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(LHX0/e;Lm8/a;Ltw/b;Le90/a;Ltw/j;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;)V
    .locals 0
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ltw/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Le90/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ltw/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->e:LHX0/e;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f:Lm8/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->g:Ltw/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->h:Le90/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->i:Ltw/j;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->j:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 15
    .line 16
    new-instance p1, LVy0/a;

    .line 17
    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object p2

    .line 22
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p3

    .line 26
    invoke-direct {p1, p2, p3}, LVy0/a;-><init>(Ljava/util/List;Ljava/util/List;)V

    .line 27
    .line 28
    .line 29
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->k:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    sget-object p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;->a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;

    .line 36
    .line 37
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l:Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    return-void
.end method

.method private static final synthetic C(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic k(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Ltw/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->g:Ltw/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic l(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic n(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Le90/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->h:Le90/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic o(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Ltw/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->i:Ltw/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->k:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->C(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final x()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->j:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->d()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$observeStageTable$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$observeStageTable$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$observeStageTable$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$observeStageTable$2;

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-static {v2}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f:Lm8/a;

    .line 28
    .line 29
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-static {v0, v2, v1}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method


# virtual methods
.method public A1()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public J(ILjava/lang/Integer;JI)V
    .locals 8

    .line 1
    invoke-virtual {p0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->v(Ljava/lang/Integer;)LDy0/c;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    if-eqz p2, :cond_0

    .line 6
    .line 7
    invoke-static {p2, p3, p4, p5}, LLy0/c;->a(LDy0/c;JI)Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    if-nez p2, :cond_1

    .line 12
    .line 13
    :cond_0
    sget-object p2, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->Companion:Lorg/xbet/betting/core/coupon/models/SingleBetGame$a;

    .line 14
    .line 15
    invoke-virtual {p2}, Lorg/xbet/betting/core/coupon/models/SingleBetGame$a;->a()Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    :cond_1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->u(I)LDy0/b;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    if-eqz p1, :cond_2

    .line 24
    .line 25
    iget-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->e:LHX0/e;

    .line 26
    .line 27
    invoke-static {p1, p3}, LLy0/b;->a(LDy0/b;LHX0/e;)Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 34
    .line 35
    .line 36
    move-result-object p3

    .line 37
    invoke-static {p3}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f:Lm8/a;

    .line 42
    .line 43
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketLongClicked$1$1;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketLongClicked$1$1;

    .line 48
    .line 49
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketLongClicked$1$2;

    .line 50
    .line 51
    const/4 p3, 0x0

    .line 52
    invoke-direct {v5, p0, p2, p1, p3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketLongClicked$1$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    const/16 v6, 0xa

    .line 56
    .line 57
    const/4 v7, 0x0

    .line 58
    const/4 v2, 0x0

    .line 59
    const/4 v4, 0x0

    .line 60
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 61
    .line 62
    .line 63
    :cond_2
    return-void
.end method

.method public c3(ILjava/lang/Integer;JI)V
    .locals 8

    .line 1
    invoke-virtual {p0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->v(Ljava/lang/Integer;)LDy0/c;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    if-eqz p2, :cond_0

    .line 6
    .line 7
    invoke-static {p2, p3, p4, p5}, LLy0/c;->a(LDy0/c;JI)Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    if-nez p2, :cond_1

    .line 12
    .line 13
    :cond_0
    sget-object p2, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->Companion:Lorg/xbet/betting/core/coupon/models/SingleBetGame$a;

    .line 14
    .line 15
    invoke-virtual {p2}, Lorg/xbet/betting/core/coupon/models/SingleBetGame$a;->a()Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    :cond_1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->u(I)LDy0/b;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    if-eqz p1, :cond_2

    .line 24
    .line 25
    iget-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->e:LHX0/e;

    .line 26
    .line 27
    invoke-static {p1, p3}, LLy0/a;->a(LDy0/b;LHX0/e;)Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 34
    .line 35
    .line 36
    move-result-object p3

    .line 37
    invoke-static {p3}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f:Lm8/a;

    .line 42
    .line 43
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$1;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$1;

    .line 48
    .line 49
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;

    .line 50
    .line 51
    const/4 p3, 0x0

    .line 52
    invoke-direct {v5, p0, p2, p1, p3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    const/16 v6, 0xa

    .line 56
    .line 57
    const/4 v7, 0x0

    .line 58
    const/4 v2, 0x0

    .line 59
    const/4 v4, 0x0

    .line 60
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 61
    .line 62
    .line 63
    :cond_2
    return-void
.end method

.method public e(Landroidx/lifecycle/b0;Landroidx/lifecycle/Q;)V
    .locals 0
    .param p1    # Landroidx/lifecycle/b0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/ui_common/viewmodel/core/k;->e(Landroidx/lifecycle/b0;Landroidx/lifecycle/Q;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->x()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
    .locals 9
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/coupon/models/SimpleBetZip;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$1;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$1;

    .line 16
    .line 17
    new-instance v6, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;

    .line 18
    .line 19
    const/4 v0, 0x0

    .line 20
    invoke-direct {v6, p0, p1, p2, v0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    const/16 v7, 0xa

    .line 24
    .line 25
    const/4 v8, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public j1()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;->a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final u(I)LDy0/b;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->k:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LVy0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LVy0/a;->c()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    const/4 v2, 0x0

    .line 22
    if-eqz v1, :cond_1

    .line 23
    .line 24
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    move-object v3, v1

    .line 29
    check-cast v3, LDy0/d;

    .line 30
    .line 31
    invoke-virtual {v3}, LDy0/d;->d()I

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    if-ne v3, p1, :cond_0

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_1
    move-object v1, v2

    .line 39
    :goto_0
    check-cast v1, LDy0/d;

    .line 40
    .line 41
    if-eqz v1, :cond_2

    .line 42
    .line 43
    invoke-virtual {v1}, LDy0/d;->c()LDy0/b;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    return-object p1

    .line 48
    :cond_2
    return-object v2
.end method

.method public final v(Ljava/lang/Integer;)LDy0/c;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->k:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LVy0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LVy0/a;->b()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_2

    .line 22
    .line 23
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v2, v1

    .line 28
    check-cast v2, LDy0/c;

    .line 29
    .line 30
    invoke-virtual {v2}, LDy0/c;->a()I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    if-nez p1, :cond_1

    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    if-ne v2, v3, :cond_0

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_2
    const/4 v1, 0x0

    .line 45
    :goto_1
    check-cast v1, LDy0/c;

    .line 46
    .line 47
    return-object v1
.end method
