.class public final LGa1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J5\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "LGa1/a;",
        "",
        "LwX0/C;",
        "routerHolder",
        "<init>",
        "(LwX0/C;)V",
        "",
        "partitionId",
        "",
        "providerId",
        "providerName",
        "",
        "subCategoryId",
        "Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
        "openedFromType",
        "",
        "a",
        "(JLjava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V",
        "LwX0/C;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwX0/C;)V
    .locals 0
    .param p1    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LGa1/a;->a:LwX0/C;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(JLjava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V
    .locals 17
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LGa1/a;->a:LwX0/C;

    .line 4
    .line 5
    invoke-virtual {v1}, LwX0/D;->a()LwX0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    new-instance v2, LP91/p;

    .line 12
    .line 13
    invoke-static/range {p3 .. p3}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 14
    .line 15
    .line 16
    move-result-wide v5

    .line 17
    const/16 v15, 0x70

    .line 18
    .line 19
    const/16 v16, 0x0

    .line 20
    .line 21
    const/4 v8, 0x1

    .line 22
    const-wide/16 v9, 0x0

    .line 23
    .line 24
    const/4 v11, 0x0

    .line 25
    const/4 v12, 0x0

    .line 26
    move-wide/from16 v3, p1

    .line 27
    .line 28
    move-object/from16 v7, p4

    .line 29
    .line 30
    move/from16 v13, p5

    .line 31
    .line 32
    move-object/from16 v14, p6

    .line 33
    .line 34
    invoke-direct/range {v2 .. v16}, LP91/p;-><init>(JJLjava/lang/String;ZJIZILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 38
    .line 39
    .line 40
    :cond_0
    return-void
.end method
