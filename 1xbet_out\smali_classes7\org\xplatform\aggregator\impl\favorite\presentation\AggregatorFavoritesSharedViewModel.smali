.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$a;,
        Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;,
        Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;,
        Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\u0008\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0008\t\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008,\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010%\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000f\u0008\u0000\u0018\u0000 \u00e7\u00012\u00020\u0001:\u0006\u00e8\u0001\u00e9\u0001\u00ea\u0001B\u0091\u0002\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u0012\u0006\u0010A\u001a\u00020@\u0012\u0006\u0010C\u001a\u00020B\u00a2\u0006\u0004\u0008D\u0010EJ\u000f\u0010G\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008G\u0010HJ\u000f\u0010I\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008I\u0010HJ\u000f\u0010J\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008J\u0010HJ\u000f\u0010K\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008K\u0010HJ\u000f\u0010L\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008L\u0010HJ\u0018\u0010O\u001a\u00020F2\u0006\u0010N\u001a\u00020MH\u0082@\u00a2\u0006\u0004\u0008O\u0010PJ\u001f\u0010U\u001a\u00020F2\u0006\u0010R\u001a\u00020Q2\u0006\u0010T\u001a\u00020SH\u0002\u00a2\u0006\u0004\u0008U\u0010VJ\u0018\u0010W\u001a\u00020F2\u0006\u0010N\u001a\u00020MH\u0082@\u00a2\u0006\u0004\u0008W\u0010PJ\u0018\u0010X\u001a\u00020F2\u0006\u0010N\u001a\u00020MH\u0082@\u00a2\u0006\u0004\u0008X\u0010PJ\'\u0010^\u001a\u00020M2\u000c\u0010[\u001a\u0008\u0012\u0004\u0012\u00020Z0Y2\u0008\u0008\u0002\u0010]\u001a\u00020\\H\u0002\u00a2\u0006\u0004\u0008^\u0010_J.\u0010`\u001a\u00020F2\u000c\u0010[\u001a\u0008\u0012\u0004\u0012\u00020Z0Y2\u0006\u0010N\u001a\u00020M2\u0006\u0010R\u001a\u00020QH\u0082@\u00a2\u0006\u0004\u0008`\u0010aJ\u001e\u0010c\u001a\u0008\u0012\u0004\u0012\u00020b0Y2\u0006\u0010N\u001a\u00020MH\u0082@\u00a2\u0006\u0004\u0008c\u0010PJ#\u0010f\u001a\u00020F2\u0012\u0010e\u001a\n\u0012\u0006\u0008\u0001\u0012\u00020Q0d\"\u00020QH\u0002\u00a2\u0006\u0004\u0008f\u0010gJ\'\u0010k\u001a\u00020F2\u0006\u0010h\u001a\u00020Z2\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008k\u0010lJ\'\u0010m\u001a\u00020F2\u0006\u0010h\u001a\u00020Z2\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008m\u0010lJ\u001f\u0010o\u001a\u00020n2\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008o\u0010pJ,\u0010s\u001a\u0008\u0012\u0004\u0012\u00020r0Y2\u000c\u0010q\u001a\u0008\u0012\u0004\u0012\u00020b0Y2\u0006\u0010N\u001a\u00020MH\u0082@\u00a2\u0006\u0004\u0008s\u0010tJ\u001f\u0010v\u001a\u00020u2\u0006\u0010N\u001a\u00020M2\u0006\u0010R\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008v\u0010wJ\u000f\u0010x\u001a\u00020FH\u0016\u00a2\u0006\u0004\u0008x\u0010HJ\u0017\u0010y\u001a\u00020F2\u0006\u0010T\u001a\u00020SH\u0016\u00a2\u0006\u0004\u0008y\u0010zJ\u0013\u0010|\u001a\u0008\u0012\u0004\u0012\u00020M0{\u00a2\u0006\u0004\u0008|\u0010}J\u0016\u0010\u0080\u0001\u001a\u0008\u0012\u0004\u0012\u00020\u007f0~\u00a2\u0006\u0006\u0008\u0080\u0001\u0010\u0081\u0001J\u001a\u0010\u0084\u0001\u001a\u00020F2\u0008\u0010\u0083\u0001\u001a\u00030\u0082\u0001\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0085\u0001J\u000f\u0010\u0086\u0001\u001a\u00020F\u00a2\u0006\u0005\u0008\u0086\u0001\u0010HJ\u000f\u0010\u0087\u0001\u001a\u00020F\u00a2\u0006\u0005\u0008\u0087\u0001\u0010HJ\u000f\u0010\u0088\u0001\u001a\u00020F\u00a2\u0006\u0005\u0008\u0088\u0001\u0010HJ\u0012\u0010\u0089\u0001\u001a\u00020nH\u0007\u00a2\u0006\u0006\u0008\u0089\u0001\u0010\u008a\u0001J4\u0010\u008d\u0001\u001a\u00020F2\u0008\u0010\u0083\u0001\u001a\u00030\u0082\u00012\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u00012\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020Q\u00a2\u0006\u0006\u0008\u008d\u0001\u0010\u008e\u0001J2\u0010\u008f\u0001\u001a\u00020F2\u0008\u0010\u0083\u0001\u001a\u00030\u0082\u00012\u0006\u0010h\u001a\u00020Z2\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020Q\u00a2\u0006\u0006\u0008\u008f\u0001\u0010\u0090\u0001J3\u0010\u0092\u0001\u001a\u00020F2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u00012\u0007\u0010\u0091\u0001\u001a\u00020M2\u0006\u0010i\u001a\u00020M2\u0006\u0010j\u001a\u00020Q\u00a2\u0006\u0006\u0008\u0092\u0001\u0010\u0093\u0001R\u0016\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0095\u0001R\u0016\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u0097\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u0099\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u009d\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00a1\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a4\u0001\u0010\u00a5\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a9\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b0\u0001\u0010\u00b1\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b2\u0001\u0010\u00b3\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0001\u0010\u00b5\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b7\u0001R\u0018\u0010\u00bb\u0001\u001a\u00030\u00b8\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001R\u0018\u0010\u00bf\u0001\u001a\u00030\u00bc\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bd\u0001\u0010\u00be\u0001R$\u0010\u00c3\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020b0Y0\u00c0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c1\u0001\u0010\u00c2\u0001R\u0018\u0010\u00c7\u0001\u001a\u00030\u00c4\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c5\u0001\u0010\u00c6\u0001R\u001c\u0010\u00cb\u0001\u001a\u0005\u0018\u00010\u00c8\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00ca\u0001R\u001c\u0010\u00cd\u0001\u001a\u0005\u0018\u00010\u00c8\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cc\u0001\u0010\u00ca\u0001R\u001c\u0010\u00cf\u0001\u001a\u0005\u0018\u00010\u00c8\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00ce\u0001\u0010\u00ca\u0001R%\u0010\u00d3\u0001\u001a\u0010\u0012\u0005\u0012\u00030\u008b\u0001\u0012\u0004\u0012\u00020Z0\u00d0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d1\u0001\u0010\u00d2\u0001R#\u0010\u00d7\u0001\u001a\t\u0012\u0004\u0012\u00020\\0\u00c0\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00d4\u0001\u0010\u00c2\u0001\u001a\u0006\u0008\u00d5\u0001\u0010\u00d6\u0001R#\u0010\u00da\u0001\u001a\t\u0012\u0004\u0012\u00020\\0\u00c0\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00d8\u0001\u0010\u00c2\u0001\u001a\u0006\u0008\u00d9\u0001\u0010\u00d6\u0001R$\u0010\u00e1\u0001\u001a\n\u0012\u0005\u0012\u00030\u00dc\u00010\u00db\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00dd\u0001\u0010\u00de\u0001\u001a\u0006\u0008\u00df\u0001\u0010\u00e0\u0001R\u001e\u0010\u00e3\u0001\u001a\t\u0012\u0004\u0012\u00020M0\u00c0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e2\u0001\u0010\u00c2\u0001R\u001a\u0010\u00e6\u0001\u001a\u00030\u0082\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00e4\u0001\u0010\u00e5\u0001\u00a8\u0006\u00eb\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lp9/g;",
        "observeLoginStateUseCase",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "setNeedFavoritesReUpdateUseCase",
        "Lf81/c;",
        "gamesForNonAuthUseCase",
        "Le81/c;",
        "favoriteGamesFlowScenario",
        "Le81/a;",
        "checkFavoritesGameScenario",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
        "getViewedGamesScenario",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "LHX0/e;",
        "resourceManager",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "LXa0/i;",
        "setShowPopUpBonusUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "LP91/b;",
        "aggregatorNavigator",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LwX0/C;",
        "routerHolder",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(Lp9/g;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lf81/c;Le81/c;Le81/a;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;Lm8/a;Lorg/xbet/analytics/domain/scope/g0;LHX0/e;Lp9/c;LnR/a;LXa0/i;Lorg/xbet/remoteconfig/domain/usecases/i;LGg/a;Lorg/xbet/analytics/domain/scope/I;LP91/b;LxX0/a;LwX0/C;Lfk/s;LAR/a;LZR/a;Lfk/o;Lgk0/a;Lek/f;Lek/d;Lfk/l;LC81/f;)V",
        "",
        "f5",
        "()V",
        "V4",
        "n5",
        "k5",
        "m5",
        "",
        "loggedIn",
        "W4",
        "(ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;",
        "type",
        "",
        "throwable",
        "S4",
        "(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Ljava/lang/Throwable;)V",
        "Y4",
        "l5",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "games",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
        "state",
        "i5",
        "(Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)Z",
        "o5",
        "(Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Ld81/b;",
        "K4",
        "",
        "types",
        "h5",
        "([Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "game",
        "recommended",
        "screenType",
        "H4",
        "(Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "g5",
        "",
        "Q4",
        "(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)I",
        "items",
        "Lt81/b;",
        "T4",
        "(Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "O4",
        "(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)Lorg/xbet/uikit/components/lottie_empty/n;",
        "d4",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/e;",
        "J4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "N4",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "screenName",
        "X4",
        "(Ljava/lang/String;)V",
        "I4",
        "e5",
        "d5",
        "P4",
        "()I",
        "",
        "gameId",
        "a5",
        "(Ljava/lang/String;JZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "b5",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "isFavorite",
        "Z4",
        "(JZZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "y5",
        "Lp9/g;",
        "z5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "A5",
        "Lf81/c;",
        "B5",
        "Le81/c;",
        "C5",
        "Le81/a;",
        "D5",
        "Lf81/a;",
        "E5",
        "Lf81/d;",
        "F5",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "G5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
        "H5",
        "LSX0/c;",
        "I5",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "J5",
        "Lorg/xbet/ui_common/utils/M;",
        "K5",
        "Lm8/a;",
        "L5",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "M5",
        "LHX0/e;",
        "N5",
        "Lp9/c;",
        "O5",
        "LnR/a;",
        "P5",
        "LXa0/i;",
        "Lek0/o;",
        "Q5",
        "Lek0/o;",
        "remoteConfig",
        "Lek0/a;",
        "R5",
        "Lek0/a;",
        "aggregatorModel",
        "Lkotlinx/coroutines/flow/V;",
        "S5",
        "Lkotlinx/coroutines/flow/V;",
        "mutableGamesCategories",
        "Lkotlinx/coroutines/sync/a;",
        "T5",
        "Lkotlinx/coroutines/sync/a;",
        "gamesMutex",
        "Lkotlinx/coroutines/x0;",
        "U5",
        "Lkotlinx/coroutines/x0;",
        "connectionJob",
        "V5",
        "observeFavoriteGamesJob",
        "W5",
        "observeViewedGamesJob",
        "",
        "X5",
        "Ljava/util/Map;",
        "gamesMap",
        "Y5",
        "M4",
        "()Lkotlinx/coroutines/flow/V;",
        "favoriteState",
        "Z5",
        "R4",
        "viewedGamesState",
        "Lkotlinx/coroutines/flow/U;",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;",
        "a6",
        "Lkotlinx/coroutines/flow/U;",
        "L4",
        "()Lkotlinx/coroutines/flow/U;",
        "effect",
        "b6",
        "authorizedStateFlow",
        "c6",
        "Ljava/lang/String;",
        "currentScreenName",
        "d6",
        "c",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d6:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lf81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Le81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final N5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P5:LXa0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Q5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R5:Lek0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T5:Lkotlinx/coroutines/sync/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public U5:Lkotlinx/coroutines/x0;

.field public V5:Lkotlinx/coroutines/x0;

.field public W5:Lkotlinx/coroutines/x0;

.field public final X5:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Y5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Z5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a6:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c6:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lp9/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->d6:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$a;

    return-void
.end method

.method public constructor <init>(Lp9/g;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lf81/c;Le81/c;Le81/a;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;Lm8/a;Lorg/xbet/analytics/domain/scope/g0;LHX0/e;Lp9/c;LnR/a;LXa0/i;Lorg/xbet/remoteconfig/domain/usecases/i;LGg/a;Lorg/xbet/analytics/domain/scope/I;LP91/b;LxX0/a;LwX0/C;Lfk/s;LAR/a;LZR/a;Lfk/o;Lgk0/a;Lek/f;Lek/d;Lfk/l;LC81/f;)V
    .locals 20
    .param p1    # Lp9/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lf81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Le81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LXa0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v2, p11

    .line 4
    .line 5
    move-object/from16 v3, p12

    .line 6
    .line 7
    move-object/from16 v9, p13

    .line 8
    .line 9
    move-object/from16 v13, p15

    .line 10
    .line 11
    move-object/from16 v5, p16

    .line 12
    .line 13
    move-object/from16 v6, p20

    .line 14
    .line 15
    move-object/from16 v7, p21

    .line 16
    .line 17
    move-object/from16 v1, p22

    .line 18
    .line 19
    move-object/from16 v4, p23

    .line 20
    .line 21
    move-object/from16 v8, p24

    .line 22
    .line 23
    move-object/from16 v17, p25

    .line 24
    .line 25
    move-object/from16 v14, p26

    .line 26
    .line 27
    move-object/from16 v15, p27

    .line 28
    .line 29
    move-object/from16 v18, p28

    .line 30
    .line 31
    move-object/from16 v16, p29

    .line 32
    .line 33
    move-object/from16 v10, p30

    .line 34
    .line 35
    move-object/from16 v12, p31

    .line 36
    .line 37
    move-object/from16 v11, p32

    .line 38
    .line 39
    move-object/from16 v19, p33

    .line 40
    .line 41
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V

    .line 42
    .line 43
    .line 44
    move-object/from16 v1, p1

    .line 45
    .line 46
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->y5:Lp9/g;

    .line 47
    .line 48
    move-object/from16 v1, p2

    .line 49
    .line 50
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 51
    .line 52
    move-object/from16 v1, p3

    .line 53
    .line 54
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->A5:Lf81/c;

    .line 55
    .line 56
    move-object/from16 v1, p4

    .line 57
    .line 58
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->B5:Le81/c;

    .line 59
    .line 60
    move-object/from16 v1, p5

    .line 61
    .line 62
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->C5:Le81/a;

    .line 63
    .line 64
    move-object/from16 v1, p6

    .line 65
    .line 66
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->D5:Lf81/a;

    .line 67
    .line 68
    move-object/from16 v1, p7

    .line 69
    .line 70
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->E5:Lf81/d;

    .line 71
    .line 72
    move-object/from16 v1, p8

    .line 73
    .line 74
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->F5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 75
    .line 76
    move-object/from16 v1, p9

    .line 77
    .line 78
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->G5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    .line 79
    .line 80
    move-object/from16 v1, p10

    .line 81
    .line 82
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->H5:LSX0/c;

    .line 83
    .line 84
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->I5:Lorg/xbet/ui_common/utils/internet/a;

    .line 85
    .line 86
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->J5:Lorg/xbet/ui_common/utils/M;

    .line 87
    .line 88
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K5:Lm8/a;

    .line 89
    .line 90
    move-object/from16 v1, p14

    .line 91
    .line 92
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->L5:Lorg/xbet/analytics/domain/scope/g0;

    .line 93
    .line 94
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->M5:LHX0/e;

    .line 95
    .line 96
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->N5:Lp9/c;

    .line 97
    .line 98
    move-object/from16 v1, p17

    .line 99
    .line 100
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->O5:LnR/a;

    .line 101
    .line 102
    move-object/from16 v1, p18

    .line 103
    .line 104
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->P5:LXa0/i;

    .line 105
    .line 106
    invoke-interface/range {p19 .. p19}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q5:Lek0/o;

    .line 111
    .line 112
    invoke-virtual {v1}, Lek0/o;->o()Lek0/a;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    .line 117
    .line 118
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 127
    .line 128
    const/4 v1, 0x1

    .line 129
    const/4 v2, 0x0

    .line 130
    const/4 v3, 0x0

    .line 131
    invoke-static {v3, v1, v2}, Lkotlinx/coroutines/sync/MutexKt;->b(ZILjava/lang/Object;)Lkotlinx/coroutines/sync/a;

    .line 132
    .line 133
    .line 134
    move-result-object v1

    .line 135
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->T5:Lkotlinx/coroutines/sync/a;

    .line 136
    .line 137
    new-instance v1, Ljava/util/LinkedHashMap;

    .line 138
    .line 139
    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 140
    .line 141
    .line 142
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X5:Ljava/util/Map;

    .line 143
    .line 144
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;

    .line 145
    .line 146
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 147
    .line 148
    .line 149
    move-result-object v2

    .line 150
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 151
    .line 152
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 157
    .line 158
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 159
    .line 160
    .line 161
    move-result-object v1

    .line 162
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->a6:Lkotlinx/coroutines/flow/U;

    .line 163
    .line 164
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 165
    .line 166
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->b6:Lkotlinx/coroutines/flow/V;

    .line 171
    .line 172
    const-string v1, ""

    .line 173
    .line 174
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 175
    .line 176
    return-void
.end method

.method public static final synthetic A4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic B4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->i5(Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic C4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->k5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->l5(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic E4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->m5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->n5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->o5(Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final U4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->a6:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$b;

    .line 4
    .line 5
    invoke-direct {v0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$b;-><init>(Ld81/b;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method private final V4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->I5:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeConnection$1;

    .line 17
    .line 18
    invoke-direct {v2, p0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeConnection$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K5:Lm8/a;

    .line 30
    .line 31
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public static final c5(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method private final f5()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    :cond_1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->n5()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static synthetic j5(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;ILjava/lang/Object;)Z
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    invoke-interface {p2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    check-cast p2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;

    .line 12
    .line 13
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->i5(Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)Z

    .line 14
    .line 15
    .line 16
    move-result p0

    .line 17
    return p0
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c5(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->U4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic n4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)Lf81/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->D5:Lf81/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final n5()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 15
    .line 16
    .line 17
    move-result-object v4

    .line 18
    new-instance v6, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$update$1;

    .line 19
    .line 20
    invoke-direct {v6, p0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$update$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    const/4 v7, 0x2

    .line 24
    const/4 v8, 0x0

    .line 25
    const/4 v5, 0x0

    .line 26
    invoke-static/range {v3 .. v8}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final synthetic o4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)Lek0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K4(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic q4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->N5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)Lf81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->E5:Lf81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q4(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->S4(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->T4(Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W4(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y4(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic z4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->f5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final H4(Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$addFavorite$1;

    .line 10
    .line 11
    const/4 v7, 0x0

    .line 12
    move-object v3, p0

    .line 13
    move-object v6, p1

    .line 14
    move v4, p2

    .line 15
    move-object v5, p3

    .line 16
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$addFavorite$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/4 v4, 0x2

    .line 20
    const/4 v5, 0x0

    .line 21
    move-object v3, v2

    .line 22
    const/4 v2, 0x0

    .line 23
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final I4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final J4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->b6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final K4(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x2

    .line 36
    const/4 v3, 0x1

    .line 37
    const/4 v8, 0x0

    .line 38
    if-eqz v1, :cond_3

    .line 39
    .line 40
    if-eq v1, v3, :cond_2

    .line 41
    .line 42
    if-ne v1, v2, :cond_1

    .line 43
    .line 44
    iget-object p1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast p1, Lkotlinx/coroutines/sync/a;

    .line 47
    .line 48
    :try_start_0
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    check-cast p2, Lkotlin/Result;

    .line 52
    .line 53
    invoke-virtual {p2}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 57
    goto :goto_4

    .line 58
    :catchall_0
    move-exception v0

    .line 59
    move-object p2, v0

    .line 60
    goto :goto_5

    .line 61
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 64
    .line 65
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 66
    .line 67
    .line 68
    throw p1

    .line 69
    :cond_2
    iget-boolean p1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->Z$0:Z

    .line 70
    .line 71
    iget-object v1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->L$0:Ljava/lang/Object;

    .line 72
    .line 73
    check-cast v1, Lkotlinx/coroutines/sync/a;

    .line 74
    .line 75
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 76
    .line 77
    .line 78
    move v3, p1

    .line 79
    move-object p1, v1

    .line 80
    goto :goto_2

    .line 81
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->T5:Lkotlinx/coroutines/sync/a;

    .line 85
    .line 86
    iput-object p2, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput-boolean p1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->Z$0:Z

    .line 89
    .line 90
    iput v3, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->label:I

    .line 91
    .line 92
    invoke-interface {p2, v8, v7}, Lkotlinx/coroutines/sync/a;->g(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    if-ne v1, v0, :cond_4

    .line 97
    .line 98
    goto :goto_3

    .line 99
    :cond_4
    move v3, p1

    .line 100
    move-object p1, p2

    .line 101
    :goto_2
    :try_start_1
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 102
    .line 103
    invoke-interface {p2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object p2

    .line 107
    check-cast p2, Ljava/util/Collection;

    .line 108
    .line 109
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 110
    .line 111
    .line 112
    move-result v1

    .line 113
    if-eqz v1, :cond_7

    .line 114
    .line 115
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->A5:Lf81/c;

    .line 116
    .line 117
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    .line 118
    .line 119
    invoke-virtual {p2}, Lek0/a;->m()Z

    .line 120
    .line 121
    .line 122
    move-result p2

    .line 123
    iput-object p1, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->L$0:Ljava/lang/Object;

    .line 124
    .line 125
    iput v2, v7, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$getCategoriesGames$1;->label:I

    .line 126
    .line 127
    const/16 v4, 0x8

    .line 128
    .line 129
    const/4 v5, 0x0

    .line 130
    const/4 v6, 0x0

    .line 131
    move v2, p2

    .line 132
    invoke-interface/range {v1 .. v7}, Lf81/c;->a(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object p2

    .line 136
    if-ne p2, v0, :cond_5

    .line 137
    .line 138
    :goto_3
    return-object v0

    .line 139
    :cond_5
    :goto_4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    invoke-static {p2}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 144
    .line 145
    .line 146
    move-result v1

    .line 147
    if-eqz v1, :cond_6

    .line 148
    .line 149
    move-object p2, v0

    .line 150
    :cond_6
    check-cast p2, Ljava/util/List;

    .line 151
    .line 152
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 153
    .line 154
    invoke-interface {v0, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 155
    .line 156
    .line 157
    :cond_7
    check-cast p2, Ljava/util/List;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 158
    .line 159
    invoke-interface {p1, v8}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 160
    .line 161
    .line 162
    return-object p2

    .line 163
    :goto_5
    invoke-interface {p1, v8}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 164
    .line 165
    .line 166
    throw p2
.end method

.method public final L4()Lkotlinx/coroutines/flow/U;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->a6:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final M4()Lkotlinx/coroutines/flow/V;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final N4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->F5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final O4(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$d;->a:[I

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    aget p2, v0, p2

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p2, v0, :cond_2

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-ne p2, v0, :cond_1

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    sget p1, Lpb/k;->casino_viewed_empty:I

    .line 18
    .line 19
    :goto_0
    move v6, p1

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    sget p1, Lpb/k;->casino_viewed_no_auth:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_2
    if-eqz p1, :cond_3

    .line 31
    .line 32
    sget p1, Lpb/k;->casino_favorites_empty:I

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_3
    sget p1, Lpb/k;->casino_favorites_no_auth:I

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :goto_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->H5:LSX0/c;

    .line 39
    .line 40
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    .line 41
    .line 42
    invoke-virtual {p1}, Lek0/a;->m()Z

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    if-eqz p1, :cond_4

    .line 47
    .line 48
    sget-object p1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->AGGREGATOR_ALT:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 49
    .line 50
    :goto_2
    move-object v1, p1

    .line 51
    goto :goto_3

    .line 52
    :cond_4
    sget-object p1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->AGGREGATOR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 53
    .line 54
    goto :goto_2

    .line 55
    :goto_3
    const/16 v10, 0x1de

    .line 56
    .line 57
    const/4 v11, 0x0

    .line 58
    const/4 v2, 0x0

    .line 59
    const/4 v3, 0x0

    .line 60
    const/4 v4, 0x0

    .line 61
    const/4 v5, 0x0

    .line 62
    const/4 v7, 0x0

    .line 63
    const/4 v8, 0x0

    .line 64
    const/4 v9, 0x0

    .line 65
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    return-object p1
.end method

.method public final P4()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q5:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-static {v0, v1}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    return v0
.end method

.method public final Q4(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)I
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    const/16 p1, 0x73

    .line 4
    .line 5
    return p1

    .line 6
    :cond_0
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 7
    .line 8
    if-ne p2, p1, :cond_1

    .line 9
    .line 10
    const/16 p1, 0x1fbb

    .line 11
    .line 12
    return p1

    .line 13
    :cond_1
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->VIEWED:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 14
    .line 15
    if-ne p2, p1, :cond_2

    .line 16
    .line 17
    const/16 p1, 0x1fb5

    .line 18
    .line 19
    return p1

    .line 20
    :cond_2
    const/4 p1, 0x0

    .line 21
    return p1
.end method

.method public final R4()Lkotlinx/coroutines/flow/V;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final S4(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->J5:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    invoke-interface {v0, p2}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    instance-of v0, p2, Ljava/net/SocketTimeoutException;

    .line 7
    .line 8
    if-nez v0, :cond_1

    .line 9
    .line 10
    instance-of v0, p2, Ljava/net/ConnectException;

    .line 11
    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    instance-of v0, p2, Ljava/net/UnknownHostException;

    .line 15
    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 p2, 0x1

    .line 20
    new-array p2, p2, [Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 21
    .line 22
    const/4 v0, 0x0

    .line 23
    aput-object p1, p2, v0

    .line 24
    .line 25
    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->h5([Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-interface {v0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-interface {p1, v0, p2}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 42
    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V4()V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public final T4(Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 26
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lt81/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;

    .line 25
    .line 26
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 36
    .line 37
    const/4 v7, 0x1

    .line 38
    if-eqz v4, :cond_2

    .line 39
    .line 40
    if-ne v4, v7, :cond_1

    .line 41
    .line 42
    iget v4, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$2:I

    .line 43
    .line 44
    iget-boolean v8, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->Z$1:Z

    .line 45
    .line 46
    iget v9, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$1:I

    .line 47
    .line 48
    iget v10, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$0:I

    .line 49
    .line 50
    iget-boolean v11, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->Z$0:Z

    .line 51
    .line 52
    iget-object v12, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$9:Ljava/lang/Object;

    .line 53
    .line 54
    check-cast v12, Ljava/util/Collection;

    .line 55
    .line 56
    iget-object v13, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$8:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v13, Lorg/xplatform/aggregator/api/model/Game;

    .line 59
    .line 60
    iget-object v14, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$7:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v14, LHX0/e;

    .line 63
    .line 64
    iget-object v15, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$6:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast v15, Ljava/util/Collection;

    .line 67
    .line 68
    iget-object v6, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$5:Ljava/lang/Object;

    .line 69
    .line 70
    check-cast v6, Ljava/util/Iterator;

    .line 71
    .line 72
    iget-object v7, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$4:Ljava/lang/Object;

    .line 73
    .line 74
    check-cast v7, Ljava/util/Collection;

    .line 75
    .line 76
    iget-object v5, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$3:Ljava/lang/Object;

    .line 77
    .line 78
    check-cast v5, Ljava/lang/String;

    .line 79
    .line 80
    move-object/from16 v17, v1

    .line 81
    .line 82
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$2:Ljava/lang/Object;

    .line 83
    .line 84
    check-cast v1, Ld81/b;

    .line 85
    .line 86
    move-object/from16 p1, v1

    .line 87
    .line 88
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$1:Ljava/lang/Object;

    .line 89
    .line 90
    check-cast v1, Ljava/util/Iterator;

    .line 91
    .line 92
    move-object/from16 p2, v1

    .line 93
    .line 94
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$0:Ljava/lang/Object;

    .line 95
    .line 96
    check-cast v1, Ljava/util/Collection;

    .line 97
    .line 98
    invoke-static/range {v17 .. v17}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 99
    .line 100
    .line 101
    move-object/from16 v16, v2

    .line 102
    .line 103
    move-object v2, v1

    .line 104
    move-object v1, v3

    .line 105
    move v3, v4

    .line 106
    move-object/from16 v4, v16

    .line 107
    .line 108
    move-object/from16 v16, v14

    .line 109
    .line 110
    move-object/from16 v18, v15

    .line 111
    .line 112
    move-object v15, v7

    .line 113
    move-object v14, v13

    .line 114
    move-object/from16 v7, p2

    .line 115
    .line 116
    move-object v13, v12

    .line 117
    move v12, v11

    .line 118
    move v11, v10

    .line 119
    move-object v10, v6

    .line 120
    move-object/from16 v6, p1

    .line 121
    .line 122
    goto/16 :goto_4

    .line 123
    .line 124
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 125
    .line 126
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 127
    .line 128
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 129
    .line 130
    .line 131
    throw v1

    .line 132
    :cond_2
    move-object/from16 v17, v1

    .line 133
    .line 134
    invoke-static/range {v17 .. v17}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 135
    .line 136
    .line 137
    new-instance v1, Ljava/util/ArrayList;

    .line 138
    .line 139
    move-object/from16 v4, p1

    .line 140
    .line 141
    const/16 v5, 0xa

    .line 142
    .line 143
    invoke-static {v4, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 144
    .line 145
    .line 146
    move-result v6

    .line 147
    invoke-direct {v1, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 148
    .line 149
    .line 150
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 151
    .line 152
    .line 153
    move-result-object v4

    .line 154
    move-object v5, v4

    .line 155
    move-object v4, v2

    .line 156
    move-object v2, v1

    .line 157
    :goto_1
    move/from16 v1, p2

    .line 158
    .line 159
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 160
    .line 161
    .line 162
    move-result v6

    .line 163
    if-eqz v6, :cond_9

    .line 164
    .line 165
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 166
    .line 167
    .line 168
    move-result-object v6

    .line 169
    check-cast v6, Ld81/b;

    .line 170
    .line 171
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q5:Lek0/o;

    .line 172
    .line 173
    invoke-virtual {v7}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 174
    .line 175
    .line 176
    move-result-object v7

    .line 177
    const/4 v8, 0x1

    .line 178
    invoke-static {v7, v8}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 179
    .line 180
    .line 181
    move-result v7

    .line 182
    invoke-virtual {v6}, Ld81/b;->h()Ljava/lang/String;

    .line 183
    .line 184
    .line 185
    move-result-object v8

    .line 186
    invoke-virtual {v6}, Ld81/b;->e()J

    .line 187
    .line 188
    .line 189
    move-result-wide v9

    .line 190
    sget-object v11, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 191
    .line 192
    invoke-virtual {v11}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 193
    .line 194
    .line 195
    move-result-wide v11

    .line 196
    cmp-long v13, v9, v11

    .line 197
    .line 198
    if-nez v13, :cond_3

    .line 199
    .line 200
    const/4 v9, 0x1

    .line 201
    goto :goto_2

    .line 202
    :cond_3
    const/4 v9, 0x0

    .line 203
    :goto_2
    invoke-virtual {v6}, Ld81/b;->c()Ljava/util/List;

    .line 204
    .line 205
    .line 206
    move-result-object v10

    .line 207
    new-instance v11, Ljava/util/ArrayList;

    .line 208
    .line 209
    const/16 v12, 0xa

    .line 210
    .line 211
    invoke-static {v10, v12}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 212
    .line 213
    .line 214
    move-result v13

    .line 215
    invoke-direct {v11, v13}, Ljava/util/ArrayList;-><init>(I)V

    .line 216
    .line 217
    .line 218
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 219
    .line 220
    .line 221
    move-result-object v10

    .line 222
    move v15, v7

    .line 223
    move-object v7, v5

    .line 224
    move-object v5, v8

    .line 225
    move v8, v15

    .line 226
    move-object v15, v11

    .line 227
    move-object v11, v2

    .line 228
    :goto_3
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 229
    .line 230
    .line 231
    move-result v13

    .line 232
    if-eqz v13, :cond_7

    .line 233
    .line 234
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 235
    .line 236
    .line 237
    move-result-object v13

    .line 238
    check-cast v13, Lorg/xplatform/aggregator/api/model/Game;

    .line 239
    .line 240
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X5:Ljava/util/Map;

    .line 241
    .line 242
    invoke-virtual {v13}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 243
    .line 244
    .line 245
    move-result-wide v16

    .line 246
    invoke-static/range {v16 .. v17}, LHc/a;->f(J)Ljava/lang/Long;

    .line 247
    .line 248
    .line 249
    move-result-object v12

    .line 250
    invoke-interface {v14, v12, v13}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 251
    .line 252
    .line 253
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->M5:LHX0/e;

    .line 254
    .line 255
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    .line 256
    .line 257
    invoke-virtual {v12}, Lek0/a;->m()Z

    .line 258
    .line 259
    .line 260
    move-result v12

    .line 261
    if-eqz v1, :cond_6

    .line 262
    .line 263
    move-object/from16 v24, v3

    .line 264
    .line 265
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->C5:Le81/a;

    .line 266
    .line 267
    iput-object v2, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$0:Ljava/lang/Object;

    .line 268
    .line 269
    iput-object v7, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$1:Ljava/lang/Object;

    .line 270
    .line 271
    iput-object v6, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$2:Ljava/lang/Object;

    .line 272
    .line 273
    iput-object v5, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$3:Ljava/lang/Object;

    .line 274
    .line 275
    iput-object v15, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$4:Ljava/lang/Object;

    .line 276
    .line 277
    iput-object v10, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$5:Ljava/lang/Object;

    .line 278
    .line 279
    iput-object v15, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$6:Ljava/lang/Object;

    .line 280
    .line 281
    iput-object v14, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$7:Ljava/lang/Object;

    .line 282
    .line 283
    iput-object v13, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$8:Ljava/lang/Object;

    .line 284
    .line 285
    iput-object v11, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->L$9:Ljava/lang/Object;

    .line 286
    .line 287
    iput-boolean v1, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->Z$0:Z

    .line 288
    .line 289
    iput v8, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$0:I

    .line 290
    .line 291
    iput v9, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$1:I

    .line 292
    .line 293
    iput-boolean v12, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->Z$1:Z

    .line 294
    .line 295
    iput v1, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->I$2:I

    .line 296
    .line 297
    move/from16 p2, v1

    .line 298
    .line 299
    const/4 v1, 0x1

    .line 300
    iput v1, v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 301
    .line 302
    invoke-interface {v3, v13, v4}, Le81/a;->a(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 303
    .line 304
    .line 305
    move-result-object v3

    .line 306
    move-object/from16 v1, v24

    .line 307
    .line 308
    if-ne v3, v1, :cond_4

    .line 309
    .line 310
    return-object v1

    .line 311
    :cond_4
    move-object/from16 v17, v3

    .line 312
    .line 313
    move-object/from16 v16, v14

    .line 314
    .line 315
    move-object/from16 v18, v15

    .line 316
    .line 317
    move/from16 v3, p2

    .line 318
    .line 319
    move-object v14, v13

    .line 320
    move-object v13, v11

    .line 321
    move v11, v8

    .line 322
    move v8, v12

    .line 323
    move v12, v3

    .line 324
    :goto_4
    if-eqz v3, :cond_5

    .line 325
    .line 326
    const/4 v3, 0x1

    .line 327
    goto :goto_5

    .line 328
    :cond_5
    const/4 v3, 0x0

    .line 329
    :goto_5
    check-cast v17, Ljava/lang/Boolean;

    .line 330
    .line 331
    invoke-virtual/range {v17 .. v17}, Ljava/lang/Boolean;->booleanValue()Z

    .line 332
    .line 333
    .line 334
    move-result v17

    .line 335
    move/from16 v21, v3

    .line 336
    .line 337
    move/from16 v22, v8

    .line 338
    .line 339
    move v8, v11

    .line 340
    move-object v11, v13

    .line 341
    move-object/from16 v19, v14

    .line 342
    .line 343
    move-object v3, v15

    .line 344
    move-object/from16 v20, v16

    .line 345
    .line 346
    move/from16 v23, v17

    .line 347
    .line 348
    move-object/from16 v15, v18

    .line 349
    .line 350
    goto :goto_6

    .line 351
    :cond_6
    move/from16 p2, v1

    .line 352
    .line 353
    move-object v1, v3

    .line 354
    move/from16 v21, p2

    .line 355
    .line 356
    move/from16 v22, v12

    .line 357
    .line 358
    move-object/from16 v19, v13

    .line 359
    .line 360
    move-object/from16 v20, v14

    .line 361
    .line 362
    move-object v3, v15

    .line 363
    const/16 v23, 0x0

    .line 364
    .line 365
    move/from16 v12, v21

    .line 366
    .line 367
    :goto_6
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q5:Lek0/o;

    .line 368
    .line 369
    invoke-virtual {v13}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 370
    .line 371
    .line 372
    move-result-object v24

    .line 373
    invoke-virtual {v6}, Ld81/b;->e()J

    .line 374
    .line 375
    .line 376
    move-result-wide v13

    .line 377
    long-to-int v14, v13

    .line 378
    invoke-static {v14}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 379
    .line 380
    .line 381
    move-result-object v25

    .line 382
    invoke-static/range {v19 .. v25}, LQ91/c;->a(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;)LN21/k;

    .line 383
    .line 384
    .line 385
    move-result-object v13

    .line 386
    invoke-interface {v15, v13}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 387
    .line 388
    .line 389
    move-object v15, v3

    .line 390
    move-object v3, v1

    .line 391
    move v1, v12

    .line 392
    const/16 v12, 0xa

    .line 393
    .line 394
    goto/16 :goto_3

    .line 395
    .line 396
    :cond_7
    move/from16 p2, v1

    .line 397
    .line 398
    move-object v1, v3

    .line 399
    move-object/from16 v20, v15

    .line 400
    .line 401
    check-cast v20, Ljava/util/List;

    .line 402
    .line 403
    new-instance v16, Lt81/b;

    .line 404
    .line 405
    if-eqz v9, :cond_8

    .line 406
    .line 407
    const/16 v19, 0x1

    .line 408
    .line 409
    goto :goto_7

    .line 410
    :cond_8
    const/16 v19, 0x0

    .line 411
    .line 412
    :goto_7
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/e;

    .line 413
    .line 414
    invoke-direct {v3, v0, v6}, Lorg/xplatform/aggregator/impl/favorite/presentation/e;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)V

    .line 415
    .line 416
    .line 417
    const/16 v22, 0x1

    .line 418
    .line 419
    const/16 v23, 0x0

    .line 420
    .line 421
    move-object/from16 v21, v3

    .line 422
    .line 423
    move-object/from16 v18, v5

    .line 424
    .line 425
    move/from16 v17, v8

    .line 426
    .line 427
    invoke-direct/range {v16 .. v23}, Lt81/b;-><init>(ILjava/lang/String;ZLjava/util/List;Lkotlin/jvm/functions/Function0;ZZ)V

    .line 428
    .line 429
    .line 430
    move-object/from16 v3, v16

    .line 431
    .line 432
    invoke-interface {v11, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 433
    .line 434
    .line 435
    move-object v3, v1

    .line 436
    move-object v5, v7

    .line 437
    goto/16 :goto_1

    .line 438
    .line 439
    :cond_9
    check-cast v2, Ljava/util/List;

    .line 440
    .line 441
    return-object v2
.end method

.method public final W4(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    invoke-interface {p2}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    const/4 v0, 0x1

    .line 10
    if-ne p2, v0, :cond_0

    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1

    .line 15
    :cond_0
    sget-object p2, Lkotlinx/coroutines/CoroutineExceptionHandler;->Y3:Lkotlinx/coroutines/CoroutineExceptionHandler$a;

    .line 16
    .line 17
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$e;

    .line 18
    .line 19
    invoke-direct {v0, p2, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$e;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V

    .line 20
    .line 21
    .line 22
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->B5:Le81/c;

    .line 23
    .line 24
    invoke-interface {p2}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;

    .line 29
    .line 30
    const/4 v2, 0x0

    .line 31
    invoke-direct {v1, p0, p1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    invoke-static {p2, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    invoke-static {p2, v0}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 53
    .line 54
    return-object p1
.end method

.method public final X4(Ljava/lang/String;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->b6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->N5:Lp9/c;

    .line 4
    .line 5
    invoke-virtual {v1}, Lp9/c;->a()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 17
    .line 18
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;->a()V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->y5:Lp9/g;

    .line 22
    .line 23
    invoke-virtual {v0}, Lp9/g;->a()Lkotlinx/coroutines/flow/e;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeLoginState$1;

    .line 28
    .line 29
    const/4 v2, 0x0

    .line 30
    invoke-direct {v1, p0, p1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeLoginState$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-static {v0, v1}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K5:Lm8/a;

    .line 50
    .line 51
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-static {v0, v1}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 60
    .line 61
    .line 62
    return-void
.end method

.method public final Y4(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    invoke-interface {p2}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    const/4 v0, 0x1

    .line 10
    if-ne p2, v0, :cond_0

    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1

    .line 15
    :cond_0
    sget-object p2, Lkotlinx/coroutines/CoroutineExceptionHandler;->Y3:Lkotlinx/coroutines/CoroutineExceptionHandler$a;

    .line 16
    .line 17
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f;

    .line 18
    .line 19
    invoke-direct {v0, p2, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V

    .line 20
    .line 21
    .line 22
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->G5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    .line 23
    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-virtual {p2, v1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->b(Z)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeViewedGames$2;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-direct {v1, p0, p1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeViewedGames$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)V

    .line 33
    .line 34
    .line 35
    invoke-static {p2, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 40
    .line 41
    .line 42
    move-result-object p2

    .line 43
    invoke-static {p2, v0}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object p2

    .line 47
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 54
    .line 55
    return-object p1
.end method

.method public final Z4(JZZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 1
    .param p5    # Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p1, :cond_1

    .line 14
    .line 15
    if-eqz p3, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0, p1, p4, p5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->g5(Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    invoke-virtual {p0, p1, p4, p5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->H4(Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public final a5(Ljava/lang/String;JZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    invoke-interface {v0, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    check-cast p2, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p2, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0, p1, p2, p4, p5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->b5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method

.method public final b5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 11
    .line 12
    :goto_0
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 13
    .line 14
    invoke-virtual {p0, p3, p4}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q4(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->O5:LnR/a;

    .line 19
    .line 20
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->c6:Ljava/lang/String;

    .line 21
    .line 22
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 23
    .line 24
    .line 25
    move-result-wide v0

    .line 26
    long-to-int v1, v0

    .line 27
    const-string v0, "cas_favorite"

    .line 28
    .line 29
    invoke-interface {p3, p4, v1, v0}, LnR/a;->h(Ljava/lang/String;ILjava/lang/String;)V

    .line 30
    .line 31
    .line 32
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->L5:Lorg/xbet/analytics/domain/scope/g0;

    .line 33
    .line 34
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 35
    .line 36
    .line 37
    move-result-wide v0

    .line 38
    int-to-long v2, p1

    .line 39
    invoke-virtual {p3, v0, v1, v2, v3}, Lorg/xbet/analytics/domain/scope/g0;->O(JJ)V

    .line 40
    .line 41
    .line 42
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->F5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 43
    .line 44
    new-instance p4, Lorg/xplatform/aggregator/impl/favorite/presentation/f;

    .line 45
    .line 46
    invoke-direct {p4, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/f;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p3, p2, p1, p4}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method public d4()V
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 3
    .line 4
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->VIEWED:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    aput-object v1, v0, v2

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->h5([Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 15
    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->V4()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final d5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->P5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->J5:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showCustomError$1;

    .line 11
    .line 12
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showCustomError$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final e5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->P5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final g5(Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$removeFavorite$1;

    .line 10
    .line 11
    const/4 v7, 0x0

    .line 12
    move-object v3, p0

    .line 13
    move-object v6, p1

    .line 14
    move v4, p2

    .line 15
    move-object v5, p3

    .line 16
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$removeFavorite$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/4 v4, 0x2

    .line 20
    const/4 v5, 0x0

    .line 21
    move-object v3, v2

    .line 22
    const/4 v2, 0x0

    .line 23
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final varargs h5([Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->H5:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    sget v8, Lpb/k;->try_again_text:I

    .line 8
    .line 9
    new-instance v9, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$setupObserveGamesErrorLottie$lottieConfig$1;

    .line 10
    .line 11
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$setupObserveGamesErrorLottie$lottieConfig$1;-><init>(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    const/16 v10, 0x5e

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    array-length v1, p1

    .line 27
    const/4 v2, 0x0

    .line 28
    :goto_0
    if-ge v2, v1, :cond_2

    .line 29
    .line 30
    aget-object v3, p1, v2

    .line 31
    .line 32
    sget-object v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$d;->a:[I

    .line 33
    .line 34
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    aget v3, v4, v3

    .line 39
    .line 40
    const/4 v4, 0x1

    .line 41
    if-eq v3, v4, :cond_1

    .line 42
    .line 43
    const/4 v4, 0x2

    .line 44
    if-ne v3, v4, :cond_0

    .line 45
    .line 46
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 47
    .line 48
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;

    .line 49
    .line 50
    invoke-direct {v4, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 51
    .line 52
    .line 53
    invoke-interface {v3, v4}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 58
    .line 59
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 60
    .line 61
    .line 62
    throw p1

    .line 63
    :cond_1
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 64
    .line 65
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;

    .line 66
    .line 67
    invoke-direct {v4, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 68
    .line 69
    .line 70
    invoke-interface {v3, v4}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    :goto_1
    add-int/lit8 v2, v2, 0x1

    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_2
    return-void
.end method

.method public final i5(Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
            ")Z"
        }
    .end annotation

    .line 1
    instance-of p2, p2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;

    .line 2
    .line 3
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    const/4 v0, 0x1

    .line 8
    xor-int/2addr p1, v0

    .line 9
    if-eq p2, p1, :cond_0

    .line 10
    .line 11
    return v0

    .line 12
    :cond_0
    const/4 p1, 0x0

    .line 13
    return p1
.end method

.method public final k5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final l5(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    instance-of v1, p2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;

    .line 3
    .line 4
    if-eqz v1, :cond_0

    .line 5
    .line 6
    move-object v1, p2

    .line 7
    check-cast v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;

    .line 8
    .line 9
    iget v2, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->label:I

    .line 10
    .line 11
    const/high16 v3, -0x80000000

    .line 12
    .line 13
    and-int v4, v2, v3

    .line 14
    .line 15
    if-eqz v4, :cond_0

    .line 16
    .line 17
    sub-int/2addr v2, v3

    .line 18
    iput v2, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->label:I

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;

    .line 22
    .line 23
    invoke-direct {v1, p0, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    :goto_0
    iget-object p2, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->result:Ljava/lang/Object;

    .line 27
    .line 28
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    iget v3, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->label:I

    .line 33
    .line 34
    if-eqz v3, :cond_2

    .line 35
    .line 36
    if-ne v3, v0, :cond_1

    .line 37
    .line 38
    iget-boolean p1, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->Z$0:Z

    .line 39
    .line 40
    iget-object v3, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->L$0:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast v3, Ljava/util/Iterator;

    .line 43
    .line 44
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    goto :goto_1

    .line 48
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw p1

    .line 56
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->k5()V

    .line 60
    .line 61
    .line 62
    const/4 p2, 0x2

    .line 63
    new-array p2, p2, [Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 64
    .line 65
    sget-object v3, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 66
    .line 67
    const/4 v4, 0x0

    .line 68
    aput-object v3, p2, v4

    .line 69
    .line 70
    sget-object v3, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->VIEWED:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 71
    .line 72
    aput-object v3, p2, v0

    .line 73
    .line 74
    invoke-static {p2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 75
    .line 76
    .line 77
    move-result-object p2

    .line 78
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 79
    .line 80
    .line 81
    move-result-object p2

    .line 82
    move-object v3, p2

    .line 83
    :cond_3
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 84
    .line 85
    .line 86
    move-result p2

    .line 87
    if-eqz p2, :cond_4

    .line 88
    .line 89
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object p2

    .line 93
    check-cast p2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 94
    .line 95
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 96
    .line 97
    .line 98
    move-result-object v4

    .line 99
    iput-object v3, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->L$0:Ljava/lang/Object;

    .line 100
    .line 101
    iput-boolean p1, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->Z$0:Z

    .line 102
    .line 103
    iput v0, v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$showNonAuthGames$1;->label:I

    .line 104
    .line 105
    invoke-virtual {p0, v4, p1, p2, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->o5(Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object p2

    .line 109
    if-ne p2, v2, :cond_3

    .line 110
    .line 111
    return-object v2

    .line 112
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 113
    .line 114
    return-object p1
.end method

.method public final m5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final o5(Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;Z",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    move-object/from16 v0, p0

    move/from16 v1, p2

    move-object/from16 v2, p4

    const/4 v3, 0x1

    instance-of v5, v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;

    if-eqz v5, :cond_0

    move-object v5, v2

    check-cast v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;

    iget v6, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    const/high16 v7, -0x80000000

    and-int v8, v6, v7

    if-eqz v8, :cond_0

    sub-int/2addr v6, v7

    iput v6, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    goto :goto_0

    :cond_0
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;

    invoke-direct {v5, v0, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lkotlin/coroutines/e;)V

    :goto_0
    iget-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->result:Ljava/lang/Object;

    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    move-result-object v6

    .line 1
    iget v7, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    const/4 v8, 0x0

    const/4 v9, 0x5

    const/4 v10, 0x4

    const/4 v11, 0x3

    const/4 v12, 0x2

    if-eqz v7, :cond_6

    if-eq v7, v3, :cond_5

    if-eq v7, v12, :cond_4

    if-eq v7, v11, :cond_3

    if-eq v7, v10, :cond_2

    if-ne v7, v9, :cond_1

    iget v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->I$0:I

    iget-boolean v7, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$1:Z

    iget-boolean v8, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iget-object v10, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$5:Ljava/lang/Object;

    check-cast v10, Ljava/util/Collection;

    iget-object v11, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$4:Ljava/lang/Object;

    check-cast v11, Lorg/xplatform/aggregator/api/model/Game;

    iget-object v13, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$3:Ljava/lang/Object;

    check-cast v13, LHX0/e;

    iget-object v14, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$2:Ljava/lang/Object;

    check-cast v14, Ljava/util/Iterator;

    iget-object v15, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    check-cast v15, Ljava/util/Collection;

    const/16 v16, 0x0

    iget-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    goto/16 :goto_7

    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_2
    iget-object v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    check-cast v1, Ljava/util/Collection;

    iget-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    goto/16 :goto_4

    :cond_3
    iget-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iget-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$2:Ljava/lang/Object;

    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iget-object v7, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    check-cast v7, Ljava/util/Collection;

    iget-object v9, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    check-cast v9, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    move-object/from16 v26, v7

    move-object v7, v2

    move-object/from16 v2, v26

    goto/16 :goto_3

    :cond_4
    const/16 v16, 0x0

    iget-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iget-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    goto :goto_2

    :cond_5
    const/16 v16, 0x0

    iget-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iget-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iget-object v7, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    check-cast v7, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    move-object/from16 v26, v4

    move-object v4, v2

    move-object v2, v7

    move-object/from16 v7, v26

    goto :goto_1

    :cond_6
    const/16 v16, 0x0

    invoke-static {v2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 2
    invoke-interface/range {p1 .. p1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_c

    move-object/from16 v2, p3

    .line 3
    iput-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    iput-object v0, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    iput-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iput v3, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    invoke-virtual {v0, v1, v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K4(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object v4

    if-ne v4, v6, :cond_7

    goto/16 :goto_6

    :cond_7
    move-object v7, v0

    .line 4
    :goto_1
    check-cast v4, Ljava/util/List;

    .line 5
    iput-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    iput-object v8, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    iput-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iput v12, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    invoke-virtual {v7, v4, v1, v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->T4(Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object v4

    if-ne v4, v6, :cond_8

    goto/16 :goto_6

    :cond_8
    move-object/from16 v26, v4

    move-object v4, v2

    move-object/from16 v2, v26

    .line 6
    :goto_2
    check-cast v2, Ljava/util/List;

    .line 7
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_9

    .line 8
    new-array v1, v3, [Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    aput-object v4, v1, v16

    invoke-virtual {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->h5([Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 9
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1

    .line 10
    :cond_9
    invoke-virtual {v0, v1, v4}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->O4(ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)Lorg/xbet/uikit/components/lottie_empty/n;

    move-result-object v2

    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    .line 11
    iput-object v4, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    iput-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    iput-object v0, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$2:Ljava/lang/Object;

    iput-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iput v11, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    invoke-virtual {v0, v1, v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->K4(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object v7

    if-ne v7, v6, :cond_a

    goto/16 :goto_6

    :cond_a
    move-object v9, v4

    move-object v4, v0

    .line 12
    :goto_3
    check-cast v7, Ljava/util/List;

    .line 13
    iput-object v9, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    iput-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    iput-object v8, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$2:Ljava/lang/Object;

    iput v10, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    invoke-virtual {v4, v7, v1, v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->T4(Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object v1

    if-ne v1, v6, :cond_b

    goto/16 :goto_6

    :cond_b
    move-object v4, v2

    move-object v2, v1

    move-object v1, v4

    move-object v4, v9

    .line 14
    :goto_4
    check-cast v2, Ljava/lang/Iterable;

    .line 15
    invoke-static {v1, v2}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    move-result-object v1

    .line 16
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$e;

    invoke-direct {v2, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$e;-><init>(Ljava/util/List;)V

    goto/16 :goto_b

    :cond_c
    move-object/from16 v2, p3

    .line 17
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->O3()Z

    move-result v4

    if-nez v4, :cond_d

    .line 18
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->d4()V

    .line 19
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1

    .line 20
    :cond_d
    new-instance v4, Ljava/util/ArrayList;

    const/16 v7, 0xa

    move-object/from16 v8, p1

    invoke-static {v8, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    move-result v7

    invoke-direct {v4, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 21
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v7

    move-object v10, v4

    move-object v14, v7

    :goto_5
    invoke-interface {v14}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_11

    invoke-interface {v14}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    .line 22
    move-object v11, v4

    check-cast v11, Lorg/xplatform/aggregator/api/model/Game;

    .line 23
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X5:Ljava/util/Map;

    invoke-virtual {v11}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    move-result-wide v7

    invoke-static {v7, v8}, LHc/a;->f(J)Ljava/lang/Long;

    move-result-object v7

    invoke-interface {v4, v7, v11}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->M5:LHX0/e;

    .line 25
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R5:Lek0/a;

    invoke-virtual {v4}, Lek0/a;->m()Z

    move-result v7

    if-eqz v1, :cond_10

    .line 26
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->C5:Le81/a;

    iput-object v2, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$0:Ljava/lang/Object;

    iput-object v10, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$1:Ljava/lang/Object;

    iput-object v14, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$2:Ljava/lang/Object;

    iput-object v13, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$3:Ljava/lang/Object;

    iput-object v11, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$4:Ljava/lang/Object;

    iput-object v10, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->L$5:Ljava/lang/Object;

    iput-boolean v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$0:Z

    iput-boolean v7, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->Z$1:Z

    iput v1, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->I$0:I

    iput v9, v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$updateGames$1;->label:I

    invoke-interface {v4, v11, v5}, Le81/a;->a(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object v4

    if-ne v4, v6, :cond_e

    :goto_6
    return-object v6

    :cond_e
    move-object v8, v4

    move-object v4, v2

    move-object v2, v8

    move v8, v1

    move-object v15, v10

    :goto_7
    if-eqz v1, :cond_f

    const/4 v1, 0x1

    goto :goto_8

    :cond_f
    const/4 v1, 0x0

    :goto_8
    check-cast v2, Ljava/lang/Boolean;

    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    move/from16 v19, v1

    move/from16 v21, v2

    move-object v2, v4

    move v1, v8

    :goto_9
    move/from16 v20, v7

    move-object/from16 v17, v11

    move-object/from16 v18, v13

    goto :goto_a

    :cond_10
    move/from16 v19, v1

    move-object v15, v10

    const/16 v21, 0x0

    goto :goto_9

    .line 27
    :goto_a
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Q5:Lek0/o;

    invoke-virtual {v4}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    move-result-object v22

    const/16 v24, 0x20

    const/16 v25, 0x0

    const/16 v23, 0x0

    .line 28
    invoke-static/range {v17 .. v25}, LQ91/c;->b(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;ILjava/lang/Object;)LN21/k;

    move-result-object v4

    .line 29
    invoke-interface {v10, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    move-object v10, v15

    goto :goto_5

    .line 30
    :cond_11
    check-cast v10, Ljava/util/List;

    .line 31
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;

    invoke-direct {v1, v10}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;-><init>(Ljava/util/List;)V

    move-object v4, v2

    move-object v2, v1

    .line 32
    :goto_b
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$d;->a:[I

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    aget v1, v1, v4

    if-eq v1, v3, :cond_13

    if-ne v1, v12, :cond_12

    .line 33
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    goto :goto_c

    .line 34
    :cond_12
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v1

    .line 35
    :cond_13
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 36
    :goto_c
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1
.end method
