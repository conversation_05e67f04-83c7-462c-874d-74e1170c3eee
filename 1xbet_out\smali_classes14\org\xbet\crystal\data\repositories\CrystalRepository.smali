.class public final Lorg/xbet/crystal/data/repositories/CrystalRepository;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u000c\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ*\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0012H\u0086@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\r\u0010\u0017\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\r\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010$\u00a8\u0006%"
    }
    d2 = {
        "Lorg/xbet/crystal/data/repositories/CrystalRepository;",
        "",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;",
        "crystalRemoteDataSource",
        "Lorg/xbet/crystal/data/datasources/a;",
        "crystalLocalDataSource",
        "LUx/c;",
        "crystalModelMapper",
        "LUx/a;",
        "crystalCoefMapModelMapper",
        "<init>",
        "(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;Lorg/xbet/crystal/data/datasources/a;LUx/c;LUx/a;)V",
        "",
        "betSum",
        "",
        "activeId",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "bonus",
        "LZx/b;",
        "g",
        "(DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "f",
        "()LZx/b;",
        "",
        "e",
        "()V",
        "a",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "b",
        "Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;",
        "c",
        "Lorg/xbet/crystal/data/datasources/a;",
        "d",
        "LUx/c;",
        "LUx/a;",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/crystal/data/datasources/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LUx/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LUx/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;Lorg/xbet/crystal/data/datasources/a;LUx/c;LUx/a;)V
    .locals 0
    .param p1    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/crystal/data/datasources/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LUx/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LUx/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->a:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->b:Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->c:Lorg/xbet/crystal/data/datasources/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->d:LUx/c;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->e:LUx/a;

    .line 13
    .line 14
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/crystal/data/repositories/CrystalRepository;)LUx/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->e:LUx/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/crystal/data/repositories/CrystalRepository;)Lorg/xbet/crystal/data/datasources/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->c:Lorg/xbet/crystal/data/datasources/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/crystal/data/repositories/CrystalRepository;)LUx/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->d:LUx/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/crystal/data/repositories/CrystalRepository;)Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->b:Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final e()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->c:Lorg/xbet/crystal/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/crystal/data/datasources/a;->a()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final f()LZx/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->c:Lorg/xbet/crystal/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/crystal/data/datasources/a;->b()LZx/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final g(DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(DJ",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "LZx/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository;->a:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    move-object v2, p0

    .line 7
    move-wide v3, p1

    .line 8
    move-wide v5, p3

    .line 9
    move-object v7, p5

    .line 10
    invoke-direct/range {v1 .. v8}, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;-><init>(Lorg/xbet/crystal/data/repositories/CrystalRepository;DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1, p6}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method
