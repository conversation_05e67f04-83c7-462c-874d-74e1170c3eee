.class public final synthetic Lorg/xplatform/aggregator/impl/favorite/presentation/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/k;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/k;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    check-cast p1, <PERSON>ja<PERSON>/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    check-cast p3, Ljava/lang/Integer;

    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    move-result p3

    invoke-static {v0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->J2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;III)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
