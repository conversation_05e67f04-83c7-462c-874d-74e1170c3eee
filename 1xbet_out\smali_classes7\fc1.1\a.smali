.class public final Lfc1/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lfc1/a$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LVX0/i;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\rB\'\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\u000e"
    }
    d2 = {
        "Lfc1/a;",
        "LA4/e;",
        "LVX0/i;",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LVb1/a;",
        "aggregatorPopularItemsClickListener",
        "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;",
        "aggregatorPopularCommonClickListener",
        "",
        "screenName",
        "<init>",
        "(LUX0/k;LVb1/a;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)V",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(LUX0/k;LVb1/a;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)V
    .locals 2
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LVb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lfc1/a$a;->a:Lfc1/a$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    invoke-static {p1, p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularSimpleBannersContainerDelegateKt;->g(LUX0/k;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)LA4/c;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-static {p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularAggregatorBannerDelegateKt;->e(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)LA4/c;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularGamesCategoryContainerDelegateKt;->i(LUX0/k;LVb1/a;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)LA4/c;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-static {p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PromoProductBannerDelegateKt;->e(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)LA4/c;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-static {p2, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularSelectionContainerDelegateKt;->d(LVb1/a;Ljava/lang/String;)LA4/c;

    .line 41
    .line 42
    .line 43
    move-result-object p2

    .line 44
    invoke-virtual {v0, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    invoke-static {p1, p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularCategoriesContainerDelegateKt;->h(LUX0/k;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)LA4/c;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-virtual {p2, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-static {}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularShimmersDelegateKt;->w()LA4/c;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-static {}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularShimmersDelegateKt;->m()LA4/c;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-static {}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularShimmersDelegateKt;->r()LA4/c;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 77
    .line 78
    .line 79
    return-void
.end method
