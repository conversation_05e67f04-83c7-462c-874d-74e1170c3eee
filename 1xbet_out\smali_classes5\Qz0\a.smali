.class public abstract LQz0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LQz0/a$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u00080\u0018\u0000 \u00082\u00020\u0001:\u0001\u0008B\u0011\u0008\u0004\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0008\u0010\t\u0082\u0001\u0007\n\u000b\u000c\r\u000e\u000f\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LQz0/a;",
        "",
        "Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;",
        "cardIdentity",
        "<init>",
        "(Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;)V",
        "a",
        "Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;",
        "b",
        "()Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;",
        "LQz0/i;",
        "LQz0/k;",
        "LQz0/l;",
        "LQz0/n;",
        "LQz0/p;",
        "LQz0/r;",
        "LQz0/t;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:LQz0/a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final c:Landroidx/recyclerview/widget/i$f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/recyclerview/widget/i$f<",
            "LQz0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LQz0/a$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LQz0/a$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LQz0/a;->b:LQz0/a$b;

    .line 8
    .line 9
    new-instance v0, LQz0/a$a;

    .line 10
    .line 11
    invoke-direct {v0}, LQz0/a$a;-><init>()V

    .line 12
    .line 13
    .line 14
    sput-object v0, LQz0/a;->c:Landroidx/recyclerview/widget/i$f;

    .line 15
    .line 16
    return-void
.end method

.method public constructor <init>(Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, LQz0/a;->a:Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LQz0/a;-><init>(Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;)V

    return-void
.end method

.method public static final synthetic a()Landroidx/recyclerview/widget/i$f;
    .locals 1

    .line 1
    sget-object v0, LQz0/a;->c:Landroidx/recyclerview/widget/i$f;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LQz0/a;->a:Lorg/xbet/sportgame/classic/impl/presentation/models/CardIdentity;

    .line 2
    .line 3
    return-object v0
.end method
