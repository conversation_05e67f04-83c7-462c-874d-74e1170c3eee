.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$getJackpot$1"
    f = "PopularOneXGamesViewModel.kt"
    l = {
        0x1df,
        0x1ea
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    const-wide/32 v4, 0x2bf20

    .line 10
    .line 11
    .line 12
    if-eqz v1, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto :goto_3

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 23
    .line 24
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 25
    .line 26
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    goto :goto_1

    .line 34
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->W3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Ljava/lang/Long;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-eqz p1, :cond_3

    .line 44
    .line 45
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 46
    .line 47
    .line 48
    move-result-wide v6

    .line 49
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 50
    .line 51
    .line 52
    move-result-wide v8

    .line 53
    sub-long/2addr v8, v6

    .line 54
    sub-long v6, v4, v8

    .line 55
    .line 56
    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->min(JJ)J

    .line 57
    .line 58
    .line 59
    move-result-wide v6

    .line 60
    goto :goto_0

    .line 61
    :cond_3
    const-wide/16 v6, 0x0

    .line 62
    .line 63
    :goto_0
    iput v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->label:I

    .line 64
    .line 65
    invoke-static {v6, v7, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v0, :cond_4

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_4
    :goto_1
    sget-object p1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 73
    .line 74
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1$1;

    .line 75
    .line 76
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 77
    .line 78
    const/4 v6, 0x0

    .line 79
    invoke-direct {v1, v3, v6}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 80
    .line 81
    .line 82
    invoke-static {v4, v5, p1, v1}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->b(JLjava/util/concurrent/TimeUnit;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1$a;

    .line 87
    .line 88
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 89
    .line 90
    invoke-direct {v1, v3}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1$a;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 91
    .line 92
    .line 93
    iput v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;->label:I

    .line 94
    .line 95
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/e;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    if-ne p1, v0, :cond_5

    .line 100
    .line 101
    :goto_2
    return-object v0

    .line 102
    :cond_5
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object p1
.end method
