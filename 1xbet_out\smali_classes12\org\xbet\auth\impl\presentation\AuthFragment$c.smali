.class public final Lorg/xbet/auth/impl/presentation/AuthFragment$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/core/view/K;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/auth/impl/presentation/AuthFragment;->s2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/auth/impl/presentation/AuthFragment;

.field public final synthetic c:Z


# direct methods
.method public constructor <init>(ZLorg/xbet/auth/impl/presentation/AuthFragment;Z)V
    .locals 0

    iput-boolean p1, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->a:Z

    iput-object p2, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->b:Lorg/xbet/auth/impl/presentation/AuthFragment;

    iput-boolean p3, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->c:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onApplyWindowInsets(Landroid/view/View;Landroidx/core/view/F0;)Landroidx/core/view/F0;
    .locals 7

    .line 1
    iget-object p1, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->b:Lorg/xbet/auth/impl/presentation/AuthFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xbet/auth/impl/presentation/AuthFragment;->Q2(Lorg/xbet/auth/impl/presentation/AuthFragment;)Lti/a;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object v0, p1, Lti/a;->i:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 8
    .line 9
    invoke-static {}, Landroidx/core/view/F0$o;->h()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget v2, p1, LI0/d;->b:I

    .line 18
    .line 19
    const/16 v5, 0xd

    .line 20
    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v1, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v4, 0x0

    .line 25
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-boolean p1, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->c:Z

    .line 29
    .line 30
    if-eqz p1, :cond_1

    .line 31
    .line 32
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->s(I)Z

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    if-eqz p1, :cond_0

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    invoke-static {}, Landroidx/core/view/F0$o;->g()I

    .line 44
    .line 45
    .line 46
    move-result p1

    .line 47
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iget p1, p1, LI0/d;->d:I

    .line 52
    .line 53
    goto :goto_1

    .line 54
    :cond_1
    :goto_0
    const/4 p1, 0x0

    .line 55
    :goto_1
    iget-object v0, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->b:Lorg/xbet/auth/impl/presentation/AuthFragment;

    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/auth/impl/presentation/AuthFragment;->Q2(Lorg/xbet/auth/impl/presentation/AuthFragment;)Lti/a;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {v0}, Lti/a;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-virtual {v0}, Landroid/view/View;->getPaddingLeft()I

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 70
    .line 71
    .line 72
    move-result v2

    .line 73
    invoke-virtual {v0}, Landroid/view/View;->getPaddingRight()I

    .line 74
    .line 75
    .line 76
    move-result v3

    .line 77
    invoke-virtual {v0, v1, v2, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 78
    .line 79
    .line 80
    sget p1, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 81
    .line 82
    const/16 v0, 0x1d

    .line 83
    .line 84
    if-le p1, v0, :cond_2

    .line 85
    .line 86
    iget-object p1, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->b:Lorg/xbet/auth/impl/presentation/AuthFragment;

    .line 87
    .line 88
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 89
    .line 90
    .line 91
    move-result v0

    .line 92
    invoke-virtual {p2, v0}, Landroidx/core/view/F0;->s(I)Z

    .line 93
    .line 94
    .line 95
    move-result v0

    .line 96
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 97
    .line 98
    .line 99
    move-result v1

    .line 100
    invoke-virtual {p2, v1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    iget v1, v1, LI0/d;->d:I

    .line 105
    .line 106
    invoke-static {p1, v0, v1}, Lorg/xbet/auth/impl/presentation/AuthFragment;->S2(Lorg/xbet/auth/impl/presentation/AuthFragment;ZI)V

    .line 107
    .line 108
    .line 109
    :cond_2
    iget-boolean p1, p0, Lorg/xbet/auth/impl/presentation/AuthFragment$c;->a:Z

    .line 110
    .line 111
    if-eqz p1, :cond_3

    .line 112
    .line 113
    sget-object p1, Landroidx/core/view/F0;->b:Landroidx/core/view/F0;

    .line 114
    .line 115
    return-object p1

    .line 116
    :cond_3
    return-object p2
.end method
