.class public final LtY0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtY0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static a(LtY0/a;I)F
    .locals 1
    .param p0    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LtY0/a;->a()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    add-int/lit8 p1, p1, -0x1

    .line 6
    .line 7
    int-to-float p1, p1

    .line 8
    mul-float v0, v0, p1

    .line 9
    .line 10
    invoke-interface {p0}, LtY0/a;->j()F

    .line 11
    .line 12
    .line 13
    move-result p0

    .line 14
    add-float/2addr v0, p0

    .line 15
    return v0
.end method

.method public static b(LtY0/a;)F
    .locals 1
    .param p0    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LtY0/a;->g()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-interface {p0}, LtY0/a;->b()F

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    add-float/2addr v0, p0

    .line 10
    return v0
.end method

.method public static c(LtY0/a;)F
    .locals 1
    .param p0    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LtY0/a;->f()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-interface {p0}, LtY0/a;->h()F

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    add-float/2addr v0, p0

    .line 10
    return v0
.end method

.method public static d(LtY0/a;)F
    .locals 1
    .param p0    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LtY0/a;->e()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-interface {p0}, LtY0/a;->i()F

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    add-float/2addr v0, p0

    .line 10
    return v0
.end method

.method public static e(LtY0/a;F)LtY0/a;
    .locals 3
    .param p0    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-interface {p0}, LtY0/a;->a()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    mul-float v0, v0, p1

    .line 6
    .line 7
    invoke-interface {p0}, LtY0/a;->e()F

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    mul-float v1, v1, p1

    .line 12
    .line 13
    invoke-interface {p0}, LtY0/a;->g()F

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    mul-float v2, v2, p1

    .line 18
    .line 19
    invoke-interface {p0}, LtY0/a;->i()F

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    invoke-interface {p0}, LtY0/a;->b()F

    .line 24
    .line 25
    .line 26
    move-result p0

    .line 27
    invoke-static {v0, v1, v2, p1, p0}, LtY0/b;->a(FFFFF)LtY0/a;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    return-object p0
.end method
