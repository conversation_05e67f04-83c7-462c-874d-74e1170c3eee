.class public final synthetic Lhz0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

.field public final synthetic b:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinButton;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;Lorg/xbet/spin_and_win/presentation/views/SpinAndWinButton;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lhz0/b;->a:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    iput-object p2, p0, Lhz0/b;->b:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinButton;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lhz0/b;->a:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    iget-object v1, p0, Lhz0/b;->b:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinButton;

    invoke-static {v0, v1, p1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->c(Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;Lorg/xbet/spin_and_win/presentation/views/SpinAndWinButton;Landroid/view/View;)V

    return-void
.end method
