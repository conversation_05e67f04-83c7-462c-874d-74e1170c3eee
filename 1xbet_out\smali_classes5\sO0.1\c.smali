.class public interface abstract LsO0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LsO0/c$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u0005J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LsO0/c;",
        "",
        "Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;",
        "fragment",
        "",
        "a",
        "(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;)V
    .param p1    # Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
