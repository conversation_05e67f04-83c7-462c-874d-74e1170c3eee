.class public final LRw0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRw0/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LRw0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LRw0/a$b$C;,
        LRw0/a$b$q;,
        LRw0/a$b$i;,
        LRw0/a$b$a;,
        LRw0/a$b$E;,
        LRw0/a$b$M;,
        LRw0/a$b$h;,
        LRw0/a$b$D;,
        LRw0/a$b$I;,
        LRw0/a$b$t;,
        LRw0/a$b$s;,
        LRw0/a$b$r;,
        LRw0/a$b$J;,
        LRw0/a$b$H;,
        LRw0/a$b$L;,
        LRw0/a$b$B;,
        LRw0/a$b$u;,
        LRw0/a$b$F;,
        LRw0/a$b$v;,
        LRw0/a$b$k;,
        LRw0/a$b$b;,
        LRw0/a$b$m;,
        LRw0/a$b$x;,
        LRw0/a$b$y;,
        LRw0/a$b$n;,
        LRw0/a$b$e;,
        LRw0/a$b$d;,
        LRw0/a$b$K;,
        LRw0/a$b$p;,
        LRw0/a$b$A;,
        LRw0/a$b$g;,
        LRw0/a$b$j;,
        LRw0/a$b$f;,
        LRw0/a$b$o;,
        LRw0/a$b$z;,
        LRw0/a$b$G;,
        LRw0/a$b$c;,
        LRw0/a$b$l;,
        LRw0/a$b$w;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIP/a;",
            ">;"
        }
    .end annotation
.end field

.field public A0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LuX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public B0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/usecase/GetDotaTopTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJZ/a;",
            ">;"
        }
    .end annotation
.end field

.field public C0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/usecase/GetLolTopTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lbl0/g;",
            ">;"
        }
    .end annotation
.end field

.field public D0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSTopTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNo0/b;",
            ">;"
        }
    .end annotation
.end field

.field public E0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Llp0/g;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lts0/a;",
            ">;"
        }
    .end annotation
.end field

.field public F0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFI/c;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lss0/a;",
            ">;"
        }
    .end annotation
.end field

.field public G0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSTopPlayersStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lrs0/a;",
            ">;"
        }
    .end annotation
.end field

.field public H0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/usecase/GetDotaTopPlayersStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lqs0/a;",
            ">;"
        }
    .end annotation
.end field

.field public I0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/usecase/GetLolTopPlayersStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lps0/a;",
            ">;"
        }
    .end annotation
.end field

.field public J0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Llp0/e;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Los0/a;",
            ">;"
        }
    .end annotation
.end field

.field public K0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public L0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSGroupsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/top_players/data/c;",
            ">;"
        }
    .end annotation
.end field

.field public M0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/usecase/GetDotaGroupTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/top_players/data/StatisticTopPlayersRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public N0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/usecase/GetLolGroupsTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCw0/d;",
            ">;"
        }
    .end annotation
.end field

.field public O0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Llp0/c;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCw0/a;",
            ">;"
        }
    .end annotation
.end field

.field public P0:Lorg/xbet/special_event/impl/tournament/presentation/x;

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public Q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LRw0/f;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBu0/a;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/statistic/data/StatisticStadiumRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIu0/d;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lry0/a;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHg/d;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfS/a;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LJo/h;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final b:Lal0/c;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPx0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:Lyy0/d;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/medal_statistic/data/d;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LzX0/k;

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/medal_statistic/data/b;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LRw0/a$b;

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/medal_statistic/data/StatisticTopMedalsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKs0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVo/e;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;",
            ">;"
        }
    .end annotation
.end field

.field public n0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFy0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Low0/b;",
            ">;"
        }
    .end annotation
.end field

.field public p0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/usecase/GetLolAboutTournamentStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/usecase/GetDotaAboutTournamentStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/teams/data/TeamsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public r0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSAboutTournamentStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Luw0/a;",
            ">;"
        }
    .end annotation
.end field

.field public s0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Llp0/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public t0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lkc1/p;",
            ">;"
        }
    .end annotation
.end field

.field public u0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ltn/a;",
            ">;"
        }
    .end annotation
.end field

.field public v0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/usecase/GetLolPrizeDistributionStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lvw0/a;",
            ">;"
        }
    .end annotation
.end field

.field public w0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/usecase/GetDotaPrizeDistributionStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public x0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSPrizeDistributionStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LRl0/b;",
            ">;"
        }
    .end annotation
.end field

.field public y0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Llp0/i;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVg0/a;",
            ">;"
        }
    .end annotation
.end field

.field public z0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V
    .locals 2

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LRw0/a$b;->e:LRw0/a$b;

    .line 4
    iput-object p2, p0, LRw0/a$b;->a:LJo/h;

    .line 5
    iput-object p7, p0, LRw0/a$b;->b:Lal0/c;

    move-object/from16 v0, p16

    .line 6
    iput-object v0, p0, LRw0/a$b;->c:Lyy0/d;

    move-object/from16 v1, p50

    .line 7
    iput-object v1, p0, LRw0/a$b;->d:LzX0/k;

    .line 8
    invoke-virtual/range {p0 .. p51}, LRw0/a$b;->b(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V

    .line 9
    invoke-virtual/range {p0 .. p51}, LRw0/a$b;->c(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V

    .line 10
    invoke-virtual/range {p0 .. p51}, LRw0/a$b;->d(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V

    .line 11
    invoke-virtual/range {p0 .. p51}, LRw0/a$b;->e(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;LRw0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p51}, LRw0/a$b;-><init>(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LRw0/a$b;->f(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V
    .locals 0

    .line 1
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p8

    .line 5
    iput-object p8, p0, LRw0/a$b;->f:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p8

    .line 11
    iput-object p8, p0, LRw0/a$b;->g:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p8

    .line 17
    iput-object p8, p0, LRw0/a$b;->h:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p8, LRw0/a$b$C;

    .line 20
    .line 21
    invoke-direct {p8, p4}, LRw0/a$b$C;-><init>(Ldk0/p;)V

    .line 22
    .line 23
    .line 24
    iput-object p8, p0, LRw0/a$b;->i:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p8

    .line 30
    iput-object p8, p0, LRw0/a$b;->j:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p30 .. p30}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p8

    .line 36
    iput-object p8, p0, LRw0/a$b;->k:Ldagger/internal/h;

    .line 37
    .line 38
    new-instance p8, LRw0/a$b$q;

    .line 39
    .line 40
    invoke-direct {p8, p2}, LRw0/a$b$q;-><init>(LJo/h;)V

    .line 41
    .line 42
    .line 43
    iput-object p8, p0, LRw0/a$b;->l:Ldagger/internal/h;

    .line 44
    .line 45
    new-instance p2, LRw0/a$b$i;

    .line 46
    .line 47
    invoke-direct {p2, p1}, LRw0/a$b$i;-><init>(LQW0/c;)V

    .line 48
    .line 49
    .line 50
    iput-object p2, p0, LRw0/a$b;->m:Ldagger/internal/h;

    .line 51
    .line 52
    invoke-static/range {p37 .. p37}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput-object p1, p0, LRw0/a$b;->n:Ldagger/internal/h;

    .line 57
    .line 58
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iput-object p1, p0, LRw0/a$b;->o:Ldagger/internal/h;

    .line 63
    .line 64
    invoke-static {p1}, Low0/c;->a(LBc/a;)Low0/c;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    iput-object p1, p0, LRw0/a$b;->p:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iput-object p1, p0, LRw0/a$b;->q:Ldagger/internal/h;

    .line 75
    .line 76
    iget-object p2, p0, LRw0/a$b;->n:Ldagger/internal/h;

    .line 77
    .line 78
    iget-object p8, p0, LRw0/a$b;->p:Ldagger/internal/h;

    .line 79
    .line 80
    invoke-static {p2, p8, p1}, Lorg/xbet/special_event/impl/teams/data/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/teams/data/a;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    iput-object p1, p0, LRw0/a$b;->r:Ldagger/internal/h;

    .line 85
    .line 86
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    iput-object p1, p0, LRw0/a$b;->s:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static {p1}, Lorg/xbet/special_event/impl/teams/domain/usecase/c;->a(LBc/a;)Lorg/xbet/special_event/impl/teams/domain/usecase/c;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iput-object p1, p0, LRw0/a$b;->t:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static/range {p51 .. p51}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    iput-object p1, p0, LRw0/a$b;->u:Ldagger/internal/h;

    .line 103
    .line 104
    new-instance p1, LRw0/a$b$a;

    .line 105
    .line 106
    invoke-direct {p1, p5}, LRw0/a$b$a;-><init>(LMm/a;)V

    .line 107
    .line 108
    .line 109
    iput-object p1, p0, LRw0/a$b;->v:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static {}, Lvw0/c;->a()Lvw0/c;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iput-object p1, p0, LRw0/a$b;->w:Ldagger/internal/h;

    .line 120
    .line 121
    invoke-static {}, LWo0/c;->a()LWo0/c;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LRw0/a$b;->x:Ldagger/internal/h;

    .line 130
    .line 131
    new-instance p1, LRw0/a$b$E;

    .line 132
    .line 133
    invoke-direct {p1, p6}, LRw0/a$b$E;-><init>(LMl0/a;)V

    .line 134
    .line 135
    .line 136
    iput-object p1, p0, LRw0/a$b;->y:Ldagger/internal/h;

    .line 137
    .line 138
    invoke-static/range {p39 .. p39}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    iput-object p1, p0, LRw0/a$b;->z:Ldagger/internal/h;

    .line 143
    .line 144
    invoke-static/range {p41 .. p41}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    iput-object p1, p0, LRw0/a$b;->A:Ldagger/internal/h;

    .line 149
    .line 150
    new-instance p1, LRw0/a$b$M;

    .line 151
    .line 152
    invoke-direct {p1, p4}, LRw0/a$b$M;-><init>(Ldk0/p;)V

    .line 153
    .line 154
    .line 155
    iput-object p1, p0, LRw0/a$b;->B:Ldagger/internal/h;

    .line 156
    .line 157
    new-instance p1, LRw0/a$b$h;

    .line 158
    .line 159
    invoke-direct {p1, p3}, LRw0/a$b$h;-><init>(LDZ/m;)V

    .line 160
    .line 161
    .line 162
    iput-object p1, p0, LRw0/a$b;->C:Ldagger/internal/h;

    .line 163
    .line 164
    new-instance p1, LRw0/a$b$D;

    .line 165
    .line 166
    invoke-direct {p1, p7}, LRw0/a$b$D;-><init>(Lal0/c;)V

    .line 167
    .line 168
    .line 169
    iput-object p1, p0, LRw0/a$b;->D:Ldagger/internal/h;

    .line 170
    .line 171
    return-void
.end method

.method public final c(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V
    .locals 0

    .line 1
    new-instance p2, LRw0/a$b$I;

    .line 2
    .line 3
    invoke-direct {p2, p14}, LRw0/a$b$I;-><init>(LJo0/a;)V

    .line 4
    .line 5
    .line 6
    iput-object p2, p0, LRw0/a$b;->E:Ldagger/internal/h;

    .line 7
    .line 8
    new-instance p2, LRw0/a$b$t;

    .line 9
    .line 10
    invoke-direct {p2, p13}, LRw0/a$b$t;-><init>(Lks0/f;)V

    .line 11
    .line 12
    .line 13
    iput-object p2, p0, LRw0/a$b;->F:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {p2}, Lss0/b;->a(LBc/a;)Lss0/b;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    iput-object p2, p0, LRw0/a$b;->G:Ldagger/internal/h;

    .line 20
    .line 21
    new-instance p2, LRw0/a$b$s;

    .line 22
    .line 23
    invoke-direct {p2, p13}, LRw0/a$b$s;-><init>(Lks0/f;)V

    .line 24
    .line 25
    .line 26
    iput-object p2, p0, LRw0/a$b;->H:Ldagger/internal/h;

    .line 27
    .line 28
    invoke-static {p2}, Lqs0/b;->a(LBc/a;)Lqs0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p2

    .line 32
    iput-object p2, p0, LRw0/a$b;->I:Ldagger/internal/h;

    .line 33
    .line 34
    new-instance p2, LRw0/a$b$r;

    .line 35
    .line 36
    invoke-direct {p2, p13}, LRw0/a$b$r;-><init>(Lks0/f;)V

    .line 37
    .line 38
    .line 39
    iput-object p2, p0, LRw0/a$b;->J:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static {p2}, Los0/b;->a(LBc/a;)Los0/b;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, LRw0/a$b;->K:Ldagger/internal/h;

    .line 46
    .line 47
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iput-object p1, p0, LRw0/a$b;->L:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object p1, p0, LRw0/a$b;->o:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xbet/special_event/impl/top_players/data/d;->a(LBc/a;)Lorg/xbet/special_event/impl/top_players/data/d;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput-object p1, p0, LRw0/a$b;->M:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object p2, p0, LRw0/a$b;->m:Ldagger/internal/h;

    .line 62
    .line 63
    iget-object p3, p0, LRw0/a$b;->q:Ldagger/internal/h;

    .line 64
    .line 65
    invoke-static {p2, p1, p3}, Lorg/xbet/special_event/impl/top_players/data/e;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/top_players/data/e;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    iput-object p1, p0, LRw0/a$b;->N:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    iput-object p1, p0, LRw0/a$b;->O:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static {p1}, LHu0/c;->a(LBc/a;)LHu0/c;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LRw0/a$b;->P:Ldagger/internal/h;

    .line 86
    .line 87
    new-instance p1, LRw0/a$b$J;

    .line 88
    .line 89
    invoke-direct {p1, p11}, LRw0/a$b$J;-><init>(LLD0/a;)V

    .line 90
    .line 91
    .line 92
    iput-object p1, p0, LRw0/a$b;->Q:Ldagger/internal/h;

    .line 93
    .line 94
    iget-object p1, p0, LRw0/a$b;->o:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static {p1}, LCu0/c;->a(LBc/a;)LCu0/c;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LRw0/a$b;->R:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static/range {p48 .. p48}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    iput-object p1, p0, LRw0/a$b;->S:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object p2, p0, LRw0/a$b;->m:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object p3, p0, LRw0/a$b;->R:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p4, p0, LRw0/a$b;->q:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p2, p3, p4, p1}, Lorg/xbet/special_event/impl/statistic/data/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/statistic/data/a;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iput-object p1, p0, LRw0/a$b;->T:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    iput-object p1, p0, LRw0/a$b;->U:Ldagger/internal/h;

    .line 125
    .line 126
    invoke-static {p1}, LIu0/c;->a(LBc/a;)LIu0/c;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    iput-object p1, p0, LRw0/a$b;->V:Ldagger/internal/h;

    .line 131
    .line 132
    invoke-static {}, Lry0/c;->a()Lry0/c;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    iput-object p1, p0, LRw0/a$b;->W:Ldagger/internal/h;

    .line 141
    .line 142
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    iput-object p1, p0, LRw0/a$b;->X:Ldagger/internal/h;

    .line 147
    .line 148
    invoke-static/range {p46 .. p46}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    iput-object p1, p0, LRw0/a$b;->Y:Ldagger/internal/h;

    .line 153
    .line 154
    new-instance p1, LRw0/a$b$H;

    .line 155
    .line 156
    invoke-direct {p1, p10}, LRw0/a$b$H;-><init>(LiR/a;)V

    .line 157
    .line 158
    .line 159
    iput-object p1, p0, LRw0/a$b;->Z:Ldagger/internal/h;

    .line 160
    .line 161
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    iput-object p1, p0, LRw0/a$b;->a0:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object p2, p0, LRw0/a$b;->Y:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object p3, p0, LRw0/a$b;->Z:Ldagger/internal/h;

    .line 170
    .line 171
    invoke-static {p2, p3, p1}, LPx0/b;->a(LBc/a;LBc/a;LBc/a;)LPx0/b;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    iput-object p1, p0, LRw0/a$b;->b0:Ldagger/internal/h;

    .line 176
    .line 177
    iget-object p1, p0, LRw0/a$b;->o:Ldagger/internal/h;

    .line 178
    .line 179
    invoke-static {p1}, Lorg/xbet/special_event/impl/medal_statistic/data/e;->a(LBc/a;)Lorg/xbet/special_event/impl/medal_statistic/data/e;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    iput-object p1, p0, LRw0/a$b;->c0:Ldagger/internal/h;

    .line 184
    .line 185
    return-void
.end method

.method public final d(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V
    .locals 1

    .line 1
    move-object/from16 p1, p16

    .line 2
    .line 3
    move-object/from16 p2, p18

    .line 4
    .line 5
    move-object/from16 p3, p19

    .line 6
    .line 7
    move-object/from16 p4, p20

    .line 8
    .line 9
    invoke-static/range {p49 .. p49}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 10
    .line 11
    .line 12
    move-result-object p5

    .line 13
    iput-object p5, p0, LRw0/a$b;->d0:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object p6, p0, LRw0/a$b;->m:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object p7, p0, LRw0/a$b;->c0:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v0, p0, LRw0/a$b;->q:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static {p6, p7, p5, v0}, Lorg/xbet/special_event/impl/medal_statistic/data/f;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/medal_statistic/data/f;

    .line 22
    .line 23
    .line 24
    move-result-object p5

    .line 25
    iput-object p5, p0, LRw0/a$b;->e0:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static {p5}, Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/e;->a(LBc/a;)Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/e;

    .line 28
    .line 29
    .line 30
    move-result-object p5

    .line 31
    iput-object p5, p0, LRw0/a$b;->f0:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {}, LKs0/c;->a()LKs0/c;

    .line 34
    .line 35
    .line 36
    move-result-object p5

    .line 37
    invoke-static {p5}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 38
    .line 39
    .line 40
    move-result-object p5

    .line 41
    iput-object p5, p0, LRw0/a$b;->g0:Ldagger/internal/h;

    .line 42
    .line 43
    new-instance p5, LRw0/a$b$L;

    .line 44
    .line 45
    invoke-direct {p5, p1}, LRw0/a$b$L;-><init>(Lyy0/d;)V

    .line 46
    .line 47
    .line 48
    iput-object p5, p0, LRw0/a$b;->h0:Ldagger/internal/h;

    .line 49
    .line 50
    new-instance p5, LRw0/a$b$B;

    .line 51
    .line 52
    invoke-direct {p5, p1}, LRw0/a$b$B;-><init>(Lyy0/d;)V

    .line 53
    .line 54
    .line 55
    iput-object p5, p0, LRw0/a$b;->i0:Ldagger/internal/h;

    .line 56
    .line 57
    new-instance p5, LRw0/a$b$u;

    .line 58
    .line 59
    invoke-direct {p5, p1}, LRw0/a$b$u;-><init>(Lyy0/d;)V

    .line 60
    .line 61
    .line 62
    iput-object p5, p0, LRw0/a$b;->j0:Ldagger/internal/h;

    .line 63
    .line 64
    invoke-static/range {p36 .. p36}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 65
    .line 66
    .line 67
    move-result-object p5

    .line 68
    iput-object p5, p0, LRw0/a$b;->k0:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static {p5}, Lp9/d;->a(LBc/a;)Lp9/d;

    .line 71
    .line 72
    .line 73
    move-result-object p5

    .line 74
    iput-object p5, p0, LRw0/a$b;->l0:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static/range {p35 .. p35}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 77
    .line 78
    .line 79
    move-result-object p5

    .line 80
    iput-object p5, p0, LRw0/a$b;->m0:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object p5, p0, LRw0/a$b;->s:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p5}, Lorg/xbet/special_event/impl/teams/domain/usecase/e;->a(LBc/a;)Lorg/xbet/special_event/impl/teams/domain/usecase/e;

    .line 85
    .line 86
    .line 87
    move-result-object p5

    .line 88
    iput-object p5, p0, LRw0/a$b;->n0:Ldagger/internal/h;

    .line 89
    .line 90
    new-instance p5, LRw0/a$b$F;

    .line 91
    .line 92
    invoke-direct {p5, p1}, LRw0/a$b$F;-><init>(Lyy0/d;)V

    .line 93
    .line 94
    .line 95
    iput-object p5, p0, LRw0/a$b;->o0:Ldagger/internal/h;

    .line 96
    .line 97
    new-instance p1, LRw0/a$b$v;

    .line 98
    .line 99
    invoke-direct {p1, p3}, LRw0/a$b$v;-><init>(Loq0/c;)V

    .line 100
    .line 101
    .line 102
    iput-object p1, p0, LRw0/a$b;->p0:Ldagger/internal/h;

    .line 103
    .line 104
    new-instance p1, LRw0/a$b$k;

    .line 105
    .line 106
    invoke-direct {p1, p2}, LRw0/a$b$k;-><init>(LYp0/c;)V

    .line 107
    .line 108
    .line 109
    iput-object p1, p0, LRw0/a$b;->q0:Ldagger/internal/h;

    .line 110
    .line 111
    new-instance p1, LRw0/a$b$b;

    .line 112
    .line 113
    invoke-direct {p1, p4}, LRw0/a$b$b;-><init>(LMp0/a;)V

    .line 114
    .line 115
    .line 116
    iput-object p1, p0, LRw0/a$b;->r0:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object p5, p0, LRw0/a$b;->p0:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object p6, p0, LRw0/a$b;->q0:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static {p5, p6, p1}, Llp0/b;->a(LBc/a;LBc/a;LBc/a;)Llp0/b;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    iput-object p1, p0, LRw0/a$b;->s0:Ldagger/internal/h;

    .line 127
    .line 128
    new-instance p1, LRw0/a$b$m;

    .line 129
    .line 130
    invoke-direct {p1, p2}, LRw0/a$b$m;-><init>(LYp0/c;)V

    .line 131
    .line 132
    .line 133
    iput-object p1, p0, LRw0/a$b;->t0:Ldagger/internal/h;

    .line 134
    .line 135
    new-instance p1, LRw0/a$b$x;

    .line 136
    .line 137
    invoke-direct {p1, p3}, LRw0/a$b$x;-><init>(Loq0/c;)V

    .line 138
    .line 139
    .line 140
    iput-object p1, p0, LRw0/a$b;->u0:Ldagger/internal/h;

    .line 141
    .line 142
    new-instance p1, LRw0/a$b$y;

    .line 143
    .line 144
    invoke-direct {p1, p3}, LRw0/a$b$y;-><init>(Loq0/c;)V

    .line 145
    .line 146
    .line 147
    iput-object p1, p0, LRw0/a$b;->v0:Ldagger/internal/h;

    .line 148
    .line 149
    new-instance p1, LRw0/a$b$n;

    .line 150
    .line 151
    invoke-direct {p1, p2}, LRw0/a$b$n;-><init>(LYp0/c;)V

    .line 152
    .line 153
    .line 154
    iput-object p1, p0, LRw0/a$b;->w0:Ldagger/internal/h;

    .line 155
    .line 156
    new-instance p1, LRw0/a$b$e;

    .line 157
    .line 158
    invoke-direct {p1, p4}, LRw0/a$b$e;-><init>(LMp0/a;)V

    .line 159
    .line 160
    .line 161
    iput-object p1, p0, LRw0/a$b;->x0:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object p3, p0, LRw0/a$b;->v0:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object p5, p0, LRw0/a$b;->w0:Ldagger/internal/h;

    .line 166
    .line 167
    invoke-static {p3, p5, p1}, Llp0/j;->a(LBc/a;LBc/a;LBc/a;)Llp0/j;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    iput-object p1, p0, LRw0/a$b;->y0:Ldagger/internal/h;

    .line 172
    .line 173
    new-instance p1, LRw0/a$b$d;

    .line 174
    .line 175
    invoke-direct {p1, p4}, LRw0/a$b$d;-><init>(LMp0/a;)V

    .line 176
    .line 177
    .line 178
    iput-object p1, p0, LRw0/a$b;->z0:Ldagger/internal/h;

    .line 179
    .line 180
    new-instance p1, LRw0/a$b$K;

    .line 181
    .line 182
    move-object/from16 p3, p21

    .line 183
    .line 184
    invoke-direct {p1, p3}, LRw0/a$b$K;-><init>(LsX0/f;)V

    .line 185
    .line 186
    .line 187
    iput-object p1, p0, LRw0/a$b;->A0:Ldagger/internal/h;

    .line 188
    .line 189
    new-instance p1, LRw0/a$b$p;

    .line 190
    .line 191
    invoke-direct {p1, p2}, LRw0/a$b$p;-><init>(LYp0/c;)V

    .line 192
    .line 193
    .line 194
    iput-object p1, p0, LRw0/a$b;->B0:Ldagger/internal/h;

    .line 195
    .line 196
    return-void
.end method

.method public final e(LQW0/c;LJo/h;LDZ/m;Ldk0/p;LMm/a;LMl0/a;Lal0/c;LlV/a;LRT/c;LiR/a;LLD0/a;Lak/a;Lks0/f;LJo0/a;LJo/k;Lyy0/d;Lzu/a;LYp0/c;Loq0/c;LMp0/a;LsX0/f;LtI/a;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;LHX0/e;LwX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lf8/g;Lc8/h;LfX/b;Lau/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lo9/a;Lorg/xbet/special_event/impl/teams/data/datasource/local/TeamsLocalDataSource;Lw30/i;LVg0/a;Lw30/q;LIP/a;LB10/a;LQn/b;LEP/b;LQn/a;LHg/d;LTn/a;LBu0/a;Lorg/xbet/special_event/impl/medal_statistic/data/b;LzX0/k;Lkc1/p;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p18

    .line 4
    .line 5
    move-object/from16 v2, p19

    .line 6
    .line 7
    move-object/from16 v3, p20

    .line 8
    .line 9
    new-instance v4, LRw0/a$b$A;

    .line 10
    .line 11
    invoke-direct {v4, v2}, LRw0/a$b$A;-><init>(Loq0/c;)V

    .line 12
    .line 13
    .line 14
    iput-object v4, v0, LRw0/a$b;->C0:Ldagger/internal/h;

    .line 15
    .line 16
    new-instance v4, LRw0/a$b$g;

    .line 17
    .line 18
    invoke-direct {v4, v3}, LRw0/a$b$g;-><init>(LMp0/a;)V

    .line 19
    .line 20
    .line 21
    iput-object v4, v0, LRw0/a$b;->D0:Ldagger/internal/h;

    .line 22
    .line 23
    iget-object v5, v0, LRw0/a$b;->B0:Ldagger/internal/h;

    .line 24
    .line 25
    iget-object v6, v0, LRw0/a$b;->C0:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static {v5, v6, v4}, Llp0/h;->a(LBc/a;LBc/a;LBc/a;)Llp0/h;

    .line 28
    .line 29
    .line 30
    move-result-object v4

    .line 31
    iput-object v4, v0, LRw0/a$b;->E0:Ldagger/internal/h;

    .line 32
    .line 33
    new-instance v4, LRw0/a$b$j;

    .line 34
    .line 35
    move-object/from16 v5, p22

    .line 36
    .line 37
    invoke-direct {v4, v5}, LRw0/a$b$j;-><init>(LtI/a;)V

    .line 38
    .line 39
    .line 40
    iput-object v4, v0, LRw0/a$b;->F0:Ldagger/internal/h;

    .line 41
    .line 42
    new-instance v4, LRw0/a$b$f;

    .line 43
    .line 44
    invoke-direct {v4, v3}, LRw0/a$b$f;-><init>(LMp0/a;)V

    .line 45
    .line 46
    .line 47
    iput-object v4, v0, LRw0/a$b;->G0:Ldagger/internal/h;

    .line 48
    .line 49
    new-instance v4, LRw0/a$b$o;

    .line 50
    .line 51
    invoke-direct {v4, v1}, LRw0/a$b$o;-><init>(LYp0/c;)V

    .line 52
    .line 53
    .line 54
    iput-object v4, v0, LRw0/a$b;->H0:Ldagger/internal/h;

    .line 55
    .line 56
    new-instance v4, LRw0/a$b$z;

    .line 57
    .line 58
    invoke-direct {v4, v2}, LRw0/a$b$z;-><init>(Loq0/c;)V

    .line 59
    .line 60
    .line 61
    iput-object v4, v0, LRw0/a$b;->I0:Ldagger/internal/h;

    .line 62
    .line 63
    iget-object v5, v0, LRw0/a$b;->G0:Ldagger/internal/h;

    .line 64
    .line 65
    iget-object v6, v0, LRw0/a$b;->H0:Ldagger/internal/h;

    .line 66
    .line 67
    invoke-static {v5, v6, v4}, Llp0/f;->a(LBc/a;LBc/a;LBc/a;)Llp0/f;

    .line 68
    .line 69
    .line 70
    move-result-object v4

    .line 71
    iput-object v4, v0, LRw0/a$b;->J0:Ldagger/internal/h;

    .line 72
    .line 73
    new-instance v4, LRw0/a$b$G;

    .line 74
    .line 75
    move-object/from16 v5, p14

    .line 76
    .line 77
    invoke-direct {v4, v5}, LRw0/a$b$G;-><init>(LJo0/a;)V

    .line 78
    .line 79
    .line 80
    iput-object v4, v0, LRw0/a$b;->K0:Ldagger/internal/h;

    .line 81
    .line 82
    new-instance v4, LRw0/a$b$c;

    .line 83
    .line 84
    invoke-direct {v4, v3}, LRw0/a$b$c;-><init>(LMp0/a;)V

    .line 85
    .line 86
    .line 87
    iput-object v4, v0, LRw0/a$b;->L0:Ldagger/internal/h;

    .line 88
    .line 89
    new-instance v3, LRw0/a$b$l;

    .line 90
    .line 91
    invoke-direct {v3, v1}, LRw0/a$b$l;-><init>(LYp0/c;)V

    .line 92
    .line 93
    .line 94
    iput-object v3, v0, LRw0/a$b;->M0:Ldagger/internal/h;

    .line 95
    .line 96
    new-instance v1, LRw0/a$b$w;

    .line 97
    .line 98
    invoke-direct {v1, v2}, LRw0/a$b$w;-><init>(Loq0/c;)V

    .line 99
    .line 100
    .line 101
    iput-object v1, v0, LRw0/a$b;->N0:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v2, v0, LRw0/a$b;->L0:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v3, v0, LRw0/a$b;->M0:Ldagger/internal/h;

    .line 106
    .line 107
    invoke-static {v2, v3, v1}, Llp0/d;->a(LBc/a;LBc/a;LBc/a;)Llp0/d;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    iput-object v1, v0, LRw0/a$b;->O0:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v2, v0, LRw0/a$b;->f:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v3, v0, LRw0/a$b;->g:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object v4, v0, LRw0/a$b;->h:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object v5, v0, LRw0/a$b;->i:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v6, v0, LRw0/a$b;->j:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v7, v0, LRw0/a$b;->k:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v8, v0, LRw0/a$b;->l:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v9, v0, LRw0/a$b;->m:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v10, v0, LRw0/a$b;->t:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v11, v0, LRw0/a$b;->u:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object v12, v0, LRw0/a$b;->v:Ldagger/internal/h;

    .line 134
    .line 135
    iget-object v13, v0, LRw0/a$b;->w:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object v14, v0, LRw0/a$b;->x:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object v15, v0, LRw0/a$b;->y:Ldagger/internal/h;

    .line 140
    .line 141
    move-object/from16 p50, v1

    .line 142
    .line 143
    iget-object v1, v0, LRw0/a$b;->z:Ldagger/internal/h;

    .line 144
    .line 145
    move-object/from16 p15, v1

    .line 146
    .line 147
    iget-object v1, v0, LRw0/a$b;->A:Ldagger/internal/h;

    .line 148
    .line 149
    move-object/from16 p16, v1

    .line 150
    .line 151
    iget-object v1, v0, LRw0/a$b;->B:Ldagger/internal/h;

    .line 152
    .line 153
    move-object/from16 p17, v1

    .line 154
    .line 155
    iget-object v1, v0, LRw0/a$b;->C:Ldagger/internal/h;

    .line 156
    .line 157
    move-object/from16 p18, v1

    .line 158
    .line 159
    iget-object v1, v0, LRw0/a$b;->D:Ldagger/internal/h;

    .line 160
    .line 161
    move-object/from16 p19, v1

    .line 162
    .line 163
    iget-object v1, v0, LRw0/a$b;->E:Ldagger/internal/h;

    .line 164
    .line 165
    move-object/from16 p20, v1

    .line 166
    .line 167
    iget-object v1, v0, LRw0/a$b;->G:Ldagger/internal/h;

    .line 168
    .line 169
    move-object/from16 p21, v1

    .line 170
    .line 171
    iget-object v1, v0, LRw0/a$b;->I:Ldagger/internal/h;

    .line 172
    .line 173
    move-object/from16 p22, v1

    .line 174
    .line 175
    iget-object v1, v0, LRw0/a$b;->K:Ldagger/internal/h;

    .line 176
    .line 177
    move-object/from16 p23, v1

    .line 178
    .line 179
    iget-object v1, v0, LRw0/a$b;->L:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 p24, v1

    .line 182
    .line 183
    iget-object v1, v0, LRw0/a$b;->P:Ldagger/internal/h;

    .line 184
    .line 185
    move-object/from16 p25, v1

    .line 186
    .line 187
    iget-object v1, v0, LRw0/a$b;->Q:Ldagger/internal/h;

    .line 188
    .line 189
    move-object/from16 p26, v1

    .line 190
    .line 191
    iget-object v1, v0, LRw0/a$b;->V:Ldagger/internal/h;

    .line 192
    .line 193
    move-object/from16 p27, v1

    .line 194
    .line 195
    iget-object v1, v0, LRw0/a$b;->W:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p28, v1

    .line 198
    .line 199
    iget-object v1, v0, LRw0/a$b;->X:Ldagger/internal/h;

    .line 200
    .line 201
    move-object/from16 p29, v1

    .line 202
    .line 203
    iget-object v1, v0, LRw0/a$b;->b0:Ldagger/internal/h;

    .line 204
    .line 205
    move-object/from16 p30, v1

    .line 206
    .line 207
    iget-object v1, v0, LRw0/a$b;->f0:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 p31, v1

    .line 210
    .line 211
    iget-object v1, v0, LRw0/a$b;->g0:Ldagger/internal/h;

    .line 212
    .line 213
    move-object/from16 p32, v1

    .line 214
    .line 215
    iget-object v1, v0, LRw0/a$b;->h0:Ldagger/internal/h;

    .line 216
    .line 217
    move-object/from16 p33, v1

    .line 218
    .line 219
    iget-object v1, v0, LRw0/a$b;->i0:Ldagger/internal/h;

    .line 220
    .line 221
    move-object/from16 p34, v1

    .line 222
    .line 223
    iget-object v1, v0, LRw0/a$b;->j0:Ldagger/internal/h;

    .line 224
    .line 225
    move-object/from16 p35, v1

    .line 226
    .line 227
    iget-object v1, v0, LRw0/a$b;->l0:Ldagger/internal/h;

    .line 228
    .line 229
    move-object/from16 p36, v1

    .line 230
    .line 231
    iget-object v1, v0, LRw0/a$b;->m0:Ldagger/internal/h;

    .line 232
    .line 233
    move-object/from16 p37, v1

    .line 234
    .line 235
    iget-object v1, v0, LRw0/a$b;->n0:Ldagger/internal/h;

    .line 236
    .line 237
    move-object/from16 p38, v1

    .line 238
    .line 239
    iget-object v1, v0, LRw0/a$b;->o0:Ldagger/internal/h;

    .line 240
    .line 241
    move-object/from16 p39, v1

    .line 242
    .line 243
    iget-object v1, v0, LRw0/a$b;->s0:Ldagger/internal/h;

    .line 244
    .line 245
    move-object/from16 p40, v1

    .line 246
    .line 247
    iget-object v1, v0, LRw0/a$b;->t0:Ldagger/internal/h;

    .line 248
    .line 249
    move-object/from16 p41, v1

    .line 250
    .line 251
    iget-object v1, v0, LRw0/a$b;->u0:Ldagger/internal/h;

    .line 252
    .line 253
    move-object/from16 p42, v1

    .line 254
    .line 255
    iget-object v1, v0, LRw0/a$b;->y0:Ldagger/internal/h;

    .line 256
    .line 257
    move-object/from16 p43, v1

    .line 258
    .line 259
    iget-object v1, v0, LRw0/a$b;->z0:Ldagger/internal/h;

    .line 260
    .line 261
    move-object/from16 p44, v1

    .line 262
    .line 263
    iget-object v1, v0, LRw0/a$b;->A0:Ldagger/internal/h;

    .line 264
    .line 265
    move-object/from16 p45, v1

    .line 266
    .line 267
    iget-object v1, v0, LRw0/a$b;->E0:Ldagger/internal/h;

    .line 268
    .line 269
    move-object/from16 p46, v1

    .line 270
    .line 271
    iget-object v1, v0, LRw0/a$b;->F0:Ldagger/internal/h;

    .line 272
    .line 273
    move-object/from16 p47, v1

    .line 274
    .line 275
    iget-object v1, v0, LRw0/a$b;->J0:Ldagger/internal/h;

    .line 276
    .line 277
    move-object/from16 p48, v1

    .line 278
    .line 279
    iget-object v1, v0, LRw0/a$b;->K0:Ldagger/internal/h;

    .line 280
    .line 281
    move-object/from16 p49, v1

    .line 282
    .line 283
    move-object/from16 p1, v2

    .line 284
    .line 285
    move-object/from16 p2, v3

    .line 286
    .line 287
    move-object/from16 p3, v4

    .line 288
    .line 289
    move-object/from16 p4, v5

    .line 290
    .line 291
    move-object/from16 p5, v6

    .line 292
    .line 293
    move-object/from16 p6, v7

    .line 294
    .line 295
    move-object/from16 p7, v8

    .line 296
    .line 297
    move-object/from16 p8, v9

    .line 298
    .line 299
    move-object/from16 p9, v10

    .line 300
    .line 301
    move-object/from16 p10, v11

    .line 302
    .line 303
    move-object/from16 p11, v12

    .line 304
    .line 305
    move-object/from16 p12, v13

    .line 306
    .line 307
    move-object/from16 p13, v14

    .line 308
    .line 309
    move-object/from16 p14, v15

    .line 310
    .line 311
    invoke-static/range {p1 .. p50}, Lorg/xbet/special_event/impl/tournament/presentation/x;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/tournament/presentation/x;

    .line 312
    .line 313
    .line 314
    move-result-object v1

    .line 315
    iput-object v1, v0, LRw0/a$b;->P0:Lorg/xbet/special_event/impl/tournament/presentation/x;

    .line 316
    .line 317
    invoke-static {v1}, LRw0/g;->c(Lorg/xbet/special_event/impl/tournament/presentation/x;)Ldagger/internal/h;

    .line 318
    .line 319
    .line 320
    move-result-object v1

    .line 321
    iput-object v1, v0, LRw0/a$b;->Q0:Ldagger/internal/h;

    .line 322
    .line 323
    return-void
.end method

.method public final f(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LRw0/a$b;->Q0:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LRw0/f;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->f(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LRw0/f;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LRw0/a$b;->a:LJo/h;

    .line 13
    .line 14
    invoke-interface {v0}, LJo/h;->b()LVo/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    check-cast v0, LVo/a;

    .line 23
    .line 24
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->a(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LVo/a;)V

    .line 25
    .line 26
    .line 27
    iget-object v0, p0, LRw0/a$b;->a:LJo/h;

    .line 28
    .line 29
    invoke-interface {v0}, LJo/h;->d()LVo/b;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, LVo/b;

    .line 38
    .line 39
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->b(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LVo/b;)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, LRw0/a$b;->b:Lal0/c;

    .line 43
    .line 44
    invoke-interface {v0}, Lal0/c;->c()Lbl0/d;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    check-cast v0, Lbl0/d;

    .line 53
    .line 54
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->d(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lbl0/d;)V

    .line 55
    .line 56
    .line 57
    iget-object v0, p0, LRw0/a$b;->b:Lal0/c;

    .line 58
    .line 59
    invoke-interface {v0}, Lal0/c;->b()Lbl0/b;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    check-cast v0, Lbl0/b;

    .line 68
    .line 69
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->c(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lbl0/b;)V

    .line 70
    .line 71
    .line 72
    iget-object v0, p0, LRw0/a$b;->c:Lyy0/d;

    .line 73
    .line 74
    invoke-interface {v0}, Lyy0/d;->c()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 83
    .line 84
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->g(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V

    .line 85
    .line 86
    .line 87
    iget-object v0, p0, LRw0/a$b;->d:LzX0/k;

    .line 88
    .line 89
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/d;->e(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LzX0/k;)V

    .line 90
    .line 91
    .line 92
    return-object p1
.end method
