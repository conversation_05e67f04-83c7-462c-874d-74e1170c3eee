.class public final LIB0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIB0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIB0/a$b$n;,
        LIB0/a$b$m;,
        LIB0/a$b$r;,
        LIB0/a$b$b;,
        LIB0/a$b$x;,
        LIB0/a$b$g;,
        LIB0/a$b$f;,
        LIB0/a$b$A;,
        LIB0/a$b$i;,
        LIB0/a$b$l;,
        LIB0/a$b$d;,
        LIB0/a$b$h;,
        LIB0/a$b$j;,
        LIB0/a$b$p;,
        LIB0/a$b$k;,
        LIB0/a$b$q;,
        LIB0/a$b$z;,
        LIB0/a$b$t;,
        LIB0/a$b$v;,
        LIB0/a$b$a;,
        LIB0/a$b$y;,
        LIB0/a$b$o;,
        LIB0/a$b$c;,
        LIB0/a$b$u;,
        LIB0/a$b$w;,
        LIB0/a$b$e;,
        LIB0/a$b$s;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LZB0/d;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtB0/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/q;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBi0/a;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/z;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/i;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/m;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/f;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lnl/h;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/c;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lzg/a;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwR/a;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBi0/d;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNP/e;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/feed/subscriptions/domain/scenarios/b;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/n;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ltw/b;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LzX0/k;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ltw/j;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LAX0/b;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lll/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LIj0/a;

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LEP/c;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Lsw/a;

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/g;",
            ">;"
        }
    .end annotation
.end field

.field public final f:Lqa0/a;

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:Ld90/a;

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LIB0/a$b;

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/j;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lra0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lk8/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/e;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhB0/i;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqP/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAu/b;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/betting/core/tax/domain/usecase/e;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/FetchMarketsUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public n0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/g;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/l;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljo/a;",
            ">;"
        }
    .end annotation
.end field

.field public p0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQn/a;",
            ">;"
        }
    .end annotation
.end field

.field public q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQn/b;",
            ">;"
        }
    .end annotation
.end field

.field public r0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le90/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/GetMarketBetGroupModelsStreamUseCaseImpl;",
            ">;"
        }
    .end annotation
.end field

.field public s0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqR/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/i;",
            ">;"
        }
    .end annotation
.end field

.field public t0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/MarketsViewModelDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LZB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public u0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Leu/i;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLA0/d;",
            ">;"
        }
    .end annotation
.end field

.field public v0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNP/a;",
            ">;"
        }
    .end annotation
.end field

.field public w0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/router/NavBarScreenTypes;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public x0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/usecases/o;",
            ">;"
        }
    .end annotation
.end field

.field public y0:Lorg/xbet/sportgame/markets/impl/presentation/markets/j;

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/domain/scenarios/ObserveMarketsScenario;",
            ">;"
        }
    .end annotation
.end field

.field public z0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIB0/g;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V
    .locals 6

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LIB0/a$b;->h:LIB0/a$b;

    move-object/from16 v0, p30

    .line 4
    iput-object v0, p0, LIB0/a$b;->a:LTZ0/a;

    move-object/from16 v1, p18

    .line 5
    iput-object v1, p0, LIB0/a$b;->b:LzX0/k;

    move-object/from16 v2, p19

    .line 6
    iput-object v2, p0, LIB0/a$b;->c:LAX0/b;

    move-object/from16 v3, p14

    .line 7
    iput-object v3, p0, LIB0/a$b;->d:LIj0/a;

    .line 8
    iput-object p3, p0, LIB0/a$b;->e:Lsw/a;

    move-object/from16 v4, p20

    .line 9
    iput-object v4, p0, LIB0/a$b;->f:Lqa0/a;

    move-object/from16 v5, p10

    .line 10
    iput-object v5, p0, LIB0/a$b;->g:Ld90/a;

    .line 11
    invoke-virtual/range {p0 .. p53}, LIB0/a$b;->b(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V

    .line 12
    invoke-virtual/range {p0 .. p53}, LIB0/a$b;->c(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V

    .line 13
    invoke-virtual/range {p0 .. p53}, LIB0/a$b;->d(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;LIB0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p53}, LIB0/a$b;-><init>(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LIB0/a$b;->e(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V
    .locals 0

    .line 1
    invoke-static {p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    iput-object p4, p0, LIB0/a$b;->i:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p4

    .line 11
    iput-object p4, p0, LIB0/a$b;->j:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p4}, Lcom/xbet/onexuser/domain/user/d;->a(LBc/a;)Lcom/xbet/onexuser/domain/user/d;

    .line 14
    .line 15
    .line 16
    move-result-object p4

    .line 17
    iput-object p4, p0, LIB0/a$b;->k:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static/range {p43 .. p43}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p4

    .line 23
    iput-object p4, p0, LIB0/a$b;->l:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static/range {p45 .. p45}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p4

    .line 29
    iput-object p4, p0, LIB0/a$b;->m:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object p5, p0, LIB0/a$b;->k:Ldagger/internal/h;

    .line 32
    .line 33
    iget-object p6, p0, LIB0/a$b;->l:Ldagger/internal/h;

    .line 34
    .line 35
    invoke-static {p5, p6, p4}, Lorg/xbet/sportgame/markets/impl/domain/usecases/b;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/b;

    .line 36
    .line 37
    .line 38
    move-result-object p4

    .line 39
    iput-object p4, p0, LIB0/a$b;->n:Ldagger/internal/h;

    .line 40
    .line 41
    new-instance p4, LIB0/a$b$n;

    .line 42
    .line 43
    invoke-direct {p4, p2}, LIB0/a$b$n;-><init>(LKA0/c;)V

    .line 44
    .line 45
    .line 46
    iput-object p4, p0, LIB0/a$b;->o:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static/range {p50 .. p50}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 49
    .line 50
    .line 51
    move-result-object p4

    .line 52
    iput-object p4, p0, LIB0/a$b;->p:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static/range {p51 .. p51}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p4

    .line 58
    iput-object p4, p0, LIB0/a$b;->q:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static/range {p52 .. p52}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p4

    .line 64
    iput-object p4, p0, LIB0/a$b;->r:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p5, p0, LIB0/a$b;->l:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object p6, p0, LIB0/a$b;->p:Ldagger/internal/h;

    .line 69
    .line 70
    iget-object p7, p0, LIB0/a$b;->q:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static {p5, p6, p7, p4}, Lorg/xbet/sportgame/markets/impl/domain/usecases/i;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/i;

    .line 73
    .line 74
    .line 75
    move-result-object p4

    .line 76
    iput-object p4, p0, LIB0/a$b;->s:Ldagger/internal/h;

    .line 77
    .line 78
    new-instance p4, LIB0/a$b$m;

    .line 79
    .line 80
    invoke-direct {p4, p2}, LIB0/a$b$m;-><init>(LKA0/c;)V

    .line 81
    .line 82
    .line 83
    iput-object p4, p0, LIB0/a$b;->t:Ldagger/internal/h;

    .line 84
    .line 85
    new-instance p4, LIB0/a$b$r;

    .line 86
    .line 87
    invoke-direct {p4, p11}, LIB0/a$b$r;-><init>(LYB0/a;)V

    .line 88
    .line 89
    .line 90
    iput-object p4, p0, LIB0/a$b;->u:Ldagger/internal/h;

    .line 91
    .line 92
    new-instance p4, LIB0/a$b$b;

    .line 93
    .line 94
    invoke-direct {p4, p2}, LIB0/a$b$b;-><init>(LKA0/c;)V

    .line 95
    .line 96
    .line 97
    iput-object p4, p0, LIB0/a$b;->v:Ldagger/internal/h;

    .line 98
    .line 99
    invoke-static/range {p49 .. p49}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 100
    .line 101
    .line 102
    move-result-object p2

    .line 103
    iput-object p2, p0, LIB0/a$b;->w:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 106
    .line 107
    .line 108
    move-result-object p2

    .line 109
    iput-object p2, p0, LIB0/a$b;->x:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static {p2}, Lorg/xbet/sportgame/markets/impl/domain/usecases/p;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/p;

    .line 112
    .line 113
    .line 114
    move-result-object p2

    .line 115
    iput-object p2, p0, LIB0/a$b;->y:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p4, p0, LIB0/a$b;->s:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p5, p0, LIB0/a$b;->t:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p6, p0, LIB0/a$b;->u:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p7, p0, LIB0/a$b;->v:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p8, p0, LIB0/a$b;->w:Ldagger/internal/h;

    .line 126
    .line 127
    move-object p9, p2

    .line 128
    invoke-static/range {p4 .. p9}, Lorg/xbet/sportgame/markets/impl/domain/scenarios/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/scenarios/b;

    .line 129
    .line 130
    .line 131
    move-result-object p2

    .line 132
    iput-object p2, p0, LIB0/a$b;->z:Ldagger/internal/h;

    .line 133
    .line 134
    new-instance p2, LIB0/a$b$x;

    .line 135
    .line 136
    invoke-direct {p2, p11}, LIB0/a$b$x;-><init>(LYB0/a;)V

    .line 137
    .line 138
    .line 139
    iput-object p2, p0, LIB0/a$b;->A:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 142
    .line 143
    .line 144
    move-result-object p2

    .line 145
    iput-object p2, p0, LIB0/a$b;->B:Ldagger/internal/h;

    .line 146
    .line 147
    new-instance p2, LIB0/a$b$g;

    .line 148
    .line 149
    invoke-direct {p2, p1}, LIB0/a$b$g;-><init>(LQW0/c;)V

    .line 150
    .line 151
    .line 152
    iput-object p2, p0, LIB0/a$b;->C:Ldagger/internal/h;

    .line 153
    .line 154
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iput-object p1, p0, LIB0/a$b;->D:Ldagger/internal/h;

    .line 159
    .line 160
    invoke-static/range {p46 .. p46}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    iput-object p1, p0, LIB0/a$b;->E:Ldagger/internal/h;

    .line 165
    .line 166
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/v;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/v;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    iput-object p1, p0, LIB0/a$b;->F:Ldagger/internal/h;

    .line 171
    .line 172
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 173
    .line 174
    .line 175
    move-result-object p1

    .line 176
    iput-object p1, p0, LIB0/a$b;->G:Ldagger/internal/h;

    .line 177
    .line 178
    return-void
.end method

.method public final c(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V
    .locals 3

    .line 1
    iget-object v0, p0, LIB0/a$b;->G:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/sportgame/markets/impl/domain/usecases/r;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/r;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iput-object v0, p0, LIB0/a$b;->H:Ldagger/internal/h;

    .line 8
    .line 9
    new-instance v0, LIB0/a$b$f;

    .line 10
    .line 11
    invoke-direct {v0, p4}, LIB0/a$b$f;-><init>(LAi0/a;)V

    .line 12
    .line 13
    .line 14
    iput-object v0, p0, LIB0/a$b;->I:Ldagger/internal/h;

    .line 15
    .line 16
    new-instance v0, LIB0/a$b$A;

    .line 17
    .line 18
    invoke-direct {v0, p2}, LIB0/a$b$A;-><init>(LKA0/c;)V

    .line 19
    .line 20
    .line 21
    iput-object v0, p0, LIB0/a$b;->J:Ldagger/internal/h;

    .line 22
    .line 23
    new-instance p2, LIB0/a$b$i;

    .line 24
    .line 25
    invoke-direct {p2, p7}, LIB0/a$b$i;-><init>(Lll/a;)V

    .line 26
    .line 27
    .line 28
    iput-object p2, p0, LIB0/a$b;->K:Ldagger/internal/h;

    .line 29
    .line 30
    new-instance p2, LIB0/a$b$l;

    .line 31
    .line 32
    invoke-direct {p2, p7}, LIB0/a$b$l;-><init>(Lll/a;)V

    .line 33
    .line 34
    .line 35
    iput-object p2, p0, LIB0/a$b;->L:Ldagger/internal/h;

    .line 36
    .line 37
    new-instance p2, LIB0/a$b$d;

    .line 38
    .line 39
    invoke-direct {p2, p7}, LIB0/a$b$d;-><init>(Lll/a;)V

    .line 40
    .line 41
    .line 42
    iput-object p2, p0, LIB0/a$b;->M:Ldagger/internal/h;

    .line 43
    .line 44
    new-instance p2, LIB0/a$b$h;

    .line 45
    .line 46
    invoke-direct {p2, p7}, LIB0/a$b$h;-><init>(Lll/a;)V

    .line 47
    .line 48
    .line 49
    iput-object p2, p0, LIB0/a$b;->N:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static/range {p38 .. p38}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    iput-object p2, p0, LIB0/a$b;->O:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    iput-object p2, p0, LIB0/a$b;->P:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static/range {p39 .. p39}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    iput-object p2, p0, LIB0/a$b;->Q:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object v0, p0, LIB0/a$b;->O:Ldagger/internal/h;

    .line 70
    .line 71
    iget-object v1, p0, LIB0/a$b;->P:Ldagger/internal/h;

    .line 72
    .line 73
    iget-object v2, p0, LIB0/a$b;->j:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static {v0, v1, p2, v2}, Lzg/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lzg/b;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    iput-object p2, p0, LIB0/a$b;->R:Ldagger/internal/h;

    .line 80
    .line 81
    new-instance p2, LIB0/a$b$j;

    .line 82
    .line 83
    invoke-direct {p2, p8}, LIB0/a$b$j;-><init>(LiR/a;)V

    .line 84
    .line 85
    .line 86
    iput-object p2, p0, LIB0/a$b;->S:Ldagger/internal/h;

    .line 87
    .line 88
    new-instance p2, LIB0/a$b$p;

    .line 89
    .line 90
    invoke-direct {p2, p4}, LIB0/a$b$p;-><init>(LAi0/a;)V

    .line 91
    .line 92
    .line 93
    iput-object p2, p0, LIB0/a$b;->T:Ldagger/internal/h;

    .line 94
    .line 95
    new-instance p2, LIB0/a$b$k;

    .line 96
    .line 97
    invoke-direct {p2, p9}, LIB0/a$b$k;-><init>(Lak/a;)V

    .line 98
    .line 99
    .line 100
    iput-object p2, p0, LIB0/a$b;->U:Ldagger/internal/h;

    .line 101
    .line 102
    new-instance p2, LIB0/a$b$q;

    .line 103
    .line 104
    invoke-direct {p2, p9}, LIB0/a$b$q;-><init>(Lak/a;)V

    .line 105
    .line 106
    .line 107
    iput-object p2, p0, LIB0/a$b;->V:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 110
    .line 111
    .line 112
    move-result-object p2

    .line 113
    iput-object p2, p0, LIB0/a$b;->W:Ldagger/internal/h;

    .line 114
    .line 115
    new-instance p2, LIB0/a$b$z;

    .line 116
    .line 117
    invoke-direct {p2, p5}, LIB0/a$b$z;-><init>(LDZ/m;)V

    .line 118
    .line 119
    .line 120
    iput-object p2, p0, LIB0/a$b;->X:Ldagger/internal/h;

    .line 121
    .line 122
    new-instance p2, LIB0/a$b$t;

    .line 123
    .line 124
    invoke-direct {p2, p9}, LIB0/a$b$t;-><init>(Lak/a;)V

    .line 125
    .line 126
    .line 127
    iput-object p2, p0, LIB0/a$b;->Y:Ldagger/internal/h;

    .line 128
    .line 129
    new-instance p2, LIB0/a$b$v;

    .line 130
    .line 131
    invoke-direct {p2, p6}, LIB0/a$b$v;-><init>(Ldk0/p;)V

    .line 132
    .line 133
    .line 134
    iput-object p2, p0, LIB0/a$b;->Z:Ldagger/internal/h;

    .line 135
    .line 136
    new-instance p2, LIB0/a$b$a;

    .line 137
    .line 138
    invoke-direct {p2, p3}, LIB0/a$b$a;-><init>(Lsw/a;)V

    .line 139
    .line 140
    .line 141
    iput-object p2, p0, LIB0/a$b;->a0:Ldagger/internal/h;

    .line 142
    .line 143
    new-instance p2, LIB0/a$b$y;

    .line 144
    .line 145
    invoke-direct {p2, p3}, LIB0/a$b$y;-><init>(Lsw/a;)V

    .line 146
    .line 147
    .line 148
    iput-object p2, p0, LIB0/a$b;->b0:Ldagger/internal/h;

    .line 149
    .line 150
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    iput-object p1, p0, LIB0/a$b;->c0:Ldagger/internal/h;

    .line 155
    .line 156
    invoke-static/range {p41 .. p41}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    iput-object p1, p0, LIB0/a$b;->d0:Ldagger/internal/h;

    .line 161
    .line 162
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/h;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/h;

    .line 163
    .line 164
    .line 165
    move-result-object p1

    .line 166
    iput-object p1, p0, LIB0/a$b;->e0:Ldagger/internal/h;

    .line 167
    .line 168
    new-instance p1, LIB0/a$b$o;

    .line 169
    .line 170
    invoke-direct {p1, p8}, LIB0/a$b$o;-><init>(LiR/a;)V

    .line 171
    .line 172
    .line 173
    iput-object p1, p0, LIB0/a$b;->f0:Ldagger/internal/h;

    .line 174
    .line 175
    return-void
.end method

.method public final d(LQW0/c;LKA0/c;Lsw/a;LAi0/a;LDZ/m;Ldk0/p;Lll/a;LiR/a;Lak/a;Ld90/a;LYB0/a;LC60/a;Lmo/f;LIj0/a;Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;LwX0/i;LwX0/c;LzX0/k;LAX0/b;Lqa0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lo9/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;LfX/b;LEP/b;LKB0/a;LTZ0/a;Lk8/c;Lc8/h;LwX0/a;LNP/e;Lra0/a;LqP/c;LxX0/a;Lorg/xbet/analytics/domain/b;Li8/c;Lk8/g;LEP/c;LwX0/g;LhB0/i;Lorg/xbet/ui_common/router/NavBarScreenTypes;LAu/b;LtB0/c;LRn/a;LRn/d;LNP/a;Ljo/a;LQn/a;LQn/b;Leu/i;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LIB0/a$b;->j:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object v2, v0, LIB0/a$b;->O:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object v3, v0, LIB0/a$b;->P:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object v4, v0, LIB0/a$b;->Q:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static {v1, v2, v3, v4}, LDg/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;)LDg/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iput-object v1, v0, LIB0/a$b;->g0:Ldagger/internal/h;

    .line 16
    .line 17
    new-instance v1, LIB0/a$b$c;

    .line 18
    .line 19
    move-object/from16 v2, p9

    .line 20
    .line 21
    invoke-direct {v1, v2}, LIB0/a$b$c;-><init>(Lak/a;)V

    .line 22
    .line 23
    .line 24
    iput-object v1, v0, LIB0/a$b;->h0:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p35 .. p35}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iput-object v1, v0, LIB0/a$b;->i0:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    iput-object v1, v0, LIB0/a$b;->j0:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {v1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/f;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/f;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    iput-object v1, v0, LIB0/a$b;->k0:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static/range {p36 .. p36}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iput-object v1, v0, LIB0/a$b;->l0:Ldagger/internal/h;

    .line 49
    .line 50
    new-instance v1, LIB0/a$b$u;

    .line 51
    .line 52
    move-object/from16 v2, p13

    .line 53
    .line 54
    invoke-direct {v1, v2}, LIB0/a$b$u;-><init>(Lmo/f;)V

    .line 55
    .line 56
    .line 57
    iput-object v1, v0, LIB0/a$b;->m0:Ldagger/internal/h;

    .line 58
    .line 59
    invoke-static/range {p42 .. p42}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    iput-object v1, v0, LIB0/a$b;->n0:Ldagger/internal/h;

    .line 64
    .line 65
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    iput-object v1, v0, LIB0/a$b;->o0:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static/range {p37 .. p37}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    iput-object v1, v0, LIB0/a$b;->p0:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    iput-object v1, v0, LIB0/a$b;->q0:Ldagger/internal/h;

    .line 82
    .line 83
    new-instance v1, LIB0/a$b$w;

    .line 84
    .line 85
    move-object/from16 v2, p10

    .line 86
    .line 87
    invoke-direct {v1, v2}, LIB0/a$b$w;-><init>(Ld90/a;)V

    .line 88
    .line 89
    .line 90
    iput-object v1, v0, LIB0/a$b;->r0:Ldagger/internal/h;

    .line 91
    .line 92
    new-instance v1, LIB0/a$b$e;

    .line 93
    .line 94
    move-object/from16 v2, p8

    .line 95
    .line 96
    invoke-direct {v1, v2}, LIB0/a$b$e;-><init>(LiR/a;)V

    .line 97
    .line 98
    .line 99
    iput-object v1, v0, LIB0/a$b;->s0:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object v2, v0, LIB0/a$b;->I:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v3, v0, LIB0/a$b;->J:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v4, v0, LIB0/a$b;->K:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v5, v0, LIB0/a$b;->L:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v6, v0, LIB0/a$b;->M:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v7, v0, LIB0/a$b;->N:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v8, v0, LIB0/a$b;->C:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v9, v0, LIB0/a$b;->i:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object v10, v0, LIB0/a$b;->R:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object v11, v0, LIB0/a$b;->S:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v12, v0, LIB0/a$b;->T:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v13, v0, LIB0/a$b;->U:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v14, v0, LIB0/a$b;->V:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v15, v0, LIB0/a$b;->W:Ldagger/internal/h;

    .line 128
    .line 129
    move-object/from16 p41, v1

    .line 130
    .line 131
    iget-object v1, v0, LIB0/a$b;->X:Ldagger/internal/h;

    .line 132
    .line 133
    move-object/from16 p21, v1

    .line 134
    .line 135
    iget-object v1, v0, LIB0/a$b;->Y:Ldagger/internal/h;

    .line 136
    .line 137
    move-object/from16 p22, v1

    .line 138
    .line 139
    iget-object v1, v0, LIB0/a$b;->Z:Ldagger/internal/h;

    .line 140
    .line 141
    move-object/from16 p23, v1

    .line 142
    .line 143
    iget-object v1, v0, LIB0/a$b;->a0:Ldagger/internal/h;

    .line 144
    .line 145
    move-object/from16 p24, v1

    .line 146
    .line 147
    iget-object v1, v0, LIB0/a$b;->b0:Ldagger/internal/h;

    .line 148
    .line 149
    move-object/from16 p25, v1

    .line 150
    .line 151
    iget-object v1, v0, LIB0/a$b;->c0:Ldagger/internal/h;

    .line 152
    .line 153
    move-object/from16 p26, v1

    .line 154
    .line 155
    iget-object v1, v0, LIB0/a$b;->e0:Ldagger/internal/h;

    .line 156
    .line 157
    move-object/from16 p27, v1

    .line 158
    .line 159
    iget-object v1, v0, LIB0/a$b;->f0:Ldagger/internal/h;

    .line 160
    .line 161
    move-object/from16 p28, v1

    .line 162
    .line 163
    iget-object v1, v0, LIB0/a$b;->g0:Ldagger/internal/h;

    .line 164
    .line 165
    move-object/from16 p29, v1

    .line 166
    .line 167
    iget-object v1, v0, LIB0/a$b;->h0:Ldagger/internal/h;

    .line 168
    .line 169
    move-object/from16 p30, v1

    .line 170
    .line 171
    iget-object v1, v0, LIB0/a$b;->i0:Ldagger/internal/h;

    .line 172
    .line 173
    move-object/from16 p31, v1

    .line 174
    .line 175
    iget-object v1, v0, LIB0/a$b;->k0:Ldagger/internal/h;

    .line 176
    .line 177
    move-object/from16 p32, v1

    .line 178
    .line 179
    iget-object v1, v0, LIB0/a$b;->l0:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 p33, v1

    .line 182
    .line 183
    iget-object v1, v0, LIB0/a$b;->m0:Ldagger/internal/h;

    .line 184
    .line 185
    move-object/from16 p34, v1

    .line 186
    .line 187
    iget-object v1, v0, LIB0/a$b;->B:Ldagger/internal/h;

    .line 188
    .line 189
    move-object/from16 p35, v1

    .line 190
    .line 191
    iget-object v1, v0, LIB0/a$b;->n0:Ldagger/internal/h;

    .line 192
    .line 193
    move-object/from16 p36, v1

    .line 194
    .line 195
    iget-object v1, v0, LIB0/a$b;->o0:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p37, v1

    .line 198
    .line 199
    iget-object v1, v0, LIB0/a$b;->p0:Ldagger/internal/h;

    .line 200
    .line 201
    move-object/from16 p38, v1

    .line 202
    .line 203
    iget-object v1, v0, LIB0/a$b;->q0:Ldagger/internal/h;

    .line 204
    .line 205
    move-object/from16 p39, v1

    .line 206
    .line 207
    iget-object v1, v0, LIB0/a$b;->r0:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 p40, v1

    .line 210
    .line 211
    move-object/from16 p7, v2

    .line 212
    .line 213
    move-object/from16 p8, v3

    .line 214
    .line 215
    move-object/from16 p9, v4

    .line 216
    .line 217
    move-object/from16 p10, v5

    .line 218
    .line 219
    move-object/from16 p11, v6

    .line 220
    .line 221
    move-object/from16 p12, v7

    .line 222
    .line 223
    move-object/from16 p13, v8

    .line 224
    .line 225
    move-object/from16 p14, v9

    .line 226
    .line 227
    move-object/from16 p15, v10

    .line 228
    .line 229
    move-object/from16 p16, v11

    .line 230
    .line 231
    move-object/from16 p17, v12

    .line 232
    .line 233
    move-object/from16 p18, v13

    .line 234
    .line 235
    move-object/from16 p19, v14

    .line 236
    .line 237
    move-object/from16 p20, v15

    .line 238
    .line 239
    invoke-static/range {p7 .. p41}, Lorg/xbet/sportgame/markets/impl/presentation/base/n;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/presentation/base/n;

    .line 240
    .line 241
    .line 242
    move-result-object v1

    .line 243
    iput-object v1, v0, LIB0/a$b;->t0:Ldagger/internal/h;

    .line 244
    .line 245
    invoke-static/range {p53 .. p53}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 246
    .line 247
    .line 248
    move-result-object v1

    .line 249
    iput-object v1, v0, LIB0/a$b;->u0:Ldagger/internal/h;

    .line 250
    .line 251
    new-instance v1, LIB0/a$b$s;

    .line 252
    .line 253
    move-object/from16 v2, p6

    .line 254
    .line 255
    invoke-direct {v1, v2}, LIB0/a$b$s;-><init>(Ldk0/p;)V

    .line 256
    .line 257
    .line 258
    iput-object v1, v0, LIB0/a$b;->v0:Ldagger/internal/h;

    .line 259
    .line 260
    invoke-static/range {p44 .. p44}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 261
    .line 262
    .line 263
    move-result-object v1

    .line 264
    iput-object v1, v0, LIB0/a$b;->w0:Ldagger/internal/h;

    .line 265
    .line 266
    iget-object v1, v0, LIB0/a$b;->E:Ldagger/internal/h;

    .line 267
    .line 268
    invoke-static {v1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/l;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/domain/usecases/l;

    .line 269
    .line 270
    .line 271
    move-result-object v1

    .line 272
    iput-object v1, v0, LIB0/a$b;->x0:Ldagger/internal/h;

    .line 273
    .line 274
    iget-object v2, v0, LIB0/a$b;->i:Ldagger/internal/h;

    .line 275
    .line 276
    iget-object v3, v0, LIB0/a$b;->n:Ldagger/internal/h;

    .line 277
    .line 278
    iget-object v4, v0, LIB0/a$b;->o:Ldagger/internal/h;

    .line 279
    .line 280
    iget-object v5, v0, LIB0/a$b;->z:Ldagger/internal/h;

    .line 281
    .line 282
    iget-object v6, v0, LIB0/a$b;->A:Ldagger/internal/h;

    .line 283
    .line 284
    iget-object v7, v0, LIB0/a$b;->B:Ldagger/internal/h;

    .line 285
    .line 286
    iget-object v8, v0, LIB0/a$b;->C:Ldagger/internal/h;

    .line 287
    .line 288
    iget-object v9, v0, LIB0/a$b;->D:Ldagger/internal/h;

    .line 289
    .line 290
    iget-object v10, v0, LIB0/a$b;->F:Ldagger/internal/h;

    .line 291
    .line 292
    iget-object v11, v0, LIB0/a$b;->v:Ldagger/internal/h;

    .line 293
    .line 294
    iget-object v12, v0, LIB0/a$b;->H:Ldagger/internal/h;

    .line 295
    .line 296
    iget-object v13, v0, LIB0/a$b;->t0:Ldagger/internal/h;

    .line 297
    .line 298
    iget-object v14, v0, LIB0/a$b;->q0:Ldagger/internal/h;

    .line 299
    .line 300
    iget-object v15, v0, LIB0/a$b;->u0:Ldagger/internal/h;

    .line 301
    .line 302
    move-object/from16 p17, v1

    .line 303
    .line 304
    iget-object v1, v0, LIB0/a$b;->v0:Ldagger/internal/h;

    .line 305
    .line 306
    move-object/from16 p15, v1

    .line 307
    .line 308
    iget-object v1, v0, LIB0/a$b;->w0:Ldagger/internal/h;

    .line 309
    .line 310
    move-object/from16 p16, v1

    .line 311
    .line 312
    move-object/from16 p1, v2

    .line 313
    .line 314
    move-object/from16 p2, v3

    .line 315
    .line 316
    move-object/from16 p3, v4

    .line 317
    .line 318
    move-object/from16 p4, v5

    .line 319
    .line 320
    move-object/from16 p5, v6

    .line 321
    .line 322
    move-object/from16 p6, v7

    .line 323
    .line 324
    move-object/from16 p7, v8

    .line 325
    .line 326
    move-object/from16 p8, v9

    .line 327
    .line 328
    move-object/from16 p9, v10

    .line 329
    .line 330
    move-object/from16 p10, v11

    .line 331
    .line 332
    move-object/from16 p11, v12

    .line 333
    .line 334
    move-object/from16 p12, v13

    .line 335
    .line 336
    move-object/from16 p13, v14

    .line 337
    .line 338
    move-object/from16 p14, v15

    .line 339
    .line 340
    invoke-static/range {p1 .. p17}, Lorg/xbet/sportgame/markets/impl/presentation/markets/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/presentation/markets/j;

    .line 341
    .line 342
    .line 343
    move-result-object v1

    .line 344
    iput-object v1, v0, LIB0/a$b;->y0:Lorg/xbet/sportgame/markets/impl/presentation/markets/j;

    .line 345
    .line 346
    invoke-static {v1}, LIB0/h;->c(Lorg/xbet/sportgame/markets/impl/presentation/markets/j;)Ldagger/internal/h;

    .line 347
    .line 348
    .line 349
    move-result-object v1

    .line 350
    iput-object v1, v0, LIB0/a$b;->z0:Ldagger/internal/h;

    .line 351
    .line 352
    return-void
.end method

.method public final e(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LIB0/a$b;->a:LTZ0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->a(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LTZ0/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LIB0/a$b;->b:LzX0/k;

    .line 7
    .line 8
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->b(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LzX0/k;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, LIB0/a$b;->c:LAX0/b;

    .line 12
    .line 13
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/base/g;->c(Lorg/xbet/sportgame/markets/impl/presentation/base/BaseMarketsFragment;LAX0/b;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, LIB0/a$b;->d:LIj0/a;

    .line 17
    .line 18
    invoke-interface {v0}, LIj0/a;->c()LMj0/b;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, LMj0/b;

    .line 27
    .line 28
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/g;->d(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;LMj0/b;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, LIB0/a$b;->e:Lsw/a;

    .line 32
    .line 33
    invoke-interface {v0}, Lsw/a;->n()Lww/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, Lww/a;

    .line 42
    .line 43
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/g;->a(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;Lww/a;)V

    .line 44
    .line 45
    .line 46
    iget-object v0, p0, LIB0/a$b;->f:Lqa0/a;

    .line 47
    .line 48
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/g;->c(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;Lqa0/a;)V

    .line 49
    .line 50
    .line 51
    iget-object v0, p0, LIB0/a$b;->g:Ld90/a;

    .line 52
    .line 53
    invoke-interface {v0}, Ld90/a;->b()Lf90/a;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, Lf90/a;

    .line 62
    .line 63
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/g;->b(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;Lf90/a;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, LIB0/a$b;->z0:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    check-cast v0, LIB0/g;

    .line 73
    .line 74
    invoke-static {p1, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/g;->e(Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;LIB0/g;)V

    .line 75
    .line 76
    .line 77
    return-object p1
.end method
