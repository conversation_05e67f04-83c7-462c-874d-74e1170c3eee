.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0008\u0010\tR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;",
        "",
        "Lu81/b;",
        "repository",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(Lu81/b;Lm8/a;)V",
        "a",
        "Lu81/b;",
        "b",
        "Lm8/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lu81/b;Lm8/a;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;->b:Lm8/a;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;)Lu81/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/e;->a:Lu81/b;

    .line 2
    .line 3
    return-object p0
.end method
