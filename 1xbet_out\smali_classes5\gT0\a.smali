.class public final LgT0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J \u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0086\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LgT0/a;",
        "",
        "LhT0/g;",
        "getTimeTableTimeModelUseCase",
        "LhT0/c;",
        "covertTo12FormatUseCase",
        "<init>",
        "(LhT0/g;LhT0/c;)V",
        "",
        "turnOn",
        "is24Format",
        "LfT0/a;",
        "a",
        "(ZZ)LfT0/a;",
        "LhT0/g;",
        "b",
        "LhT0/c;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LhT0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LhT0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LhT0/g;LhT0/c;)V
    .locals 0
    .param p1    # LhT0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LhT0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LgT0/a;->a:LhT0/g;

    .line 5
    .line 6
    iput-object p2, p0, LgT0/a;->b:LhT0/c;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(ZZ)LfT0/a;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LgT0/a;->a:LhT0/g;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LhT0/g;->a(Z)LfT0/a;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    new-instance p2, LfT0/a;

    .line 10
    .line 11
    invoke-virtual {p1}, LfT0/a;->a()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-virtual {p1}, LfT0/a;->b()I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    sget-object v1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->TWENTY_FOUR:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 20
    .line 21
    invoke-direct {p2, v0, p1, v1}, LfT0/a;-><init>(IILorg/xbet/themesettings/impl/domain/model/TimeFrame;)V

    .line 22
    .line 23
    .line 24
    return-object p2

    .line 25
    :cond_0
    iget-object p2, p0, LgT0/a;->b:LhT0/c;

    .line 26
    .line 27
    invoke-virtual {p1}, LfT0/a;->a()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    invoke-virtual {p1}, LfT0/a;->b()I

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    invoke-virtual {p2, v0, p1}, LhT0/c;->a(II)LfT0/a;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    return-object p1
.end method
