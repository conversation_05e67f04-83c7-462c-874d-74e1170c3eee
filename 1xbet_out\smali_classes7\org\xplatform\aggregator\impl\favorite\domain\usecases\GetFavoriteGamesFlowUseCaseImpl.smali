.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv81/m;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J,\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\u000c2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0096\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;",
        "Lv81/m;",
        "Lu81/b;",
        "repository",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(Lu81/b;Lm8/a;)V",
        "",
        "brandsApi",
        "",
        "endPoint",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;",
        "Lu81/b;",
        "b",
        "Lm8/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lu81/b;Lm8/a;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;->b:Lm8/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public a(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;
    .locals 1
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            ")",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;->a:Lu81/b;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, Lu81/b;->h(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    new-instance p2, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl$invoke$$inlined$map$1;

    .line 8
    .line 9
    invoke-direct {p2, p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl$invoke$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowUseCaseImpl;->b:Lm8/a;

    .line 13
    .line 14
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-static {p2, p1}, Lkotlinx/coroutines/flow/g;->Z(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    return-object p1
.end method
