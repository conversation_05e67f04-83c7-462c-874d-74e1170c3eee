.class public final LNN0/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u001a\u001d\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001d\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0005\u001a\u001d\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0005\u001a\u0015\u0010\n\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a\u0015\u0010\u000c\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000c\u0010\u000b\u001a\u0015\u0010\r\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\r\u0010\u000b\u001a\u0015\u0010\u000e\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000e\u0010\u000b\u00a8\u0006\u000f"
    }
    d2 = {
        "Landroid/view/View;",
        "leftTeamState",
        "rightTeamState",
        "",
        "b",
        "(Landroid/view/View;Landroid/view/View;)V",
        "d",
        "f",
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;",
        "statisticsEventCard",
        "a",
        "(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V",
        "c",
        "e",
        "g",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V
    .locals 2
    .param p0    # Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->YELLOW:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p0, v0, v0, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public static final b(Landroid/view/View;Landroid/view/View;)V
    .locals 1
    .param p0    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget v0, Lpb/e;->market_yellow:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1, v0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static final c(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V
    .locals 3
    .param p0    # Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->GREEN:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->RED:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static final d(Landroid/view/View;Landroid/view/View;)V
    .locals 1
    .param p0    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget v0, Lpb/e;->green:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 4
    .line 5
    .line 6
    sget p0, Lpb/e;->red_soft:I

    .line 7
    .line 8
    invoke-virtual {p1, p0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final e(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V
    .locals 3
    .param p0    # Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->RED:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->GREEN:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static final f(Landroid/view/View;Landroid/view/View;)V
    .locals 1
    .param p0    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget v0, Lpb/e;->red_soft:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 4
    .line 5
    .line 6
    sget p0, Lpb/e;->green:I

    .line 7
    .line 8
    invoke-virtual {p1, p0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final g(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V
    .locals 2
    .param p0    # Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x0

    .line 3
    invoke-virtual {p0, v0, v0, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
