.class public final LIB0/a$b$j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIB0/a$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "j"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/h<",
        "LwR/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LiR/a;


# direct methods
.method public constructor <init>(LiR/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LIB0/a$b$j;->a:LiR/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()LwR/a;
    .locals 1

    .line 1
    iget-object v0, p0, LIB0/a$b$j;->a:LiR/a;

    .line 2
    .line 3
    invoke-interface {v0}, LiR/a;->W()LwR/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LwR/a;

    .line 12
    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LIB0/a$b$j;->a()LwR/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
