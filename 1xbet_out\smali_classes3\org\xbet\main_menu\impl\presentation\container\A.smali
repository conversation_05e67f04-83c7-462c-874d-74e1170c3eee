.class public final Lorg/xbet/main_menu/impl/presentation/container/A;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LU80/a;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lmn0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LA7/a;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LGa/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/b;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/u;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LZa0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LZc0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Loi/a;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lej0/d;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LIn0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LYU/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/e;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lq80/a;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lh9/a;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/g;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXa0/f;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/a;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "LGa/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lfk/b;",
            ">;",
            "LBc/a<",
            "Lfk/u;",
            ">;",
            "LBc/a<",
            "LZa0/a;",
            ">;",
            "LBc/a<",
            "LZc0/b;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Loi/a;",
            ">;",
            "LBc/a<",
            "Lej0/d;",
            ">;",
            "LBc/a<",
            "LIn0/c;",
            ">;",
            "LBc/a<",
            "LYU/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;",
            "LBc/a<",
            "Lp9/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lq80/a;",
            ">;",
            "LBc/a<",
            "Lh9/a;",
            ">;",
            "LBc/a<",
            "Lp9/g;",
            ">;",
            "LBc/a<",
            "LXa0/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "LU80/a;",
            ">;",
            "LBc/a<",
            "Lmn0/i;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LA7/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/A;->D:LBc/a;

    .line 93
    .line 94
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/main_menu/impl/presentation/container/A;
    .locals 31
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/a;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "LGa/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lfk/b;",
            ">;",
            "LBc/a<",
            "Lfk/u;",
            ">;",
            "LBc/a<",
            "LZa0/a;",
            ">;",
            "LBc/a<",
            "LZc0/b;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Loi/a;",
            ">;",
            "LBc/a<",
            "Lej0/d;",
            ">;",
            "LBc/a<",
            "LIn0/c;",
            ">;",
            "LBc/a<",
            "LYU/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;",
            "LBc/a<",
            "Lp9/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lq80/a;",
            ">;",
            "LBc/a<",
            "Lh9/a;",
            ">;",
            "LBc/a<",
            "Lp9/g;",
            ">;",
            "LBc/a<",
            "LXa0/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "LU80/a;",
            ">;",
            "LBc/a<",
            "Lmn0/i;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LA7/a;",
            ">;)",
            "Lorg/xbet/main_menu/impl/presentation/container/A;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/A;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    invoke-direct/range {v0 .. v30}, Lorg/xbet/main_menu/impl/presentation/container/A;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 64
    .line 65
    .line 66
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/a;LwX0/c;LGa/a;Lm8/a;Lfk/b;Lfk/u;LZa0/a;LZc0/b;LxX0/a;Loi/a;Lej0/d;LIn0/c;LYU/a;Lorg/xbet/remoteconfig/domain/usecases/k;Lp9/e;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lfk/l;Lq80/a;Lh9/a;Lp9/g;LXa0/f;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;Lgk0/a;LU80/a;Lmn0/i;LfX/b;LA7/a;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;
    .locals 32

    .line 1
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    invoke-direct/range {v0 .. v31}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;-><init>(Landroidx/lifecycle/Q;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/a;LwX0/c;LGa/a;Lm8/a;Lfk/b;Lfk/u;LZa0/a;LZc0/b;LxX0/a;Loi/a;Lej0/d;LIn0/c;LYU/a;Lorg/xbet/remoteconfig/domain/usecases/k;Lp9/e;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lfk/l;Lq80/a;Lh9/a;Lp9/g;LXa0/f;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;Lgk0/a;LU80/a;Lmn0/i;LfX/b;LA7/a;)V

    .line 66
    .line 67
    .line 68
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;
    .locals 33

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v3, v1

    .line 10
    check-cast v3, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v4, v1

    .line 19
    check-cast v4, Lorg/xbet/main_menu/impl/domain/usecases/a;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v5, v1

    .line 28
    check-cast v5, LwX0/c;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v6, v1

    .line 37
    check-cast v6, LGa/a;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v7, v1

    .line 46
    check-cast v7, Lm8/a;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v8, v1

    .line 55
    check-cast v8, Lfk/b;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, Lfk/u;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v10, v1

    .line 73
    check-cast v10, LZa0/a;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v11, v1

    .line 82
    check-cast v11, LZc0/b;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v12, v1

    .line 91
    check-cast v12, LxX0/a;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v13, v1

    .line 100
    check-cast v13, Loi/a;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    move-object v14, v1

    .line 109
    check-cast v14, Lej0/d;

    .line 110
    .line 111
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->m:LBc/a;

    .line 112
    .line 113
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    move-object v15, v1

    .line 118
    check-cast v15, LIn0/c;

    .line 119
    .line 120
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->n:LBc/a;

    .line 121
    .line 122
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    move-object/from16 v16, v1

    .line 127
    .line 128
    check-cast v16, LYU/a;

    .line 129
    .line 130
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->o:LBc/a;

    .line 131
    .line 132
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    move-object/from16 v17, v1

    .line 137
    .line 138
    check-cast v17, Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 139
    .line 140
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->p:LBc/a;

    .line 141
    .line 142
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    move-object/from16 v18, v1

    .line 147
    .line 148
    check-cast v18, Lp9/e;

    .line 149
    .line 150
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->q:LBc/a;

    .line 151
    .line 152
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    move-object/from16 v19, v1

    .line 157
    .line 158
    check-cast v19, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 159
    .line 160
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->r:LBc/a;

    .line 161
    .line 162
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 163
    .line 164
    .line 165
    move-result-object v1

    .line 166
    move-object/from16 v20, v1

    .line 167
    .line 168
    check-cast v20, Lfk/l;

    .line 169
    .line 170
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->s:LBc/a;

    .line 171
    .line 172
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    move-object/from16 v21, v1

    .line 177
    .line 178
    check-cast v21, Lq80/a;

    .line 179
    .line 180
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->t:LBc/a;

    .line 181
    .line 182
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    move-object/from16 v22, v1

    .line 187
    .line 188
    check-cast v22, Lh9/a;

    .line 189
    .line 190
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->u:LBc/a;

    .line 191
    .line 192
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    move-object/from16 v23, v1

    .line 197
    .line 198
    check-cast v23, Lp9/g;

    .line 199
    .line 200
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->v:LBc/a;

    .line 201
    .line 202
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    move-object/from16 v24, v1

    .line 207
    .line 208
    check-cast v24, LXa0/f;

    .line 209
    .line 210
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->w:LBc/a;

    .line 211
    .line 212
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v1

    .line 216
    move-object/from16 v25, v1

    .line 217
    .line 218
    check-cast v25, Lorg/xbet/ui_common/utils/M;

    .line 219
    .line 220
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->x:LBc/a;

    .line 221
    .line 222
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    move-object/from16 v26, v1

    .line 227
    .line 228
    check-cast v26, LHX0/e;

    .line 229
    .line 230
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->y:LBc/a;

    .line 231
    .line 232
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v1

    .line 236
    move-object/from16 v27, v1

    .line 237
    .line 238
    check-cast v27, Lorg/xbet/ui_common/utils/internet/a;

    .line 239
    .line 240
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->z:LBc/a;

    .line 241
    .line 242
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v1

    .line 246
    move-object/from16 v28, v1

    .line 247
    .line 248
    check-cast v28, Lgk0/a;

    .line 249
    .line 250
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->A:LBc/a;

    .line 251
    .line 252
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    move-object/from16 v29, v1

    .line 257
    .line 258
    check-cast v29, LU80/a;

    .line 259
    .line 260
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->B:LBc/a;

    .line 261
    .line 262
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 263
    .line 264
    .line 265
    move-result-object v1

    .line 266
    move-object/from16 v30, v1

    .line 267
    .line 268
    check-cast v30, Lmn0/i;

    .line 269
    .line 270
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->C:LBc/a;

    .line 271
    .line 272
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v1

    .line 276
    move-object/from16 v31, v1

    .line 277
    .line 278
    check-cast v31, LfX/b;

    .line 279
    .line 280
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/A;->D:LBc/a;

    .line 281
    .line 282
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 283
    .line 284
    .line 285
    move-result-object v1

    .line 286
    move-object/from16 v32, v1

    .line 287
    .line 288
    check-cast v32, LA7/a;

    .line 289
    .line 290
    move-object/from16 v2, p1

    .line 291
    .line 292
    invoke-static/range {v2 .. v32}, Lorg/xbet/main_menu/impl/presentation/container/A;->c(Landroidx/lifecycle/Q;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/a;LwX0/c;LGa/a;Lm8/a;Lfk/b;Lfk/u;LZa0/a;LZc0/b;LxX0/a;Loi/a;Lej0/d;LIn0/c;LYU/a;Lorg/xbet/remoteconfig/domain/usecases/k;Lp9/e;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lfk/l;Lq80/a;Lh9/a;Lp9/g;LXa0/f;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;Lgk0/a;LU80/a;Lmn0/i;LfX/b;LA7/a;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 293
    .line 294
    .line 295
    move-result-object v1

    .line 296
    return-object v1
.end method
