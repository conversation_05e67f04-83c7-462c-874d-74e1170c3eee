.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 22\u00020\u00012\u00020\u0002:\u0001\'B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\r\u001a\u00020\u000c2\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\u0008\r\u0010\u0011J\u0015\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\r\u0010\u0014J\u0017\u0010\u0015\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0015\u0010\u000eJ\u0017\u0010\u0015\u001a\u00020\u000c2\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\u0008\u0015\u0010\u0011J\u0015\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\u0017\u0010\u0017\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0017\u0010\u000eJ\u0017\u0010\u0017\u001a\u00020\u000c2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u0017\u0010\u0019J\u0017\u0010\u001a\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001a\u0010\u000eJ\u0017\u0010\u001a\u001a\u00020\u000c2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u0019J\u0017\u0010\u001b\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001b\u0010\u000eJ\u0017\u0010\u001b\u001a\u00020\u000c2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001b\u0010\u0019J\u0017\u0010\u001c\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001c\u0010\u000eJ\u0017\u0010\u001c\u001a\u00020\u000c2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001c\u0010\u0019J\'\u0010 \u001a\u00020\u000c2\u0018\u0010\u001f\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00120\u001e0\u001d\u00a2\u0006\u0004\u0008 \u0010!J\u0015\u0010$\u001a\u00020\u000c2\u0006\u0010#\u001a\u00020\"\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010)\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010-\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0014\u00101\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100\u00a8\u00063"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "resId",
        "",
        "setTopLogo",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "url",
        "(Ljava/lang/String;)V",
        "setBotLogo",
        "text",
        "setTopTeamName",
        "",
        "(Ljava/lang/CharSequence;)V",
        "setBotTeamName",
        "setTopSeekScore",
        "setBotSeekScore",
        "",
        "Lkotlin/Pair;",
        "scores",
        "setScores",
        "(Ljava/util/List;)V",
        "",
        "canScroll",
        "setScroll",
        "(Z)V",
        "LC31/z;",
        "a",
        "LC31/z;",
        "binding",
        "Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;",
        "b",
        "Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;",
        "scrollLayoutManager",
        "Lorg/xbet/uikit_sport/eventcard/middle/h;",
        "c",
        "Lorg/xbet/uikit_sport/eventcard/middle/h;",
        "scoreAdapter",
        "d",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final d:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final e:I


# instance fields
.field public final a:LC31/z;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_sport/eventcard/middle/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->d:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->e:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-static {p2, p0}, LC31/z;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/z;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    .line 7
    new-instance v0, Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;

    const/4 v4, 0x2

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;-><init>(Landroid/content/Context;IZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->b:Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;

    .line 8
    new-instance p1, Lorg/xbet/uikit_sport/eventcard/middle/h;

    invoke-direct {p1}, Lorg/xbet/uikit_sport/eventcard/middle/h;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 9
    iget-object p3, p2, LC31/z;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {p3, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 10
    iget-object p2, p2, LC31/z;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {p2, p1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleScoreStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final setBotLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v1, v0, LC31/z;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotSeekScore(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotSeekScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotSeekScore(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->c:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->c:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->d:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->d:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setScores(Ljava/util/List;)V
    .locals 4
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lkotlin/Pair<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/h;->setItems(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    .line 7
    .line 8
    iget-object p1, p1, LC31/z;->l:Lorg/xbet/uikit/components/separator/Separator;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 11
    .line 12
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/middle/h;->getItemCount()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const/4 v1, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    if-nez v0, :cond_0

    .line 19
    .line 20
    const/4 v0, 0x1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/4 v0, 0x0

    .line 23
    :goto_0
    if-eqz v0, :cond_1

    .line 24
    .line 25
    const/4 v0, 0x4

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    const/4 v0, 0x0

    .line 28
    :goto_1
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 32
    .line 33
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/middle/h;->getItemCount()I

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    if-lez p1, :cond_2

    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    .line 40
    .line 41
    iget-object p1, p1, LC31/z;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 44
    .line 45
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/middle/h;->getItemCount()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    sub-int/2addr v0, v1

    .line 50
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 51
    .line 52
    .line 53
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    .line 54
    .line 55
    iget-object p1, p1, LC31/z;->e:Landroid/view/View;

    .line 56
    .line 57
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 58
    .line 59
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/middle/h;->getItemCount()I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    const/4 v3, 0x3

    .line 64
    if-le v0, v3, :cond_3

    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->b:Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;

    .line 67
    .line 68
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;->k()Z

    .line 69
    .line 70
    .line 71
    move-result v0

    .line 72
    if-nez v0, :cond_3

    .line 73
    .line 74
    goto :goto_2

    .line 75
    :cond_3
    const/4 v1, 0x0

    .line 76
    :goto_2
    if-eqz v1, :cond_4

    .line 77
    .line 78
    goto :goto_3

    .line 79
    :cond_4
    const/16 v2, 0x8

    .line 80
    .line 81
    :goto_3
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method public final setScroll(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->b:Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/utils/recycerview/ScrollLayoutManager;->l(Z)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    .line 7
    .line 8
    iget-object v0, v0, LC31/z;->e:Landroid/view/View;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    if-nez p1, :cond_1

    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->c:Lorg/xbet/uikit_sport/eventcard/middle/h;

    .line 14
    .line 15
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/middle/h;->getItemCount()I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    const/4 v2, 0x3

    .line 20
    if-gt p1, v2, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 p1, 0x0

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    :goto_0
    const/4 p1, 0x1

    .line 26
    :goto_1
    if-eqz p1, :cond_2

    .line 27
    .line 28
    const/16 v1, 0x8

    .line 29
    .line 30
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final setTopLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->i:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->i:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v1, v0, LC31/z;->i:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopSeekScore(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopSeekScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopSeekScore(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->j:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->j:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->k:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->a:LC31/z;

    iget-object v0, v0, LC31/z;->k:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
