.class public final Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LN31/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 52\u00020\u00012\u00020\u0002:\u0001\u0017B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0019\u0010\u0012\u001a\u00020\r2\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0015R\u0016\u0010\u0019\u001a\u00020\u00078\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u001d\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0014\u0010!\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u0014\u0010#\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010 R\u0014\u0010&\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010%R\u0016\u0010\'\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0018R\u0016\u0010)\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u0010\u0018R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R \u00104\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\r008\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103\u00a8\u00066"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;",
        "Landroid/widget/FrameLayout;",
        "LN31/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "LL11/c;",
        "image",
        "",
        "setIcon",
        "(LL11/c;)V",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/String;)V",
        "f",
        "()V",
        "e",
        "a",
        "I",
        "backgroundColor",
        "Landroid/widget/ImageView;",
        "b",
        "Landroid/widget/ImageView;",
        "iconView",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "c",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "iconShimmerView",
        "d",
        "titleShimmerView",
        "Landroid/widget/TextView;",
        "Landroid/widget/TextView;",
        "titleTextView",
        "minTitleSize",
        "g",
        "defaultTitleSize",
        "Lorg/xbet/uikit/utils/z;",
        "h",
        "Lkotlin/j;",
        "getIconLoadHelper",
        "()Lorg/xbet/uikit/utils/z;",
        "iconLoadHelper",
        "Lkotlin/Function1;",
        "Landroid/content/res/TypedArray;",
        "i",
        "Lkotlin/jvm/functions/Function1;",
        "applyAttrs",
        "j",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final j:Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final k:I


# instance fields
.field public a:I

.field public final b:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:I

.field public g:I

.field public final h:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/content/res/TypedArray;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->j:Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->k:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct/range {p0 .. p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v6, Landroid/widget/ImageView;

    invoke-direct {v6, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    iput-object v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->b:Landroid/widget/ImageView;

    .line 6
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object v7, v0

    iput-object v7, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 7
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->d:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    new-instance v2, Landroid/widget/TextView;

    invoke-direct {v2, p1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->text_10:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 10
    new-instance v3, LQ31/a;

    invoke-direct {v3, p0}, LQ31/a;-><init>(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;)V

    invoke-static {v3}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v3

    iput-object v3, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->h:Lkotlin/j;

    .line 11
    new-instance v3, LQ31/b;

    invoke-direct {v3, p0, p1}, LQ31/b;-><init>(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;Landroid/content/Context;)V

    iput-object v3, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->i:Lkotlin/jvm/functions/Function1;

    .line 12
    sget-object v4, Lm31/g;->SportsCollectionSimple:[I

    const/4 v5, 0x0

    .line 13
    invoke-virtual {p1, p2, v4, p3, v5}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v1

    invoke-interface {v3, v1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {v1}, Landroid/content/res/TypedArray;->recycle()V

    .line 14
    invoke-virtual {p0, v6}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 15
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 16
    invoke-virtual {p0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 17
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->c(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;)Lorg/xbet/uikit/utils/z;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->d(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;)Lorg/xbet/uikit/utils/z;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 23

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p2

    .line 6
    .line 7
    sget v3, Lm31/g;->SportsCollectionSimple_itemWidth:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-virtual {v2, v3, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 11
    .line 12
    .line 13
    move-result v3

    .line 14
    sget v5, Lm31/g;->SportsCollectionSimple_itemHeight:I

    .line 15
    .line 16
    invoke-virtual {v2, v5, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 17
    .line 18
    .line 19
    move-result v5

    .line 20
    sget v6, Lm31/g;->SportsCollectionSimple_iconColor:I

    .line 21
    .line 22
    invoke-virtual {v2, v6, v4}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 23
    .line 24
    .line 25
    move-result v6

    .line 26
    sget v7, Lm31/g;->SportsCollectionSimple_titleColor:I

    .line 27
    .line 28
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 29
    .line 30
    .line 31
    move-result v7

    .line 32
    sget v8, Lm31/g;->SportsCollectionSimple_iconBackground:I

    .line 33
    .line 34
    invoke-virtual {v2, v8, v4}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 35
    .line 36
    .line 37
    move-result v8

    .line 38
    sget v9, Lm31/g;->SportsCollectionSimple_itemBackground:I

    .line 39
    .line 40
    invoke-virtual {v2, v9, v4}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 41
    .line 42
    .line 43
    move-result v9

    .line 44
    iput v9, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->a:I

    .line 45
    .line 46
    sget v9, Lm31/g;->SportsCollectionSimple_titleBottomMargin:I

    .line 47
    .line 48
    invoke-virtual {v2, v9, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 49
    .line 50
    .line 51
    move-result v9

    .line 52
    sget v10, Lm31/g;->SportsCollectionSimple_titleHorizontalMargin:I

    .line 53
    .line 54
    invoke-virtual {v2, v10, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 55
    .line 56
    .line 57
    move-result v10

    .line 58
    sget v11, Lm31/g;->SportsCollectionSimple_iconTopMargin:I

    .line 59
    .line 60
    invoke-virtual {v2, v11, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 61
    .line 62
    .line 63
    move-result v11

    .line 64
    sget v12, Lm31/g;->SportsCollectionSimple_iconBackgroundWidth:I

    .line 65
    .line 66
    invoke-virtual {v2, v12, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 67
    .line 68
    .line 69
    move-result v12

    .line 70
    sget v13, Lm31/g;->SportsCollectionSimple_iconBackgroundHeight:I

    .line 71
    .line 72
    invoke-virtual {v2, v13, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 73
    .line 74
    .line 75
    move-result v13

    .line 76
    sget v14, Lm31/g;->SportsCollectionSimple_iconHorizontalPadding:I

    .line 77
    .line 78
    invoke-virtual {v2, v14, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 79
    .line 80
    .line 81
    move-result v14

    .line 82
    sget v15, Lm31/g;->SportsCollectionSimple_iconVerticalPadding:I

    .line 83
    .line 84
    invoke-virtual {v2, v15, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 85
    .line 86
    .line 87
    move-result v15

    .line 88
    move/from16 v16, v7

    .line 89
    .line 90
    sget v7, Lm31/g;->SportsCollectionSimple_minTitleHeight:I

    .line 91
    .line 92
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 93
    .line 94
    .line 95
    move-result v7

    .line 96
    sget v4, Lm31/g;->SportsCollectionSimple_iconShimmerCornerRadius:I

    .line 97
    .line 98
    move/from16 v17, v7

    .line 99
    .line 100
    const/4 v7, 0x0

    .line 101
    invoke-virtual {v2, v4, v7}, Landroid/content/res/TypedArray;->getDimension(IF)F

    .line 102
    .line 103
    .line 104
    move-result v4

    .line 105
    sget v7, Lm31/g;->SportsCollectionSimple_iconShimmerWidth:I

    .line 106
    .line 107
    move/from16 v19, v4

    .line 108
    .line 109
    const/4 v4, 0x0

    .line 110
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 111
    .line 112
    .line 113
    move-result v7

    .line 114
    move/from16 v20, v7

    .line 115
    .line 116
    sget v7, Lm31/g;->SportsCollectionSimple_iconShimmerHeight:I

    .line 117
    .line 118
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 119
    .line 120
    .line 121
    move-result v7

    .line 122
    sget v4, Lm31/g;->SportsCollectionSimple_titleShimmerCornerRadius:I

    .line 123
    .line 124
    move/from16 v21, v7

    .line 125
    .line 126
    const/4 v7, 0x0

    .line 127
    invoke-virtual {v2, v4, v7}, Landroid/content/res/TypedArray;->getDimension(IF)F

    .line 128
    .line 129
    .line 130
    move-result v4

    .line 131
    sget v7, Lm31/g;->SportsCollectionSimple_titleShimmerWidth:I

    .line 132
    .line 133
    move/from16 v18, v4

    .line 134
    .line 135
    const/4 v4, 0x0

    .line 136
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 137
    .line 138
    .line 139
    move-result v7

    .line 140
    move/from16 v22, v7

    .line 141
    .line 142
    sget v7, Lm31/g;->SportsCollectionSimple_titleShimmerHeight:I

    .line 143
    .line 144
    invoke-virtual {v2, v7, v4}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 145
    .line 146
    .line 147
    move-result v7

    .line 148
    new-instance v4, Landroid/widget/FrameLayout$LayoutParams;

    .line 149
    .line 150
    invoke-direct {v4, v12, v13}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 151
    .line 152
    .line 153
    const/16 v12, 0x31

    .line 154
    .line 155
    iput v12, v4, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 156
    .line 157
    iput v11, v4, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 158
    .line 159
    new-instance v11, Landroid/widget/FrameLayout$LayoutParams;

    .line 160
    .line 161
    const/4 v12, -0x1

    .line 162
    const/4 v13, -0x2

    .line 163
    invoke-direct {v11, v12, v13}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 164
    .line 165
    .line 166
    const/16 v12, 0x50

    .line 167
    .line 168
    iput v12, v11, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 169
    .line 170
    const/4 v12, 0x0

    .line 171
    invoke-virtual {v11, v10, v12, v10, v9}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 172
    .line 173
    .line 174
    sget v10, Lm31/g;->SportsCollectionSimple_minViewItemTextSize:I

    .line 175
    .line 176
    iget v12, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 177
    .line 178
    invoke-virtual {v2, v10, v12}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 179
    .line 180
    .line 181
    move-result v10

    .line 182
    iput v10, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->f:I

    .line 183
    .line 184
    sget v10, Lm31/g;->SportsCollectionSimple_maxViewItemTextSize:I

    .line 185
    .line 186
    iget v12, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 187
    .line 188
    invoke-virtual {v2, v10, v12}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 189
    .line 190
    .line 191
    move-result v2

    .line 192
    iput v2, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 193
    .line 194
    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 195
    .line 196
    invoke-direct {v2, v3, v5}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 197
    .line 198
    .line 199
    invoke-virtual {v0, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 200
    .line 201
    .line 202
    iget-object v2, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->b:Landroid/widget/ImageView;

    .line 203
    .line 204
    sget-object v3, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    .line 205
    .line 206
    invoke-virtual {v2, v3}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 207
    .line 208
    .line 209
    invoke-virtual {v2, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 210
    .line 211
    .line 212
    invoke-virtual {v2, v6}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 213
    .line 214
    .line 215
    invoke-virtual {v2, v14, v15, v14, v15}, Landroid/view/View;->setPadding(IIII)V

    .line 216
    .line 217
    .line 218
    if-eqz v8, :cond_0

    .line 219
    .line 220
    invoke-static {v1, v8}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 221
    .line 222
    .line 223
    move-result-object v3

    .line 224
    invoke-virtual {v2, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 225
    .line 226
    .line 227
    :cond_0
    iget-object v2, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 228
    .line 229
    sget v3, LlZ0/n;->TextStyle_Caption_Medium_M_TextPrimary:I

    .line 230
    .line 231
    invoke-static {v2, v3}, Lorg/xbet/uikit/utils/M;->a(Landroid/widget/TextView;I)V

    .line 232
    .line 233
    .line 234
    const/4 v3, 0x2

    .line 235
    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 236
    .line 237
    .line 238
    sget-object v4, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 239
    .line 240
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 241
    .line 242
    .line 243
    const/16 v4, 0x11

    .line 244
    .line 245
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 246
    .line 247
    .line 248
    const/4 v4, 0x3

    .line 249
    invoke-virtual {v2, v4}, Landroid/view/View;->setLayoutDirection(I)V

    .line 250
    .line 251
    .line 252
    invoke-virtual {v2, v4}, Landroid/view/View;->setTextDirection(I)V

    .line 253
    .line 254
    .line 255
    invoke-virtual {v2, v11}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 256
    .line 257
    .line 258
    move/from16 v4, v16

    .line 259
    .line 260
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 261
    .line 262
    .line 263
    move/from16 v4, v17

    .line 264
    .line 265
    invoke-virtual {v2, v4}, Landroid/view/View;->setMinimumHeight(I)V

    .line 266
    .line 267
    .line 268
    iget v4, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 269
    .line 270
    iget v5, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->f:I

    .line 271
    .line 272
    if-ne v4, v5, :cond_1

    .line 273
    .line 274
    int-to-float v4, v4

    .line 275
    const/4 v12, 0x0

    .line 276
    invoke-virtual {v2, v12, v4}, Landroid/widget/TextView;->setTextSize(IF)V

    .line 277
    .line 278
    .line 279
    goto :goto_0

    .line 280
    :cond_1
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e()V

    .line 281
    .line 282
    .line 283
    :goto_0
    iget-object v2, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 284
    .line 285
    const/16 v4, 0x8

    .line 286
    .line 287
    invoke-virtual {v2, v4}, Landroid/view/View;->setVisibility(I)V

    .line 288
    .line 289
    .line 290
    new-instance v5, Landroid/widget/FrameLayout$LayoutParams;

    .line 291
    .line 292
    move/from16 v6, v20

    .line 293
    .line 294
    move/from16 v8, v21

    .line 295
    .line 296
    invoke-direct {v5, v6, v8}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 297
    .line 298
    .line 299
    invoke-virtual {v2, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 300
    .line 301
    .line 302
    new-instance v5, Landroid/graphics/drawable/GradientDrawable;

    .line 303
    .line 304
    invoke-direct {v5}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 305
    .line 306
    .line 307
    move/from16 v6, v19

    .line 308
    .line 309
    invoke-virtual {v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 310
    .line 311
    .line 312
    sget v6, LlZ0/d;->uikitSecondary20:I

    .line 313
    .line 314
    const/4 v8, 0x0

    .line 315
    invoke-static {v1, v6, v8, v3, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 316
    .line 317
    .line 318
    move-result v6

    .line 319
    invoke-static {v6}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 320
    .line 321
    .line 322
    move-result-object v6

    .line 323
    invoke-virtual {v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 324
    .line 325
    .line 326
    invoke-virtual {v2, v5}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 327
    .line 328
    .line 329
    iget-object v2, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->d:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 330
    .line 331
    invoke-virtual {v2, v4}, Landroid/view/View;->setVisibility(I)V

    .line 332
    .line 333
    .line 334
    new-instance v4, Landroid/widget/FrameLayout$LayoutParams;

    .line 335
    .line 336
    move/from16 v5, v22

    .line 337
    .line 338
    invoke-direct {v4, v5, v7}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 339
    .line 340
    .line 341
    const/16 v5, 0x51

    .line 342
    .line 343
    iput v5, v4, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 344
    .line 345
    const/4 v12, 0x0

    .line 346
    invoke-virtual {v4, v12, v12, v12, v9}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 347
    .line 348
    .line 349
    invoke-virtual {v2, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 350
    .line 351
    .line 352
    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    .line 353
    .line 354
    invoke-direct {v4}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 355
    .line 356
    .line 357
    move/from16 v5, v18

    .line 358
    .line 359
    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 360
    .line 361
    .line 362
    sget v5, LlZ0/d;->uikitSecondary20:I

    .line 363
    .line 364
    invoke-static {v1, v5, v8, v3, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 365
    .line 366
    .line 367
    move-result v1

    .line 368
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 369
    .line 370
    .line 371
    move-result-object v1

    .line 372
    invoke-virtual {v4, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 373
    .line 374
    .line 375
    invoke-virtual {v2, v4}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 376
    .line 377
    .line 378
    iget v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->a:I

    .line 379
    .line 380
    if-eqz v1, :cond_2

    .line 381
    .line 382
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 383
    .line 384
    .line 385
    :cond_2
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 386
    .line 387
    return-object v0
.end method

.method public static final d(Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;)Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method private final getIconLoadHelper()Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->h:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/z;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final e()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->f:I

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->g:I

    .line 6
    .line 7
    const/4 v3, 0x1

    .line 8
    const/4 v4, 0x0

    .line 9
    invoke-static {v0, v1, v2, v3, v4}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public f()V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Landroid/view/View;->setBackgroundResource(I)V

    .line 3
    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->b:Landroid/widget/ImageView;

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public setIcon(LL11/c;)V
    .locals 7
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->getIconLoadHelper()Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/h;->ic_glyph_category_new:I

    .line 6
    .line 7
    invoke-static {v1}, LL11/c$c;->d(I)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-static {v1}, LL11/c$c;->c(I)LL11/c$c;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    const/16 v5, 0xc

    .line 16
    .line 17
    const/4 v6, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    move-object v1, p1

    .line 21
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/utils/z;->y(Lorg/xbet/uikit/utils/z;LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public setTitle(Ljava/lang/String;)V
    .locals 2

    .line 1
    invoke-virtual {p0, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 5
    .line 6
    if-eqz p1, :cond_1

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    if-nez v1, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/16 v1, 0x8

    .line 18
    .line 19
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->e:Landroid/widget/TextView;

    .line 23
    .line 24
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method
