.class public final synthetic LsS0/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LsS0/l;


# direct methods
.method public synthetic constructor <init>(LsS0/l;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LsS0/k;->a:LsS0/l;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LsS0/k;->a:LsS0/l;

    invoke-static {v0}, LsS0/l;->a(LsS0/l;)Lorg/xbet/swipex/impl/data/a;

    move-result-object v0

    return-object v0
.end method
