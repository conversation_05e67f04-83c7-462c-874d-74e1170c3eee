.class public final LIN0/D;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\u001a\u0015\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u001a\u0015\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LNN0/a;",
        "backgroundUiModel",
        "",
        "b",
        "(LNN0/a;)Ljava/lang/String;",
        "Lt0/i;",
        "availableHeight",
        "",
        "a",
        "(F)I",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(F)I
    .locals 8

    .line 1
    sget-object v0, LA11/a;->a:LA11/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LA11/a;->K0()F

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {v0}, LA11/a;->s0()F

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-virtual {v0}, LA11/a;->q1()F

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    add-float/2addr v2, v3

    .line 16
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    invoke-virtual {v0}, LA11/a;->Y()F

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    invoke-virtual {v0}, LA11/a;->q1()F

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    add-float/2addr v3, v4

    .line 29
    invoke-static {v3}, Lt0/i;->k(F)F

    .line 30
    .line 31
    .line 32
    move-result v3

    .line 33
    invoke-virtual {v0}, LA11/a;->K0()F

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    invoke-virtual {v0}, LA11/a;->q1()F

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    add-float/2addr v4, v0

    .line 42
    invoke-static {v4}, Lt0/i;->k(F)F

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    invoke-static {v1}, Lt0/i;->g(F)Lt0/i;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-static {v2}, Lt0/i;->g(F)Lt0/i;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    invoke-static {v3}, Lt0/i;->g(F)Lt0/i;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    const/4 v4, 0x3

    .line 59
    new-array v4, v4, [Lt0/i;

    .line 60
    .line 61
    const/4 v5, 0x0

    .line 62
    aput-object v1, v4, v5

    .line 63
    .line 64
    const/4 v1, 0x1

    .line 65
    aput-object v2, v4, v1

    .line 66
    .line 67
    const/4 v2, 0x2

    .line 68
    aput-object v3, v4, v2

    .line 69
    .line 70
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 75
    .line 76
    .line 77
    move-result v3

    .line 78
    sub-int/2addr v3, v1

    .line 79
    const/4 v4, 0x0

    .line 80
    :goto_0
    if-ge v5, v3, :cond_1

    .line 81
    .line 82
    invoke-interface {v2, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object v6

    .line 86
    check-cast v6, Lt0/i;

    .line 87
    .line 88
    invoke-virtual {v6}, Lt0/i;->q()F

    .line 89
    .line 90
    .line 91
    move-result v6

    .line 92
    sub-float v6, p0, v6

    .line 93
    .line 94
    invoke-static {v6}, Lt0/i;->k(F)F

    .line 95
    .line 96
    .line 97
    move-result v6

    .line 98
    sget-object v7, LA11/a;->a:LA11/a;

    .line 99
    .line 100
    invoke-virtual {v7}, LA11/a;->y()F

    .line 101
    .line 102
    .line 103
    move-result v7

    .line 104
    invoke-static {v6, v7}, Lt0/i;->i(FF)I

    .line 105
    .line 106
    .line 107
    move-result v6

    .line 108
    if-ltz v6, :cond_0

    .line 109
    .line 110
    invoke-interface {v2, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v6

    .line 114
    check-cast v6, Lt0/i;

    .line 115
    .line 116
    invoke-virtual {v6}, Lt0/i;->q()F

    .line 117
    .line 118
    .line 119
    move-result v6

    .line 120
    sub-float/2addr p0, v6

    .line 121
    invoke-static {p0}, Lt0/i;->k(F)F

    .line 122
    .line 123
    .line 124
    move-result p0

    .line 125
    add-int/2addr v4, v1

    .line 126
    add-int/2addr v5, v1

    .line 127
    goto :goto_0

    .line 128
    :cond_0
    return v4

    .line 129
    :cond_1
    :goto_1
    sub-float/2addr p0, v0

    .line 130
    invoke-static {p0}, Lt0/i;->k(F)F

    .line 131
    .line 132
    .line 133
    move-result v2

    .line 134
    sget-object v3, LA11/a;->a:LA11/a;

    .line 135
    .line 136
    invoke-virtual {v3}, LA11/a;->y()F

    .line 137
    .line 138
    .line 139
    move-result v3

    .line 140
    invoke-static {v2, v3}, Lt0/i;->i(FF)I

    .line 141
    .line 142
    .line 143
    move-result v2

    .line 144
    if-ltz v2, :cond_2

    .line 145
    .line 146
    invoke-static {p0}, Lt0/i;->k(F)F

    .line 147
    .line 148
    .line 149
    move-result p0

    .line 150
    add-int/2addr v4, v1

    .line 151
    goto :goto_1

    .line 152
    :cond_2
    return v4
.end method

.method public static final b(LNN0/a;)Ljava/lang/String;
    .locals 3
    .param p0    # LNN0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNN0/a;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-lez v1, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-nez v0, :cond_1

    .line 14
    .line 15
    sget-object v0, LDX0/c;->a:LDX0/c;

    .line 16
    .line 17
    invoke-virtual {p0}, LNN0/a;->e()J

    .line 18
    .line 19
    .line 20
    move-result-wide v1

    .line 21
    invoke-virtual {p0}, LNN0/a;->d()Z

    .line 22
    .line 23
    .line 24
    move-result p0

    .line 25
    invoke-virtual {v0, v1, v2, p0}, LDX0/c;->c(JZ)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object p0

    .line 29
    return-object p0

    .line 30
    :cond_1
    return-object v0
.end method
