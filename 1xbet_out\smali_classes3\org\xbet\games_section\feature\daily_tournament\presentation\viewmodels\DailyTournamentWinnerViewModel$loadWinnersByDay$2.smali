.class final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.daily_tournament.presentation.viewmodels.DailyTournamentWinnerViewModel$loadWinnersByDay$2"
    f = "DailyTournamentWinnerViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->F3(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $date:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->$date:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->$date:Ljava/lang/String;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->v3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->$date:Ljava/lang/String;

    .line 18
    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;->a(Ljava/lang/String;)Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    .line 24
    .line 25
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    if-eqz v1, :cond_0

    .line 30
    .line 31
    new-instance p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;

    .line 32
    .line 33
    invoke-static {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-direct {p1, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 38
    .line 39
    .line 40
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->y3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;

    .line 45
    .line 46
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;-><init>(Ljava/util/List;)V

    .line 47
    .line 48
    .line 49
    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->y3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V

    .line 50
    .line 51
    .line 52
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 53
    .line 54
    return-object p1

    .line 55
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 56
    .line 57
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 58
    .line 59
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    throw p1
.end method
