.class public final LmK0/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmK0/a$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "Ljava/lang/String;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\u0008\u0000\u0018\u0000 \u00052\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0006B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0007"
    }
    d2 = {
        "LmK0/a;",
        "LA4/e;",
        "",
        "<init>",
        "()V",
        "f",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LmK0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LmK0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LmK0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LmK0/a;->f:LmK0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    sget-object v0, LmK0/a;->f:LmK0/a$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    invoke-static {}, Lorg/xbet/statistic/player/impl/player/winter_full_description/presentation/adapter/delegate/FullDescriptionDelegateKt;->d()LA4/c;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 13
    .line 14
    .line 15
    return-void
.end method
