.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/w;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/w;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    check-cast p1, Ljava/lang/Throwable;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->r3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
