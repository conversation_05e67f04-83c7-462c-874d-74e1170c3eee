.class public final Lorg/xbet/games_section/feature/popular/presentation/b;
.super Lorg/xbet/ui_common/viewmodel/core/k;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular/presentation/b$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u0000 22\u00020\u0001:\u00013B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007JY\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u00172\u0012\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u00082\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0000\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ9\u0010\u001e\u001a\u00020\u001d*\u0008\u0012\u0004\u0012\u00020\u00180\u001b2\u0006\u0010\u0010\u001a\u00020\u001c2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ%\u0010!\u001a\u0008\u0012\u0004\u0012\u00020 0\u00172\u0006\u0010\u0010\u001a\u00020\u001c2\u0006\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u001d\u0010%\u001a\u00020\u000c2\u000c\u0010$\u001a\u0008\u0012\u0004\u0012\u00020#0\u0017H\u0002\u00a2\u0006\u0004\u0008%\u0010&J/\u0010*\u001a\u00020\u001d*\u0008\u0012\u0004\u0012\u00020\u00180\u001b2\u000c\u0010\'\u001a\u0008\u0012\u0004\u0012\u00020 0\u00172\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008*\u0010+R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u00101\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100\u00a8\u00064"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular/presentation/b;",
        "Lorg/xbet/ui_common/viewmodel/core/k;",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "Lkotlinx/coroutines/flow/V;",
        "Lorg/xbet/games_section/feature/popular/presentation/a;",
        "Lb50/e;",
        "bannersFlow",
        "",
        "showBanners",
        "bannersUiModel",
        "Lb50/i;",
        "oneXGamesUiModel",
        "Lb50/a;",
        "centerOfAttentionUiModel",
        "Lb50/c;",
        "jackpotInfoUiModel",
        "Lb50/d;",
        "luckyWheelUiModel",
        "",
        "LVX0/i;",
        "l",
        "(Lkotlinx/coroutines/flow/V;ZLb50/e;Lb50/i;Lb50/a;Lb50/c;Lb50/d;)Ljava/util/List;",
        "",
        "Lb50/i$a;",
        "",
        "o",
        "(Ljava/util/List;Lb50/i$a;Lb50/a;Lb50/c;Lb50/d;)V",
        "Lb50/h;",
        "n",
        "(Lb50/i$a;Lb50/a;)Ljava/util/List;",
        "Ls30/b;",
        "oneXGamesWithCategoryList",
        "p",
        "(Ljava/util/List;)Z",
        "oneXGamesDataList",
        "Lorg/xbet/core/domain/GamesCategoryTypeEnum;",
        "categoryType",
        "k",
        "(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V",
        "d",
        "LHX0/e;",
        "Lek0/o;",
        "e",
        "Lek0/o;",
        "remoteConfig",
        "f",
        "a",
        "popular_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:Lorg/xbet/games_section/feature/popular/presentation/b$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final d:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/b$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/b$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/popular/presentation/b;->f:Lorg/xbet/games_section/feature/popular/presentation/b$a;

    return-void
.end method

.method public constructor <init>(LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 0
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/k;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/b;->d:LHX0/e;

    .line 5
    .line 6
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/b;->e:Lek0/o;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lb50/h;",
            ">;",
            "Lorg/xbet/core/domain/GamesCategoryTypeEnum;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    move-object v1, v0

    .line 16
    check-cast v1, Lb50/h;

    .line 17
    .line 18
    invoke-virtual {v1}, Lb50/h;->d()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {p3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v0, 0x0

    .line 34
    :goto_0
    check-cast v0, Lb50/h;

    .line 35
    .line 36
    if-eqz v0, :cond_2

    .line 37
    .line 38
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    :cond_2
    return-void
.end method

.method public final l(Lkotlinx/coroutines/flow/V;ZLb50/e;Lb50/i;Lb50/a;Lb50/c;Lb50/d;)Ljava/util/List;
    .locals 5
    .param p1    # Lkotlinx/coroutines/flow/V;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lb50/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lb50/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lb50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lb50/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lb50/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/e;",
            ">;>;Z",
            "Lb50/e;",
            "Lb50/i;",
            "Lb50/a;",
            "Lb50/c;",
            "Lb50/d;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move v0, p2

    .line 2
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 3
    .line 4
    .line 5
    move-result-object p2

    .line 6
    instance-of v1, p3, Lb50/e$a;

    .line 7
    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 11
    .line 12
    .line 13
    goto :goto_1

    .line 14
    :cond_0
    instance-of v1, p3, Lb50/e$b;

    .line 15
    .line 16
    if-eqz v1, :cond_2

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    sget-object p3, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;->Companion:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/b;->e:Lek0/o;

    .line 23
    .line 24
    invoke-virtual {v0}, Lek0/o;->P1()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    invoke-virtual {p3, v0}, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 29
    .line 30
    .line 31
    move-result-object p3

    .line 32
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 33
    .line 34
    new-instance v1, Lb50/e$a;

    .line 35
    .line 36
    new-instance v2, Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 37
    .line 38
    new-instance v3, LAZ0/c;

    .line 39
    .line 40
    sget-object v4, LAZ0/c;->c:LAZ0/c$a;

    .line 41
    .line 42
    invoke-virtual {v4, p3}, LAZ0/c$a;->a(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)I

    .line 43
    .line 44
    .line 45
    move-result v4

    .line 46
    invoke-direct {v3, p3, v4}, LAZ0/c;-><init>(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;I)V

    .line 47
    .line 48
    .line 49
    invoke-direct {v2, v3}, Lorg/xbet/uikit/components/bannercollection/a$b;-><init>(LAZ0/c;)V

    .line 50
    .line 51
    .line 52
    invoke-direct {v1, v2}, Lb50/e$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 53
    .line 54
    .line 55
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/a$d;-><init>(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    goto :goto_0

    .line 59
    :cond_1
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 60
    .line 61
    :goto_0
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_2
    instance-of p1, p3, Lb50/e$c;

    .line 66
    .line 67
    if-eqz p1, :cond_6

    .line 68
    .line 69
    :goto_1
    instance-of p1, p4, Lb50/i$a;

    .line 70
    .line 71
    if-eqz p1, :cond_4

    .line 72
    .line 73
    instance-of p1, p6, Lb50/c$b;

    .line 74
    .line 75
    if-nez p1, :cond_3

    .line 76
    .line 77
    move-object p3, p4

    .line 78
    check-cast p3, Lb50/i$a;

    .line 79
    .line 80
    move-object p1, p0

    .line 81
    move-object p4, p5

    .line 82
    move-object p5, p6

    .line 83
    move-object p6, p7

    .line 84
    invoke-virtual/range {p1 .. p6}, Lorg/xbet/games_section/feature/popular/presentation/b;->o(Ljava/util/List;Lb50/i$a;Lb50/a;Lb50/c;Lb50/d;)V

    .line 85
    .line 86
    .line 87
    goto :goto_2

    .line 88
    :cond_3
    new-instance p1, Lb50/b;

    .line 89
    .line 90
    sget-object p3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 91
    .line 92
    invoke-virtual {p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object p3

    .line 96
    invoke-direct {p1, p3}, Lb50/b;-><init>(Ljava/lang/String;)V

    .line 97
    .line 98
    .line 99
    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 100
    .line 101
    .line 102
    goto :goto_2

    .line 103
    :cond_4
    instance-of p1, p4, Lb50/i$b;

    .line 104
    .line 105
    if-eqz p1, :cond_5

    .line 106
    .line 107
    new-instance p1, Lb50/b;

    .line 108
    .line 109
    sget-object p3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 110
    .line 111
    invoke-virtual {p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 112
    .line 113
    .line 114
    move-result-object p3

    .line 115
    invoke-direct {p1, p3}, Lb50/b;-><init>(Ljava/lang/String;)V

    .line 116
    .line 117
    .line 118
    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 119
    .line 120
    .line 121
    :goto_2
    invoke-static {p2}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    return-object p1

    .line 126
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 127
    .line 128
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 129
    .line 130
    .line 131
    throw p1

    .line 132
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 133
    .line 134
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 135
    .line 136
    .line 137
    throw p1
.end method

.method public final n(Lb50/i$a;Lb50/a;)Ljava/util/List;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lb50/i$a;",
            "Lb50/a;",
            ")",
            "Ljava/util/List<",
            "Lb50/h;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, p2, Lb50/a$a;

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    if-eqz v1, :cond_d

    .line 10
    .line 11
    check-cast p2, Lb50/a$a;

    .line 12
    .line 13
    invoke-virtual {p2}, Lb50/a$a;->a()Ls30/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular/presentation/b;->d:LHX0/e;

    .line 18
    .line 19
    invoke-static {v1, v3}, La50/c;->a(Ls30/a;LHX0/e;)Lb50/h;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    invoke-virtual {p2}, Lb50/a$a;->a()Ls30/a;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {v1}, Ls30/a;->a()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 35
    .line 36
    invoke-virtual {v3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    move-result v3

    .line 44
    if-eqz v3, :cond_1

    .line 45
    .line 46
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    new-instance v1, Ljava/util/ArrayList;

    .line 51
    .line 52
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 53
    .line 54
    .line 55
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-eqz v3, :cond_c

    .line 64
    .line 65
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    move-object v4, v3

    .line 70
    check-cast v4, Ls30/b;

    .line 71
    .line 72
    invoke-virtual {v4}, Ls30/b;->c()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v4

    .line 76
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 77
    .line 78
    invoke-virtual {v5}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v5

    .line 82
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    if-nez v4, :cond_0

    .line 87
    .line 88
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    goto :goto_0

    .line 92
    :cond_1
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 93
    .line 94
    invoke-virtual {v3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v3

    .line 98
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    if-eqz v1, :cond_b

    .line 103
    .line 104
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    :cond_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 113
    .line 114
    .line 115
    move-result v3

    .line 116
    const/4 v4, 0x0

    .line 117
    if-eqz v3, :cond_3

    .line 118
    .line 119
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object v3

    .line 123
    move-object v5, v3

    .line 124
    check-cast v5, Ls30/b;

    .line 125
    .line 126
    invoke-virtual {v5}, Ls30/b;->c()Ljava/lang/String;

    .line 127
    .line 128
    .line 129
    move-result-object v5

    .line 130
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 131
    .line 132
    invoke-virtual {v6}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object v6

    .line 136
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    move-result v5

    .line 140
    if-eqz v5, :cond_2

    .line 141
    .line 142
    goto :goto_1

    .line 143
    :cond_3
    move-object v3, v4

    .line 144
    :goto_1
    check-cast v3, Ls30/b;

    .line 145
    .line 146
    if-eqz v3, :cond_4

    .line 147
    .line 148
    invoke-virtual {v3}, Ls30/b;->e()Ljava/util/List;

    .line 149
    .line 150
    .line 151
    move-result-object v4

    .line 152
    :cond_4
    if-eqz v4, :cond_6

    .line 153
    .line 154
    invoke-interface {v4}, Ljava/util/Collection;->size()I

    .line 155
    .line 156
    .line 157
    move-result v1

    .line 158
    const/4 v3, 0x1

    .line 159
    if-ne v1, v3, :cond_6

    .line 160
    .line 161
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 162
    .line 163
    .line 164
    move-result-object p2

    .line 165
    new-instance v1, Ljava/util/ArrayList;

    .line 166
    .line 167
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 168
    .line 169
    .line 170
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 171
    .line 172
    .line 173
    move-result-object p2

    .line 174
    :cond_5
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 175
    .line 176
    .line 177
    move-result v3

    .line 178
    if-eqz v3, :cond_c

    .line 179
    .line 180
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v3

    .line 184
    move-object v4, v3

    .line 185
    check-cast v4, Ls30/b;

    .line 186
    .line 187
    invoke-virtual {v4}, Ls30/b;->c()Ljava/lang/String;

    .line 188
    .line 189
    .line 190
    move-result-object v4

    .line 191
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 192
    .line 193
    invoke-virtual {v5}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 194
    .line 195
    .line 196
    move-result-object v5

    .line 197
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 198
    .line 199
    .line 200
    move-result v4

    .line 201
    if-nez v4, :cond_5

    .line 202
    .line 203
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    goto :goto_2

    .line 207
    :cond_6
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 208
    .line 209
    .line 210
    move-result-object v1

    .line 211
    new-instance v3, Ljava/util/ArrayList;

    .line 212
    .line 213
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 214
    .line 215
    .line 216
    move-result v4

    .line 217
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 218
    .line 219
    .line 220
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 221
    .line 222
    .line 223
    move-result-object v1

    .line 224
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 225
    .line 226
    .line 227
    move-result v4

    .line 228
    if-eqz v4, :cond_a

    .line 229
    .line 230
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 231
    .line 232
    .line 233
    move-result-object v4

    .line 234
    move-object v5, v4

    .line 235
    check-cast v5, Ls30/b;

    .line 236
    .line 237
    invoke-virtual {v5}, Ls30/b;->c()Ljava/lang/String;

    .line 238
    .line 239
    .line 240
    move-result-object v4

    .line 241
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 242
    .line 243
    invoke-virtual {v6}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 244
    .line 245
    .line 246
    move-result-object v6

    .line 247
    invoke-static {v4, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 248
    .line 249
    .line 250
    move-result v4

    .line 251
    if-eqz v4, :cond_9

    .line 252
    .line 253
    invoke-virtual {v5}, Ls30/b;->e()Ljava/util/List;

    .line 254
    .line 255
    .line 256
    move-result-object v4

    .line 257
    new-instance v6, Ljava/util/ArrayList;

    .line 258
    .line 259
    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 260
    .line 261
    .line 262
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 263
    .line 264
    .line 265
    move-result-object v4

    .line 266
    :cond_7
    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 267
    .line 268
    .line 269
    move-result v7

    .line 270
    if-eqz v7, :cond_8

    .line 271
    .line 272
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v7

    .line 276
    move-object v8, v7

    .line 277
    check-cast v8, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 278
    .line 279
    invoke-virtual {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 280
    .line 281
    .line 282
    move-result-object v8

    .line 283
    invoke-static {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 284
    .line 285
    .line 286
    move-result-wide v8

    .line 287
    invoke-virtual {p2}, Lb50/a$a;->a()Ls30/a;

    .line 288
    .line 289
    .line 290
    move-result-object v10

    .line 291
    invoke-virtual {v10}, Ls30/a;->b()Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 292
    .line 293
    .line 294
    move-result-object v10

    .line 295
    invoke-virtual {v10}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 296
    .line 297
    .line 298
    move-result-object v10

    .line 299
    invoke-static {v10}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 300
    .line 301
    .line 302
    move-result-wide v10

    .line 303
    cmp-long v12, v8, v10

    .line 304
    .line 305
    if-eqz v12, :cond_7

    .line 306
    .line 307
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 308
    .line 309
    .line 310
    goto :goto_4

    .line 311
    :cond_8
    const/4 v9, 0x6

    .line 312
    const/4 v10, 0x0

    .line 313
    const/4 v7, 0x0

    .line 314
    const/4 v8, 0x0

    .line 315
    invoke-static/range {v5 .. v10}, Ls30/b;->b(Ls30/b;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ls30/b;

    .line 316
    .line 317
    .line 318
    move-result-object v5

    .line 319
    :cond_9
    invoke-interface {v3, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 320
    .line 321
    .line 322
    goto :goto_3

    .line 323
    :cond_a
    move-object v1, v3

    .line 324
    goto :goto_5

    .line 325
    :cond_b
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 326
    .line 327
    .line 328
    move-result-object v1

    .line 329
    :cond_c
    :goto_5
    invoke-virtual {p1, v1}, Lb50/i$a;->a(Ljava/util/List;)Lb50/i$a;

    .line 330
    .line 331
    .line 332
    move-result-object p1

    .line 333
    :cond_d
    invoke-virtual {p1}, Lb50/i$a;->b()Ljava/util/List;

    .line 334
    .line 335
    .line 336
    move-result-object p1

    .line 337
    new-instance p2, Ljava/util/ArrayList;

    .line 338
    .line 339
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 340
    .line 341
    .line 342
    move-result v1

    .line 343
    invoke-direct {p2, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 344
    .line 345
    .line 346
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 347
    .line 348
    .line 349
    move-result-object p1

    .line 350
    :goto_6
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 351
    .line 352
    .line 353
    move-result v1

    .line 354
    if-eqz v1, :cond_e

    .line 355
    .line 356
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 357
    .line 358
    .line 359
    move-result-object v1

    .line 360
    check-cast v1, Ls30/b;

    .line 361
    .line 362
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/b;->d:LHX0/e;

    .line 363
    .line 364
    sget-object v3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 365
    .line 366
    invoke-virtual {v3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 367
    .line 368
    .line 369
    move-result-object v3

    .line 370
    invoke-static {v1, v2, v3}, La50/d;->a(Ls30/b;LHX0/e;Ljava/lang/String;)Lb50/h;

    .line 371
    .line 372
    .line 373
    move-result-object v1

    .line 374
    invoke-interface {p2, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 375
    .line 376
    .line 377
    goto :goto_6

    .line 378
    :cond_e
    invoke-interface {v0, p2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 379
    .line 380
    .line 381
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 382
    .line 383
    .line 384
    move-result-object p1

    .line 385
    return-object p1
.end method

.method public final o(Ljava/util/List;Lb50/i$a;Lb50/a;Lb50/c;Lb50/d;)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Lb50/i$a;",
            "Lb50/a;",
            "Lb50/c;",
            "Lb50/d;",
            ")V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p4

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p5

    .line 12
    .line 13
    invoke-virtual {v0, v3, v4}, Lorg/xbet/games_section/feature/popular/presentation/b;->n(Lb50/i$a;Lb50/a;)Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 18
    .line 19
    const/4 v7, 0x5

    .line 20
    new-array v7, v7, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 21
    .line 22
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 23
    .line 24
    const/4 v9, 0x0

    .line 25
    aput-object v8, v7, v9

    .line 26
    .line 27
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->FOR_YOU:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 28
    .line 29
    const/4 v10, 0x1

    .line 30
    aput-object v8, v7, v10

    .line 31
    .line 32
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->BEST:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 33
    .line 34
    const/4 v11, 0x2

    .line 35
    aput-object v8, v7, v11

    .line 36
    .line 37
    const/4 v8, 0x3

    .line 38
    aput-object v6, v7, v8

    .line 39
    .line 40
    sget-object v12, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->SLOTS:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 41
    .line 42
    const/4 v13, 0x4

    .line 43
    aput-object v12, v7, v13

    .line 44
    .line 45
    invoke-static {v7}, Lkotlin/collections/v;->t([Ljava/lang/Object;)Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v7

    .line 49
    invoke-virtual {v3}, Lb50/i$a;->b()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v3

    .line 53
    invoke-virtual {v0, v3}, Lorg/xbet/games_section/feature/popular/presentation/b;->p(Ljava/util/List;)Z

    .line 54
    .line 55
    .line 56
    move-result v3

    .line 57
    if-nez v3, :cond_0

    .line 58
    .line 59
    invoke-interface {v7, v6}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    const/4 v6, -0x1

    .line 64
    if-eq v3, v6, :cond_0

    .line 65
    .line 66
    invoke-interface {v7, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    :cond_0
    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 70
    .line 71
    .line 72
    move-result-object v3

    .line 73
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 74
    .line 75
    .line 76
    move-result v6

    .line 77
    if-eqz v6, :cond_1

    .line 78
    .line 79
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v6

    .line 83
    check-cast v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 84
    .line 85
    invoke-virtual {v0, v1, v4, v6}, Lorg/xbet/games_section/feature/popular/presentation/b;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_1
    instance-of v3, v5, Lb50/d$a;

    .line 90
    .line 91
    if-eqz v3, :cond_2

    .line 92
    .line 93
    move-object v3, v5

    .line 94
    check-cast v3, Lb50/d$a;

    .line 95
    .line 96
    invoke-virtual {v3}, Lb50/d$a;->a()Lb50/f;

    .line 97
    .line 98
    .line 99
    move-result-object v3

    .line 100
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 101
    .line 102
    .line 103
    :cond_2
    new-array v3, v11, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 104
    .line 105
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->STAIRS:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 106
    .line 107
    aput-object v5, v3, v9

    .line 108
    .line 109
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->DICES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 110
    .line 111
    aput-object v5, v3, v10

    .line 112
    .line 113
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 114
    .line 115
    .line 116
    move-result-object v3

    .line 117
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 118
    .line 119
    .line 120
    move-result-object v3

    .line 121
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 122
    .line 123
    .line 124
    move-result v5

    .line 125
    if-eqz v5, :cond_3

    .line 126
    .line 127
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object v5

    .line 131
    check-cast v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 132
    .line 133
    invoke-virtual {v0, v1, v4, v5}, Lorg/xbet/games_section/feature/popular/presentation/b;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 134
    .line 135
    .line 136
    goto :goto_1

    .line 137
    :cond_3
    instance-of v3, v2, Lb50/c$a;

    .line 138
    .line 139
    if-eqz v3, :cond_4

    .line 140
    .line 141
    new-instance v12, Lb50/f;

    .line 142
    .line 143
    sget-object v3, Ll8/j;->a:Ll8/j;

    .line 144
    .line 145
    check-cast v2, Lb50/c$a;

    .line 146
    .line 147
    invoke-virtual {v2}, Lb50/c$a;->d()LP40/b;

    .line 148
    .line 149
    .line 150
    move-result-object v5

    .line 151
    invoke-virtual {v5}, LP40/b;->b()LP40/c;

    .line 152
    .line 153
    .line 154
    move-result-object v5

    .line 155
    invoke-virtual {v5}, LP40/c;->c()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v5

    .line 159
    invoke-static {v5}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 160
    .line 161
    .line 162
    move-result-wide v5

    .line 163
    invoke-virtual {v2}, Lb50/c$a;->d()LP40/b;

    .line 164
    .line 165
    .line 166
    move-result-object v2

    .line 167
    invoke-virtual {v2}, LP40/b;->a()Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v2

    .line 171
    sget-object v7, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 172
    .line 173
    invoke-virtual {v3, v5, v6, v2, v7}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v14

    .line 177
    iget-object v2, v0, Lorg/xbet/games_section/feature/popular/presentation/b;->d:LHX0/e;

    .line 178
    .line 179
    sget v3, Lpb/k;->promo_jackpot:I

    .line 180
    .line 181
    new-array v5, v9, [Ljava/lang/Object;

    .line 182
    .line 183
    invoke-interface {v2, v3, v5}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v15

    .line 187
    sget v16, LT40/a;->jackpot_popular_banner:I

    .line 188
    .line 189
    const/16 v17, 0x0

    .line 190
    .line 191
    const/4 v13, 0x2

    .line 192
    invoke-direct/range {v12 .. v17}, Lb50/f;-><init>(ILjava/lang/String;Ljava/lang/String;IZ)V

    .line 193
    .line 194
    .line 195
    invoke-interface {v1, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 196
    .line 197
    .line 198
    goto :goto_2

    .line 199
    :cond_4
    instance-of v3, v2, Lb50/c$b;

    .line 200
    .line 201
    if-nez v3, :cond_6

    .line 202
    .line 203
    sget-object v3, Lb50/c$c;->a:Lb50/c$c;

    .line 204
    .line 205
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 206
    .line 207
    .line 208
    move-result v2

    .line 209
    if-eqz v2, :cond_5

    .line 210
    .line 211
    goto :goto_2

    .line 212
    :cond_5
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 213
    .line 214
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 215
    .line 216
    .line 217
    throw v1

    .line 218
    :cond_6
    :goto_2
    new-array v2, v8, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 219
    .line 220
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CARD_GAMES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 221
    .line 222
    aput-object v3, v2, v9

    .line 223
    .line 224
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->LOTTERIES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 225
    .line 226
    aput-object v3, v2, v10

    .line 227
    .line 228
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->OTHER:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 229
    .line 230
    aput-object v3, v2, v11

    .line 231
    .line 232
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 233
    .line 234
    .line 235
    move-result-object v2

    .line 236
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 237
    .line 238
    .line 239
    move-result-object v2

    .line 240
    :goto_3
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 241
    .line 242
    .line 243
    move-result v3

    .line 244
    if-eqz v3, :cond_7

    .line 245
    .line 246
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 247
    .line 248
    .line 249
    move-result-object v3

    .line 250
    check-cast v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 251
    .line 252
    invoke-virtual {v0, v1, v4, v3}, Lorg/xbet/games_section/feature/popular/presentation/b;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 253
    .line 254
    .line 255
    goto :goto_3

    .line 256
    :cond_7
    return-void
.end method

.method public final p(Ljava/util/List;)Z
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ls30/b;",
            ">;)Z"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v1, :cond_1

    .line 11
    .line 12
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    move-object v3, v1

    .line 17
    check-cast v3, Ls30/b;

    .line 18
    .line 19
    invoke-virtual {v3}, Ls30/b;->c()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 24
    .line 25
    invoke-virtual {v4}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v3

    .line 33
    if-eqz v3, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_1
    move-object v1, v2

    .line 37
    :goto_0
    check-cast v1, Ls30/b;

    .line 38
    .line 39
    if-eqz v1, :cond_2

    .line 40
    .line 41
    invoke-virtual {v1}, Ls30/b;->e()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    if-eqz v0, :cond_2

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    check-cast v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 52
    .line 53
    if-eqz v0, :cond_2

    .line 54
    .line 55
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    if-eqz v0, :cond_2

    .line 60
    .line 61
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 62
    .line 63
    .line 64
    move-result-wide v0

    .line 65
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    goto :goto_1

    .line 70
    :cond_2
    move-object v0, v2

    .line 71
    :goto_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    if-eqz v1, :cond_4

    .line 80
    .line 81
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    move-object v3, v1

    .line 86
    check-cast v3, Ls30/b;

    .line 87
    .line 88
    invoke-virtual {v3}, Ls30/b;->c()Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object v3

    .line 92
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 93
    .line 94
    invoke-virtual {v4}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v4

    .line 98
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    move-result v3

    .line 102
    if-eqz v3, :cond_3

    .line 103
    .line 104
    goto :goto_2

    .line 105
    :cond_4
    move-object v1, v2

    .line 106
    :goto_2
    check-cast v1, Ls30/b;

    .line 107
    .line 108
    const/4 p1, 0x0

    .line 109
    if-eqz v1, :cond_a

    .line 110
    .line 111
    invoke-virtual {v1}, Ls30/b;->e()Ljava/util/List;

    .line 112
    .line 113
    .line 114
    move-result-object v3

    .line 115
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 116
    .line 117
    .line 118
    move-result-object v3

    .line 119
    :cond_5
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 120
    .line 121
    .line 122
    move-result v4

    .line 123
    if-eqz v4, :cond_6

    .line 124
    .line 125
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v4

    .line 129
    move-object v5, v4

    .line 130
    check-cast v5, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 131
    .line 132
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->g()Z

    .line 133
    .line 134
    .line 135
    move-result v5

    .line 136
    if-nez v5, :cond_5

    .line 137
    .line 138
    goto :goto_3

    .line 139
    :cond_6
    move-object v4, v2

    .line 140
    :goto_3
    check-cast v4, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 141
    .line 142
    if-eqz v4, :cond_7

    .line 143
    .line 144
    invoke-virtual {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 145
    .line 146
    .line 147
    move-result-object v3

    .line 148
    if-eqz v3, :cond_7

    .line 149
    .line 150
    invoke-static {v3}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 151
    .line 152
    .line 153
    move-result-wide v2

    .line 154
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 155
    .line 156
    .line 157
    move-result-object v2

    .line 158
    :cond_7
    invoke-virtual {v1}, Ls30/b;->e()Ljava/util/List;

    .line 159
    .line 160
    .line 161
    move-result-object v1

    .line 162
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 163
    .line 164
    .line 165
    move-result v1

    .line 166
    const/4 v3, 0x1

    .line 167
    if-ne v1, v3, :cond_9

    .line 168
    .line 169
    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    move-result v0

    .line 173
    if-nez v0, :cond_8

    .line 174
    .line 175
    goto :goto_4

    .line 176
    :cond_8
    return p1

    .line 177
    :cond_9
    :goto_4
    return v3

    .line 178
    :cond_a
    return p1
.end method
