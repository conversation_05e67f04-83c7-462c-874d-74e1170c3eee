.class public final synthetic Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/l;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/l;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;

    check-cast p1, Ljava/lang/Long;

    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    invoke-static {v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->c(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;J)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
