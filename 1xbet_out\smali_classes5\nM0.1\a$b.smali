.class public final LnM0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnM0/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LnM0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LnM0/a$b$d;,
        LnM0/a$b$b;,
        LnM0/a$b$a;,
        LnM0/a$b$c;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lqy/b;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/stage_net/presentation/viewmodels/StageNetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfM0/b;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/data/repository/ChampStatisticTourNetRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiM0/a;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiM0/c;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/viewmodels/ChampStatisticTourNetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LnM0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LrM0/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/stage_net/data/repository/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwM0/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQD0/d;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/i;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LrM0/c;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/impl/stage_net/data/repository/StageNetRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwM0/c;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stage/api/domain/TypeStageId;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwM0/e;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNo0/b;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LnM0/a$b;->a:LnM0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p27}, LnM0/a$b;->d(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V

    .line 5
    invoke-virtual/range {p0 .. p27}, LnM0/a$b;->e(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;LnM0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p27}, LnM0/a$b;-><init>(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LnM0/a$b;->g(Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;)Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LnM0/a$b;->h(Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;)Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LnM0/a$b;->f(Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;)Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final d(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V
    .locals 0

    .line 1
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LnM0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, Lorg/xbet/statistic/stage/impl/stage_net/data/repository/b;->a(LBc/a;)Lorg/xbet/statistic/stage/impl/stage_net/data/repository/b;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LnM0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p3}, LwM0/b;->a(LBc/a;)LwM0/b;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LnM0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p3, LnM0/a$b$d;

    .line 20
    .line 21
    invoke-direct {p3, p7}, LnM0/a$b$d;-><init>(LLD0/a;)V

    .line 22
    .line 23
    .line 24
    iput-object p3, p0, LnM0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    iput-object p3, p0, LnM0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p3

    .line 36
    iput-object p3, p0, LnM0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p3

    .line 42
    iput-object p3, p0, LnM0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    new-instance p3, LnM0/a$b$b;

    .line 45
    .line 46
    invoke-direct {p3, p7}, LnM0/a$b$b;-><init>(LLD0/a;)V

    .line 47
    .line 48
    .line 49
    iput-object p3, p0, LnM0/a$b;->i:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object p4, p0, LnM0/a$b;->e:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object p5, p0, LnM0/a$b;->f:Ldagger/internal/h;

    .line 54
    .line 55
    iget-object p6, p0, LnM0/a$b;->g:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object p7, p0, LnM0/a$b;->h:Ldagger/internal/h;

    .line 58
    .line 59
    invoke-static {p4, p5, p6, p7, p3}, Lorg/xbet/statistic/statistic_core/presentation/delegates/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/j;

    .line 60
    .line 61
    .line 62
    move-result-object p3

    .line 63
    iput-object p3, p0, LnM0/a$b;->j:Ldagger/internal/h;

    .line 64
    .line 65
    iget-object p4, p0, LnM0/a$b;->d:Ldagger/internal/h;

    .line 66
    .line 67
    invoke-static {p4, p3}, Lorg/xbet/statistic/stage/impl/core/presentation/d;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/stage/impl/core/presentation/d;

    .line 68
    .line 69
    .line 70
    move-result-object p3

    .line 71
    iput-object p3, p0, LnM0/a$b;->k:Ldagger/internal/h;

    .line 72
    .line 73
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 74
    .line 75
    .line 76
    move-result-object p3

    .line 77
    iput-object p3, p0, LnM0/a$b;->l:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {p3}, LrM0/d;->a(LBc/a;)LrM0/d;

    .line 80
    .line 81
    .line 82
    move-result-object p3

    .line 83
    iput-object p3, p0, LnM0/a$b;->m:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 86
    .line 87
    .line 88
    move-result-object p3

    .line 89
    iput-object p3, p0, LnM0/a$b;->n:Ldagger/internal/h;

    .line 90
    .line 91
    new-instance p3, LnM0/a$b$a;

    .line 92
    .line 93
    invoke-direct {p3, p1}, LnM0/a$b$a;-><init>(LQW0/c;)V

    .line 94
    .line 95
    .line 96
    iput-object p3, p0, LnM0/a$b;->o:Ldagger/internal/h;

    .line 97
    .line 98
    iget-object p1, p0, LnM0/a$b;->m:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object p4, p0, LnM0/a$b;->n:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static {p1, p4, p3}, Lorg/xbet/statistic/stage/impl/stage_net/data/repository/c;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stage/impl/stage_net/data/repository/c;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    iput-object p1, p0, LnM0/a$b;->p:Ldagger/internal/h;

    .line 107
    .line 108
    invoke-static {p1}, LwM0/d;->a(LBc/a;)LwM0/d;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    iput-object p1, p0, LnM0/a$b;->q:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iput-object p1, p0, LnM0/a$b;->r:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object p3, p0, LnM0/a$b;->o:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static {p3, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    iput-object p1, p0, LnM0/a$b;->s:Ldagger/internal/h;

    .line 127
    .line 128
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    iput-object p1, p0, LnM0/a$b;->t:Ldagger/internal/h;

    .line 133
    .line 134
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    iput-object p1, p0, LnM0/a$b;->u:Ldagger/internal/h;

    .line 139
    .line 140
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    iput-object p1, p0, LnM0/a$b;->v:Ldagger/internal/h;

    .line 145
    .line 146
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    iput-object p1, p0, LnM0/a$b;->w:Ldagger/internal/h;

    .line 151
    .line 152
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    iput-object p1, p0, LnM0/a$b;->x:Ldagger/internal/h;

    .line 157
    .line 158
    iget-object p1, p0, LnM0/a$b;->c:Ldagger/internal/h;

    .line 159
    .line 160
    invoke-static {p1}, LwM0/f;->a(LBc/a;)LwM0/f;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    iput-object p1, p0, LnM0/a$b;->y:Ldagger/internal/h;

    .line 165
    .line 166
    new-instance p1, LnM0/a$b$c;

    .line 167
    .line 168
    invoke-direct {p1, p2}, LnM0/a$b$c;-><init>(LJo0/a;)V

    .line 169
    .line 170
    .line 171
    iput-object p1, p0, LnM0/a$b;->z:Ldagger/internal/h;

    .line 172
    .line 173
    return-void
.end method

.method public final e(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iput-object v1, v0, LnM0/a$b;->A:Ldagger/internal/h;

    .line 8
    .line 9
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iput-object v1, v0, LnM0/a$b;->B:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iput-object v1, v0, LnM0/a$b;->C:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    iput-object v1, v0, LnM0/a$b;->D:Ldagger/internal/h;

    .line 26
    .line 27
    iget-object v2, v0, LnM0/a$b;->q:Ldagger/internal/h;

    .line 28
    .line 29
    iget-object v3, v0, LnM0/a$b;->s:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object v4, v0, LnM0/a$b;->t:Ldagger/internal/h;

    .line 32
    .line 33
    iget-object v5, v0, LnM0/a$b;->u:Ldagger/internal/h;

    .line 34
    .line 35
    iget-object v6, v0, LnM0/a$b;->g:Ldagger/internal/h;

    .line 36
    .line 37
    iget-object v7, v0, LnM0/a$b;->v:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object v8, v0, LnM0/a$b;->w:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object v9, v0, LnM0/a$b;->x:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v10, v0, LnM0/a$b;->y:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object v11, v0, LnM0/a$b;->z:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object v12, v0, LnM0/a$b;->A:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v13, v0, LnM0/a$b;->h:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object v14, v0, LnM0/a$b;->B:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object v15, v0, LnM0/a$b;->o:Ldagger/internal/h;

    .line 54
    .line 55
    move-object/from16 p18, v1

    .line 56
    .line 57
    iget-object v1, v0, LnM0/a$b;->f:Ldagger/internal/h;

    .line 58
    .line 59
    move-object/from16 p15, v1

    .line 60
    .line 61
    iget-object v1, v0, LnM0/a$b;->j:Ldagger/internal/h;

    .line 62
    .line 63
    move-object/from16 p16, v1

    .line 64
    .line 65
    iget-object v1, v0, LnM0/a$b;->C:Ldagger/internal/h;

    .line 66
    .line 67
    move-object/from16 p17, v1

    .line 68
    .line 69
    move-object/from16 p1, v2

    .line 70
    .line 71
    move-object/from16 p2, v3

    .line 72
    .line 73
    move-object/from16 p3, v4

    .line 74
    .line 75
    move-object/from16 p4, v5

    .line 76
    .line 77
    move-object/from16 p5, v6

    .line 78
    .line 79
    move-object/from16 p6, v7

    .line 80
    .line 81
    move-object/from16 p7, v8

    .line 82
    .line 83
    move-object/from16 p8, v9

    .line 84
    .line 85
    move-object/from16 p9, v10

    .line 86
    .line 87
    move-object/from16 p10, v11

    .line 88
    .line 89
    move-object/from16 p11, v12

    .line 90
    .line 91
    move-object/from16 p12, v13

    .line 92
    .line 93
    move-object/from16 p13, v14

    .line 94
    .line 95
    move-object/from16 p14, v15

    .line 96
    .line 97
    invoke-static/range {p1 .. p18}, Lorg/xbet/statistic/stage/impl/stage_net/presentation/viewmodels/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stage/impl/stage_net/presentation/viewmodels/b;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    iput-object v1, v0, LnM0/a$b;->E:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v1, v0, LnM0/a$b;->l:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static {v1}, LfM0/c;->a(LBc/a;)LfM0/c;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    iput-object v1, v0, LnM0/a$b;->F:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v2, v0, LnM0/a$b;->n:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v3, v0, LnM0/a$b;->o:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static {v2, v1, v3}, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/data/repository/a;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    iput-object v1, v0, LnM0/a$b;->G:Ldagger/internal/h;

    .line 120
    .line 121
    invoke-static {v1}, LiM0/b;->a(LBc/a;)LiM0/b;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    iput-object v1, v0, LnM0/a$b;->H:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v1, v0, LnM0/a$b;->c:Ldagger/internal/h;

    .line 128
    .line 129
    invoke-static {v1}, LiM0/d;->a(LBc/a;)LiM0/d;

    .line 130
    .line 131
    .line 132
    move-result-object v1

    .line 133
    iput-object v1, v0, LnM0/a$b;->I:Ldagger/internal/h;

    .line 134
    .line 135
    iget-object v2, v0, LnM0/a$b;->H:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object v3, v0, LnM0/a$b;->x:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object v4, v0, LnM0/a$b;->B:Ldagger/internal/h;

    .line 140
    .line 141
    iget-object v5, v0, LnM0/a$b;->u:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object v6, v0, LnM0/a$b;->C:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v7, v0, LnM0/a$b;->j:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object v8, v0, LnM0/a$b;->f:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object v9, v0, LnM0/a$b;->D:Ldagger/internal/h;

    .line 150
    .line 151
    move-object/from16 p3, v1

    .line 152
    .line 153
    move-object/from16 p1, v2

    .line 154
    .line 155
    move-object/from16 p2, v3

    .line 156
    .line 157
    move-object/from16 p4, v4

    .line 158
    .line 159
    move-object/from16 p5, v5

    .line 160
    .line 161
    move-object/from16 p6, v6

    .line 162
    .line 163
    move-object/from16 p7, v7

    .line 164
    .line 165
    move-object/from16 p8, v8

    .line 166
    .line 167
    move-object/from16 p9, v9

    .line 168
    .line 169
    invoke-static/range {p1 .. p9}, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/viewmodels/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/viewmodels/a;

    .line 170
    .line 171
    .line 172
    move-result-object v1

    .line 173
    iput-object v1, v0, LnM0/a$b;->J:Ldagger/internal/h;

    .line 174
    .line 175
    return-void
.end method

.method public final f(Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;)Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LnM0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/d;->a(Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/fragments/ChampStatisticTourNetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;)Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LnM0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stage/impl/core/presentation/c;->a(Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;)Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LnM0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/d;->a(Lorg/xbet/statistic/stage/impl/stage_net/presentation/fragments/StageNetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final i()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x3

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/stage/impl/core/presentation/StageNetBottomSheetViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LnM0/a$b;->k:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/stage/impl/stage_net/presentation/viewmodels/StageNetViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LnM0/a$b;->E:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/viewmodels/ChampStatisticTourNetViewModel;

    .line 23
    .line 24
    iget-object v2, p0, LnM0/a$b;->J:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    return-object v0
.end method

.method public final j()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LnM0/a$b;->i()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
