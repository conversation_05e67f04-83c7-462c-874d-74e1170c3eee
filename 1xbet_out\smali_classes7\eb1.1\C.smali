.class public final synthetic Leb1/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/C;->a:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Leb1/C;->a:Lkotlin/jvm/functions/Function0;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentStagesAltDesignDelegateKt;->d(L<PERSON>lin/jvm/functions/Function0;LB4/a;)Lkot<PERSON>/Unit;

    move-result-object p1

    return-object p1
.end method
