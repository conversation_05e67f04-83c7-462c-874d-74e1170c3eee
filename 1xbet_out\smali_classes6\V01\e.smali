.class public final synthetic LV01/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:J

.field public final synthetic b:F

.field public final synthetic c:F

.field public final synthetic d:F

.field public final synthetic e:F

.field public final synthetic f:F


# direct methods
.method public synthetic constructor <init>(JFFFFF)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, LV01/e;->a:J

    iput p3, p0, LV01/e;->b:F

    iput p4, p0, LV01/e;->c:F

    iput p5, p0, LV01/e;->d:F

    iput p6, p0, LV01/e;->e:F

    iput p7, p0, LV01/e;->f:F

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget-wide v0, p0, LV01/e;->a:J

    iget v2, p0, LV01/e;->b:F

    iget v3, p0, LV01/e;->c:F

    iget v4, p0, LV01/e;->d:F

    iget v5, p0, LV01/e;->e:F

    iget v6, p0, LV01/e;->f:F

    move-object v7, p1

    check-cast v7, Landroidx/compose/ui/graphics/drawscope/f;

    invoke-static/range {v0 .. v7}, LV01/g;->a(JFFFFFLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
