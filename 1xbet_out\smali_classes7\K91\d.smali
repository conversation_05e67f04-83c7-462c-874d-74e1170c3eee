.class public final LK91/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\u0008\u0007\u001a\u009f\u0001\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00022\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00022\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u00022\u0006\u0010\u000c\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u000f2\u000c\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u000f2\u0008\u0008\u0002\u0010\u0012\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0015\u0010\u0016\u001a\u00a3\u0001\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00132\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00022\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00022\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\u00022\u0006\u0010\u000c\u001a\u00020\u00022\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\r2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u000f2\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u000f2\u0008\u0008\u0002\u0010\u0012\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "",
        "partitionId",
        "",
        "whence",
        "countryIdBlocking",
        "countryId",
        "",
        "filterType",
        "ref",
        "lang",
        "groupId",
        "limit",
        "skip",
        "",
        "test",
        "",
        "categoryIdsList",
        "productIdsList",
        "subStringValue",
        "",
        "",
        "c",
        "(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;",
        "sendProducts",
        "brandsIdsList",
        "a",
        "(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;
    .locals 9
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JII",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/String;",
            "IIIZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const-string v0, "whence"

    .line 2
    .line 3
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    invoke-static {v0, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    const-string v0, "fcountry"

    .line 12
    .line 13
    invoke-static {p3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    invoke-static {v0, p3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 18
    .line 19
    .line 20
    move-result-object p3

    .line 21
    const-string v0, "ref"

    .line 22
    .line 23
    invoke-static {p6}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-static {v0, v1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const-string v1, "lng"

    .line 32
    .line 33
    move-object/from16 v2, p7

    .line 34
    .line 35
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    const-string v2, "gr"

    .line 40
    .line 41
    invoke-static/range {p8 .. p8}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    invoke-static {v2, v3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    const-string v3, "limit"

    .line 50
    .line 51
    invoke-static/range {p9 .. p9}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    invoke-static {v3, v4}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    const-string v4, "skip"

    .line 60
    .line 61
    invoke-static/range {p10 .. p10}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v5

    .line 65
    invoke-static {v4, v5}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 66
    .line 67
    .line 68
    move-result-object v4

    .line 69
    const/4 v5, 0x7

    .line 70
    new-array v5, v5, [Lkotlin/Pair;

    .line 71
    .line 72
    const/4 v6, 0x0

    .line 73
    aput-object p2, v5, v6

    .line 74
    .line 75
    const/4 p2, 0x1

    .line 76
    aput-object p3, v5, p2

    .line 77
    .line 78
    const/4 p2, 0x2

    .line 79
    aput-object v0, v5, p2

    .line 80
    .line 81
    const/4 p2, 0x3

    .line 82
    aput-object v1, v5, p2

    .line 83
    .line 84
    const/4 p2, 0x4

    .line 85
    aput-object v2, v5, p2

    .line 86
    .line 87
    const/4 p2, 0x5

    .line 88
    aput-object v3, v5, p2

    .line 89
    .line 90
    const/4 p2, 0x6

    .line 91
    aput-object v4, v5, p2

    .line 92
    .line 93
    invoke-static {v5}, Lkotlin/collections/Q;->o([Lkotlin/Pair;)Ljava/util/Map;

    .line 94
    .line 95
    .line 96
    move-result-object p2

    .line 97
    if-eqz p4, :cond_0

    .line 98
    .line 99
    const-string p3, "country"

    .line 100
    .line 101
    invoke-virtual {p4}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object p4

    .line 105
    invoke-interface {p2, p3, p4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    :cond_0
    sget-object p3, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 109
    .line 110
    invoke-virtual {p3}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 111
    .line 112
    .line 113
    move-result-wide p3

    .line 114
    cmp-long v0, p0, p3

    .line 115
    .line 116
    if-eqz v0, :cond_1

    .line 117
    .line 118
    const-string p3, "partId"

    .line 119
    .line 120
    invoke-static {p0, p1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object p0

    .line 124
    invoke-interface {p2, p3, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    :cond_1
    invoke-interface/range {p13 .. p13}, Ljava/util/Collection;->isEmpty()Z

    .line 128
    .line 129
    .line 130
    move-result p0

    .line 131
    if-nez p0, :cond_2

    .line 132
    .line 133
    const/16 v7, 0x3e

    .line 134
    .line 135
    const/4 v8, 0x0

    .line 136
    const-string v1, ","

    .line 137
    .line 138
    const/4 v2, 0x0

    .line 139
    const/4 v3, 0x0

    .line 140
    const/4 v4, 0x0

    .line 141
    const/4 v5, 0x0

    .line 142
    const/4 v6, 0x0

    .line 143
    move-object/from16 v0, p13

    .line 144
    .line 145
    invoke-static/range {v0 .. v8}, Lkotlin/collections/CollectionsKt;->G0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object p0

    .line 149
    const-string p1, "categoryIds"

    .line 150
    .line 151
    invoke-interface {p2, p1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 152
    .line 153
    .line 154
    :cond_2
    invoke-interface/range {p14 .. p14}, Ljava/util/Collection;->isEmpty()Z

    .line 155
    .line 156
    .line 157
    move-result p0

    .line 158
    if-nez p0, :cond_4

    .line 159
    .line 160
    if-eqz p12, :cond_3

    .line 161
    .line 162
    const-string p0, "productIds"

    .line 163
    .line 164
    goto :goto_0

    .line 165
    :cond_3
    const-string p0, "brandIds"

    .line 166
    .line 167
    :goto_0
    const/16 v7, 0x3e

    .line 168
    .line 169
    const/4 v8, 0x0

    .line 170
    const-string v1, ","

    .line 171
    .line 172
    const/4 v2, 0x0

    .line 173
    const/4 v3, 0x0

    .line 174
    const/4 v4, 0x0

    .line 175
    const/4 v5, 0x0

    .line 176
    const/4 v6, 0x0

    .line 177
    move-object/from16 v0, p14

    .line 178
    .line 179
    invoke-static/range {v0 .. v8}, Lkotlin/collections/CollectionsKt;->G0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/String;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 184
    .line 185
    .line 186
    :cond_4
    invoke-interface/range {p15 .. p15}, Ljava/lang/CharSequence;->length()I

    .line 187
    .line 188
    .line 189
    move-result p0

    .line 190
    if-lez p0, :cond_5

    .line 191
    .line 192
    const-string p0, "nameSubstr"

    .line 193
    .line 194
    move-object/from16 p1, p15

    .line 195
    .line 196
    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 197
    .line 198
    .line 199
    :cond_5
    if-eqz p11, :cond_6

    .line 200
    .line 201
    const-string p0, "test"

    .line 202
    .line 203
    invoke-static/range {p11 .. p11}, Ljava/lang/String;->valueOf(Z)Ljava/lang/String;

    .line 204
    .line 205
    .line 206
    move-result-object p1

    .line 207
    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 208
    .line 209
    .line 210
    :cond_6
    invoke-interface {p5}, Ljava/lang/CharSequence;->length()I

    .line 211
    .line 212
    .line 213
    move-result p0

    .line 214
    if-lez p0, :cond_7

    .line 215
    .line 216
    const-string p0, "filterType"

    .line 217
    .line 218
    invoke-interface {p2, p0, p5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    :cond_7
    return-object p2
.end method

.method public static synthetic b(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZZLjava/util/List;Ljava/util/List;Ljava/lang/String;ILjava/lang/Object;)Ljava/util/Map;
    .locals 17

    .line 1
    move/from16 v0, p16

    .line 2
    .line 3
    and-int/lit16 v0, v0, 0x4000

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    const-string v0, ""

    .line 8
    .line 9
    move-object/from16 v16, v0

    .line 10
    .line 11
    :goto_0
    move-wide/from16 v1, p0

    .line 12
    .line 13
    move/from16 v3, p2

    .line 14
    .line 15
    move/from16 v4, p3

    .line 16
    .line 17
    move-object/from16 v5, p4

    .line 18
    .line 19
    move-object/from16 v6, p5

    .line 20
    .line 21
    move/from16 v7, p6

    .line 22
    .line 23
    move-object/from16 v8, p7

    .line 24
    .line 25
    move/from16 v9, p8

    .line 26
    .line 27
    move/from16 v10, p9

    .line 28
    .line 29
    move/from16 v11, p10

    .line 30
    .line 31
    move/from16 v12, p11

    .line 32
    .line 33
    move/from16 v13, p12

    .line 34
    .line 35
    move-object/from16 v14, p13

    .line 36
    .line 37
    move-object/from16 v15, p14

    .line 38
    .line 39
    goto :goto_1

    .line 40
    :cond_0
    move-object/from16 v16, p15

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :goto_1
    invoke-static/range {v1 .. v16}, LK91/d;->a(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    return-object v0
.end method

.method public static final c(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;
    .locals 9
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JII",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/String;",
            "IIIZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    const-string v0, "whence"

    .line 6
    .line 7
    invoke-static {v0, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 12
    .line 13
    .line 14
    move-result-object p3

    .line 15
    const-string v0, "fcountry"

    .line 16
    .line 17
    invoke-static {v0, p3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 18
    .line 19
    .line 20
    move-result-object p3

    .line 21
    invoke-static {p6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    const-string v1, "ref"

    .line 26
    .line 27
    invoke-static {v1, v0}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const-string v1, "lng"

    .line 32
    .line 33
    move-object/from16 v2, p7

    .line 34
    .line 35
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-static/range {p8 .. p8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    const-string v3, "gr"

    .line 44
    .line 45
    invoke-static {v3, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-static/range {p9 .. p9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 50
    .line 51
    .line 52
    move-result-object v3

    .line 53
    const-string v4, "limit"

    .line 54
    .line 55
    invoke-static {v4, v3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    invoke-static/range {p10 .. p10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object v4

    .line 63
    const-string v5, "skip"

    .line 64
    .line 65
    invoke-static {v5, v4}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 66
    .line 67
    .line 68
    move-result-object v4

    .line 69
    const/4 v5, 0x7

    .line 70
    new-array v5, v5, [Lkotlin/Pair;

    .line 71
    .line 72
    const/4 v6, 0x0

    .line 73
    aput-object p2, v5, v6

    .line 74
    .line 75
    const/4 p2, 0x1

    .line 76
    aput-object p3, v5, p2

    .line 77
    .line 78
    const/4 p2, 0x2

    .line 79
    aput-object v0, v5, p2

    .line 80
    .line 81
    const/4 p2, 0x3

    .line 82
    aput-object v1, v5, p2

    .line 83
    .line 84
    const/4 p2, 0x4

    .line 85
    aput-object v2, v5, p2

    .line 86
    .line 87
    const/4 p2, 0x5

    .line 88
    aput-object v3, v5, p2

    .line 89
    .line 90
    const/4 p2, 0x6

    .line 91
    aput-object v4, v5, p2

    .line 92
    .line 93
    invoke-static {v5}, Lkotlin/collections/Q;->o([Lkotlin/Pair;)Ljava/util/Map;

    .line 94
    .line 95
    .line 96
    move-result-object p2

    .line 97
    if-eqz p4, :cond_0

    .line 98
    .line 99
    const-string p3, "country"

    .line 100
    .line 101
    invoke-interface {p2, p3, p4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    :cond_0
    sget-object p3, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 105
    .line 106
    invoke-virtual {p3}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 107
    .line 108
    .line 109
    move-result-wide p3

    .line 110
    cmp-long v0, p0, p3

    .line 111
    .line 112
    if-eqz v0, :cond_1

    .line 113
    .line 114
    invoke-static {p0, p1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 115
    .line 116
    .line 117
    move-result-object p0

    .line 118
    const-string p1, "partId"

    .line 119
    .line 120
    invoke-interface {p2, p1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    :cond_1
    invoke-interface/range {p12 .. p12}, Ljava/util/Collection;->isEmpty()Z

    .line 124
    .line 125
    .line 126
    move-result p0

    .line 127
    if-nez p0, :cond_2

    .line 128
    .line 129
    const/16 v7, 0x3e

    .line 130
    .line 131
    const/4 v8, 0x0

    .line 132
    const-string v1, ","

    .line 133
    .line 134
    const/4 v2, 0x0

    .line 135
    const/4 v3, 0x0

    .line 136
    const/4 v4, 0x0

    .line 137
    const/4 v5, 0x0

    .line 138
    const/4 v6, 0x0

    .line 139
    move-object/from16 v0, p12

    .line 140
    .line 141
    invoke-static/range {v0 .. v8}, Lkotlin/collections/CollectionsKt;->G0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object p0

    .line 145
    const-string p1, "categoriesId"

    .line 146
    .line 147
    invoke-interface {p2, p1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    :cond_2
    invoke-interface/range {p13 .. p13}, Ljava/util/Collection;->isEmpty()Z

    .line 151
    .line 152
    .line 153
    move-result p0

    .line 154
    if-nez p0, :cond_3

    .line 155
    .line 156
    const/16 v7, 0x3e

    .line 157
    .line 158
    const/4 v8, 0x0

    .line 159
    const-string v1, ","

    .line 160
    .line 161
    const/4 v2, 0x0

    .line 162
    const/4 v3, 0x0

    .line 163
    const/4 v4, 0x0

    .line 164
    const/4 v5, 0x0

    .line 165
    const/4 v6, 0x0

    .line 166
    move-object/from16 v0, p13

    .line 167
    .line 168
    invoke-static/range {v0 .. v8}, Lkotlin/collections/CollectionsKt;->G0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object p0

    .line 172
    const-string p1, "productIds"

    .line 173
    .line 174
    invoke-interface {p2, p1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    :cond_3
    invoke-interface/range {p14 .. p14}, Ljava/lang/CharSequence;->length()I

    .line 178
    .line 179
    .line 180
    move-result p0

    .line 181
    if-lez p0, :cond_4

    .line 182
    .line 183
    const-string p0, "nameSubstr"

    .line 184
    .line 185
    move-object/from16 p1, p14

    .line 186
    .line 187
    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 188
    .line 189
    .line 190
    :cond_4
    if-eqz p11, :cond_5

    .line 191
    .line 192
    invoke-static/range {p11 .. p11}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 193
    .line 194
    .line 195
    move-result-object p0

    .line 196
    const-string p1, "test"

    .line 197
    .line 198
    invoke-interface {p2, p1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    :cond_5
    invoke-interface {p5}, Ljava/lang/CharSequence;->length()I

    .line 202
    .line 203
    .line 204
    move-result p0

    .line 205
    if-lez p0, :cond_6

    .line 206
    .line 207
    const-string p0, "filterType"

    .line 208
    .line 209
    invoke-interface {p2, p0, p5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 210
    .line 211
    .line 212
    :cond_6
    return-object p2
.end method

.method public static synthetic d(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZLjava/util/List;Ljava/util/List;Ljava/lang/String;ILjava/lang/Object;)Ljava/util/Map;
    .locals 17

    .line 1
    move/from16 v0, p15

    .line 2
    .line 3
    and-int/lit16 v1, v0, 0x100

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    const/16 v1, 0x10

    .line 8
    .line 9
    const/16 v11, 0x10

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    move/from16 v11, p9

    .line 13
    .line 14
    :goto_0
    and-int/lit16 v1, v0, 0x400

    .line 15
    .line 16
    if-eqz v1, :cond_1

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    const/4 v13, 0x0

    .line 20
    goto :goto_1

    .line 21
    :cond_1
    move/from16 v13, p11

    .line 22
    .line 23
    :goto_1
    and-int/lit16 v0, v0, 0x2000

    .line 24
    .line 25
    if-eqz v0, :cond_2

    .line 26
    .line 27
    const-string v0, ""

    .line 28
    .line 29
    move-object/from16 v16, v0

    .line 30
    .line 31
    :goto_2
    move-wide/from16 v2, p0

    .line 32
    .line 33
    move/from16 v4, p2

    .line 34
    .line 35
    move/from16 v5, p3

    .line 36
    .line 37
    move-object/from16 v6, p4

    .line 38
    .line 39
    move-object/from16 v7, p5

    .line 40
    .line 41
    move/from16 v8, p6

    .line 42
    .line 43
    move-object/from16 v9, p7

    .line 44
    .line 45
    move/from16 v10, p8

    .line 46
    .line 47
    move/from16 v12, p10

    .line 48
    .line 49
    move-object/from16 v14, p12

    .line 50
    .line 51
    move-object/from16 v15, p13

    .line 52
    .line 53
    goto :goto_3

    .line 54
    :cond_2
    move-object/from16 v16, p14

    .line 55
    .line 56
    goto :goto_2

    .line 57
    :goto_3
    invoke-static/range {v2 .. v16}, LK91/d;->c(JIILjava/lang/Integer;Ljava/lang/String;ILjava/lang/String;IIIZLjava/util/List;Ljava/util/List;Ljava/lang/String;)Ljava/util/Map;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    return-object v0
.end method
