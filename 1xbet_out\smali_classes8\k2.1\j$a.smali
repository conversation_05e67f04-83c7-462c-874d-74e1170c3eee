.class public Lk2/j$a;
.super Lk2/p;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lk2/j;->z()Lk2/p;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic g:Lk2/j;


# direct methods
.method public constructor <init>(Lk2/j;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lk2/j$a;->g:Lk2/j;

    .line 2
    .line 3
    invoke-direct {p0}, Lk2/p;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public u()V
    .locals 1

    .line 1
    iget-object v0, p0, Lk2/j$a;->g:Lk2/j;

    .line 2
    .line 3
    invoke-static {v0, p0}, Lk2/j;->x(Lk2/j;Lv1/h;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
