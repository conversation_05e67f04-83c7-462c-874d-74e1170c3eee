.class public final LIN0/v$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LIN0/v;->b(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/n<",
        "Landroidx/compose/foundation/layout/m;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Z

.field public final synthetic c:I

.field public final synthetic d:I

.field public final synthetic e:Landroidx/compose/runtime/r1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/r1<",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic f:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "Landroidx/compose/foundation/layout/m;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function0;ZIILandroidx/compose/runtime/r1;LOc/n;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;ZII",
            "Landroidx/compose/runtime/r1<",
            "Ljava/lang/Float;",
            ">;",
            "LOc/n<",
            "-",
            "Landroidx/compose/foundation/layout/m;",
            "-",
            "Landroidx/compose/runtime/j;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LIN0/v$a;->a:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    iput-boolean p2, p0, LIN0/v$a;->b:Z

    .line 4
    .line 5
    iput p3, p0, LIN0/v$a;->c:I

    .line 6
    .line 7
    iput p4, p0, LIN0/v$a;->d:I

    .line 8
    .line 9
    iput-object p5, p0, LIN0/v$a;->e:Landroidx/compose/runtime/r1;

    .line 10
    .line 11
    iput-object p6, p0, LIN0/v$a;->f:LOc/n;

    .line 12
    .line 13
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 14
    .line 15
    .line 16
    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/foundation/layout/m;Landroidx/compose/runtime/j;I)V
    .locals 41

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v6, p2

    .line 4
    .line 5
    move/from16 v1, p3

    .line 6
    .line 7
    and-int/lit8 v2, v1, 0x11

    .line 8
    .line 9
    const/16 v3, 0x10

    .line 10
    .line 11
    if-ne v2, v3, :cond_1

    .line 12
    .line 13
    invoke-interface {v6}, Landroidx/compose/runtime/j;->c()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    if-nez v2, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    invoke-interface {v6}, Landroidx/compose/runtime/j;->n()V

    .line 21
    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_2

    .line 29
    .line 30
    const/4 v2, -0x1

    .line 31
    const-string v3, "org.xbet.statistic.statistic_core.presentation.composable.StatisticBoxCardComponent.<anonymous> (StatisticBoxCardComponent.kt:65)"

    .line 32
    .line 33
    const v4, 0x4f58e433    # 3.63883392E9f

    .line 34
    .line 35
    .line 36
    invoke-static {v4, v1, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 37
    .line 38
    .line 39
    :cond_2
    sget-object v5, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 40
    .line 41
    const/4 v1, 0x0

    .line 42
    const/4 v2, 0x0

    .line 43
    const/4 v3, 0x1

    .line 44
    invoke-static {v5, v1, v3, v2}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iget-object v13, v0, LIN0/v$a;->a:Lkotlin/jvm/functions/Function0;

    .line 49
    .line 50
    iget-boolean v2, v0, LIN0/v$a;->b:Z

    .line 51
    .line 52
    iget v4, v0, LIN0/v$a;->c:I

    .line 53
    .line 54
    iget v7, v0, LIN0/v$a;->d:I

    .line 55
    .line 56
    iget-object v8, v0, LIN0/v$a;->e:Landroidx/compose/runtime/r1;

    .line 57
    .line 58
    iget-object v9, v0, LIN0/v$a;->f:LOc/n;

    .line 59
    .line 60
    sget-object v16, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 61
    .line 62
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 63
    .line 64
    .line 65
    move-result-object v10

    .line 66
    sget-object v26, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 67
    .line 68
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 69
    .line 70
    .line 71
    move-result-object v11

    .line 72
    const/4 v12, 0x0

    .line 73
    invoke-static {v10, v11, v6, v12}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 74
    .line 75
    .line 76
    move-result-object v10

    .line 77
    invoke-static {v6, v12}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 78
    .line 79
    .line 80
    move-result v11

    .line 81
    invoke-interface {v6}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 82
    .line 83
    .line 84
    move-result-object v14

    .line 85
    invoke-static {v6, v1}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    sget-object v27, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 90
    .line 91
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 92
    .line 93
    .line 94
    move-result-object v15

    .line 95
    invoke-interface {v6}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 96
    .line 97
    .line 98
    move-result-object v17

    .line 99
    invoke-static/range {v17 .. v17}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 100
    .line 101
    .line 102
    move-result v17

    .line 103
    if-nez v17, :cond_3

    .line 104
    .line 105
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 106
    .line 107
    .line 108
    :cond_3
    invoke-interface {v6}, Landroidx/compose/runtime/j;->l()V

    .line 109
    .line 110
    .line 111
    invoke-interface {v6}, Landroidx/compose/runtime/j;->B()Z

    .line 112
    .line 113
    .line 114
    move-result v17

    .line 115
    if-eqz v17, :cond_4

    .line 116
    .line 117
    invoke-interface {v6, v15}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 118
    .line 119
    .line 120
    goto :goto_1

    .line 121
    :cond_4
    invoke-interface {v6}, Landroidx/compose/runtime/j;->h()V

    .line 122
    .line 123
    .line 124
    :goto_1
    invoke-static {v6}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 125
    .line 126
    .line 127
    move-result-object v15

    .line 128
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 129
    .line 130
    .line 131
    move-result-object v3

    .line 132
    invoke-static {v15, v10, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 133
    .line 134
    .line 135
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 136
    .line 137
    .line 138
    move-result-object v3

    .line 139
    invoke-static {v15, v14, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 140
    .line 141
    .line 142
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 143
    .line 144
    .line 145
    move-result-object v3

    .line 146
    invoke-interface {v15}, Landroidx/compose/runtime/j;->B()Z

    .line 147
    .line 148
    .line 149
    move-result v10

    .line 150
    if-nez v10, :cond_5

    .line 151
    .line 152
    invoke-interface {v15}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v10

    .line 156
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 157
    .line 158
    .line 159
    move-result-object v14

    .line 160
    invoke-static {v10, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 161
    .line 162
    .line 163
    move-result v10

    .line 164
    if-nez v10, :cond_6

    .line 165
    .line 166
    :cond_5
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 167
    .line 168
    .line 169
    move-result-object v10

    .line 170
    invoke-interface {v15, v10}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 171
    .line 172
    .line 173
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 174
    .line 175
    .line 176
    move-result-object v10

    .line 177
    invoke-interface {v15, v10, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 178
    .line 179
    .line 180
    :cond_6
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 181
    .line 182
    .line 183
    move-result-object v3

    .line 184
    invoke-static {v15, v1, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 185
    .line 186
    .line 187
    sget-object v1, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 188
    .line 189
    const v3, 0x6e3c21fe

    .line 190
    .line 191
    .line 192
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 193
    .line 194
    .line 195
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v3

    .line 199
    sget-object v10, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 200
    .line 201
    invoke-virtual {v10}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 202
    .line 203
    .line 204
    move-result-object v10

    .line 205
    if-ne v3, v10, :cond_7

    .line 206
    .line 207
    invoke-static {}, Landroidx/compose/foundation/interaction/h;->a()Landroidx/compose/foundation/interaction/i;

    .line 208
    .line 209
    .line 210
    move-result-object v3

    .line 211
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 212
    .line 213
    .line 214
    :cond_7
    check-cast v3, Landroidx/compose/foundation/interaction/i;

    .line 215
    .line 216
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 217
    .line 218
    .line 219
    const/16 v14, 0x1c

    .line 220
    .line 221
    const/4 v15, 0x0

    .line 222
    move-object v10, v9

    .line 223
    const/4 v9, 0x0

    .line 224
    move-object v11, v10

    .line 225
    const/4 v10, 0x0

    .line 226
    move-object/from16 v17, v11

    .line 227
    .line 228
    const/4 v11, 0x0

    .line 229
    const/16 v18, 0x0

    .line 230
    .line 231
    const/4 v12, 0x0

    .line 232
    move-object/from16 v28, v8

    .line 233
    .line 234
    move-object v8, v3

    .line 235
    move v3, v7

    .line 236
    move-object v7, v5

    .line 237
    const/4 v5, 0x0

    .line 238
    invoke-static/range {v7 .. v15}, Landroidx/compose/foundation/ClickableKt;->d(Landroidx/compose/ui/l;Landroidx/compose/foundation/interaction/i;Landroidx/compose/foundation/E;ZLjava/lang/String;Landroidx/compose/ui/semantics/i;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 239
    .line 240
    .line 241
    move-result-object v8

    .line 242
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 243
    .line 244
    .line 245
    move-result-object v9

    .line 246
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 247
    .line 248
    .line 249
    move-result-object v10

    .line 250
    const/16 v11, 0x30

    .line 251
    .line 252
    invoke-static {v10, v9, v6, v11}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 253
    .line 254
    .line 255
    move-result-object v9

    .line 256
    invoke-static {v6, v5}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 257
    .line 258
    .line 259
    move-result v10

    .line 260
    invoke-interface {v6}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 261
    .line 262
    .line 263
    move-result-object v11

    .line 264
    invoke-static {v6, v8}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 265
    .line 266
    .line 267
    move-result-object v8

    .line 268
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 269
    .line 270
    .line 271
    move-result-object v12

    .line 272
    invoke-interface {v6}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 273
    .line 274
    .line 275
    move-result-object v13

    .line 276
    invoke-static {v13}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 277
    .line 278
    .line 279
    move-result v13

    .line 280
    if-nez v13, :cond_8

    .line 281
    .line 282
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 283
    .line 284
    .line 285
    :cond_8
    invoke-interface {v6}, Landroidx/compose/runtime/j;->l()V

    .line 286
    .line 287
    .line 288
    invoke-interface {v6}, Landroidx/compose/runtime/j;->B()Z

    .line 289
    .line 290
    .line 291
    move-result v13

    .line 292
    if-eqz v13, :cond_9

    .line 293
    .line 294
    invoke-interface {v6, v12}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 295
    .line 296
    .line 297
    goto :goto_2

    .line 298
    :cond_9
    invoke-interface {v6}, Landroidx/compose/runtime/j;->h()V

    .line 299
    .line 300
    .line 301
    :goto_2
    invoke-static {v6}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 302
    .line 303
    .line 304
    move-result-object v12

    .line 305
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 306
    .line 307
    .line 308
    move-result-object v13

    .line 309
    invoke-static {v12, v9, v13}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 310
    .line 311
    .line 312
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 313
    .line 314
    .line 315
    move-result-object v9

    .line 316
    invoke-static {v12, v11, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 317
    .line 318
    .line 319
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 320
    .line 321
    .line 322
    move-result-object v9

    .line 323
    invoke-interface {v12}, Landroidx/compose/runtime/j;->B()Z

    .line 324
    .line 325
    .line 326
    move-result v11

    .line 327
    if-nez v11, :cond_a

    .line 328
    .line 329
    invoke-interface {v12}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 330
    .line 331
    .line 332
    move-result-object v11

    .line 333
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 334
    .line 335
    .line 336
    move-result-object v13

    .line 337
    invoke-static {v11, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 338
    .line 339
    .line 340
    move-result v11

    .line 341
    if-nez v11, :cond_b

    .line 342
    .line 343
    :cond_a
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 344
    .line 345
    .line 346
    move-result-object v11

    .line 347
    invoke-interface {v12, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 348
    .line 349
    .line 350
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 351
    .line 352
    .line 353
    move-result-object v10

    .line 354
    invoke-interface {v12, v10, v9}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 355
    .line 356
    .line 357
    :cond_b
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 358
    .line 359
    .line 360
    move-result-object v9

    .line 361
    invoke-static {v12, v8, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 362
    .line 363
    .line 364
    sget-object v18, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 365
    .line 366
    sget-object v29, LA11/a;->a:LA11/a;

    .line 367
    .line 368
    invoke-virtual/range {v29 .. v29}, LA11/a;->l1()F

    .line 369
    .line 370
    .line 371
    move-result v8

    .line 372
    invoke-virtual/range {v29 .. v29}, LA11/a;->l1()F

    .line 373
    .line 374
    .line 375
    move-result v9

    .line 376
    invoke-virtual/range {v29 .. v29}, LA11/a;->l1()F

    .line 377
    .line 378
    .line 379
    move-result v11

    .line 380
    const/4 v12, 0x4

    .line 381
    const/4 v13, 0x0

    .line 382
    const/4 v10, 0x0

    .line 383
    invoke-static/range {v7 .. v13}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 384
    .line 385
    .line 386
    move-result-object v8

    .line 387
    move-object v9, v7

    .line 388
    invoke-virtual/range {v29 .. v29}, LA11/a;->o0()F

    .line 389
    .line 390
    .line 391
    move-result v7

    .line 392
    invoke-static {v8, v7}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 393
    .line 394
    .line 395
    move-result-object v7

    .line 396
    invoke-static {v4, v6, v5}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 397
    .line 398
    .line 399
    move-result-object v4

    .line 400
    sget-object v12, LB11/e;->a:LB11/e;

    .line 401
    .line 402
    sget v13, LB11/e;->b:I

    .line 403
    .line 404
    invoke-virtual {v12, v6, v13}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 405
    .line 406
    .line 407
    move-result-object v8

    .line 408
    invoke-virtual {v8}, Lorg/xbet/uikit/compose/color/ThemeColors;->getPrimary-0d7_KjU()J

    .line 409
    .line 410
    .line 411
    move-result-wide v10

    .line 412
    move v8, v3

    .line 413
    move-object v3, v7

    .line 414
    const/16 v7, 0x30

    .line 415
    .line 416
    move v14, v8

    .line 417
    const/4 v8, 0x0

    .line 418
    move v15, v2

    .line 419
    const/4 v2, 0x0

    .line 420
    move-wide/from16 v39, v10

    .line 421
    .line 422
    move-object v11, v1

    .line 423
    move-object v1, v4

    .line 424
    move-wide/from16 v4, v39

    .line 425
    .line 426
    move/from16 v30, v15

    .line 427
    .line 428
    const/4 v10, 0x0

    .line 429
    move v15, v14

    .line 430
    const/4 v14, 0x1

    .line 431
    invoke-static/range {v1 .. v8}, Landroidx/compose/material3/IconKt;->c(Landroidx/compose/ui/graphics/painter/Painter;Ljava/lang/String;Landroidx/compose/ui/l;JLandroidx/compose/runtime/j;II)V

    .line 432
    .line 433
    .line 434
    move-object v1, v6

    .line 435
    invoke-virtual/range {v29 .. v29}, LA11/a;->L1()F

    .line 436
    .line 437
    .line 438
    move-result v6

    .line 439
    const/4 v5, 0x0

    .line 440
    const/16 v10, 0xe

    .line 441
    .line 442
    move-object v2, v11

    .line 443
    const/4 v11, 0x0

    .line 444
    const/4 v7, 0x0

    .line 445
    const/4 v8, 0x0

    .line 446
    move-object v5, v9

    .line 447
    const/4 v3, 0x0

    .line 448
    const/4 v9, 0x0

    .line 449
    move-object v3, v2

    .line 450
    const/4 v2, 0x0

    .line 451
    invoke-static/range {v5 .. v11}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 452
    .line 453
    .line 454
    move-result-object v19

    .line 455
    move-object/from16 v31, v5

    .line 456
    .line 457
    const/16 v22, 0x2

    .line 458
    .line 459
    const/16 v23, 0x0

    .line 460
    .line 461
    const/high16 v20, 0x3f800000    # 1.0f

    .line 462
    .line 463
    const/16 v21, 0x0

    .line 464
    .line 465
    invoke-static/range {v18 .. v23}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 466
    .line 467
    .line 468
    move-result-object v4

    .line 469
    invoke-static {v15, v1, v2}, Lm0/h;->a(ILandroidx/compose/runtime/j;I)Ljava/lang/String;

    .line 470
    .line 471
    .line 472
    move-result-object v5

    .line 473
    sget-object v6, LC11/a;->a:LC11/a;

    .line 474
    .line 475
    invoke-virtual {v6}, LC11/a;->o()Landroidx/compose/ui/text/a0;

    .line 476
    .line 477
    .line 478
    move-result-object v6

    .line 479
    invoke-static {v6, v1, v2}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 480
    .line 481
    .line 482
    move-result-object v21

    .line 483
    sget-object v6, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 484
    .line 485
    invoke-virtual {v6}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 486
    .line 487
    .line 488
    move-result v16

    .line 489
    const/16 v24, 0xc30

    .line 490
    .line 491
    const v25, 0xd7fc

    .line 492
    .line 493
    .line 494
    move-object v11, v3

    .line 495
    move-object v2, v4

    .line 496
    const/16 v18, 0x0

    .line 497
    .line 498
    const-wide/16 v3, 0x0

    .line 499
    .line 500
    move-object v1, v5

    .line 501
    const-wide/16 v5, 0x0

    .line 502
    .line 503
    const/4 v7, 0x0

    .line 504
    const/4 v8, 0x0

    .line 505
    const/4 v9, 0x0

    .line 506
    move-object v15, v11

    .line 507
    const-wide/16 v10, 0x0

    .line 508
    .line 509
    move-object/from16 v19, v12

    .line 510
    .line 511
    const/4 v12, 0x0

    .line 512
    move/from16 v20, v13

    .line 513
    .line 514
    const/4 v13, 0x0

    .line 515
    move-object/from16 v23, v15

    .line 516
    .line 517
    const/16 v22, 0x1

    .line 518
    .line 519
    const-wide/16 v14, 0x0

    .line 520
    .line 521
    move-object/from16 v32, v17

    .line 522
    .line 523
    const/16 v17, 0x0

    .line 524
    .line 525
    const/16 v33, 0x0

    .line 526
    .line 527
    const/16 v18, 0x2

    .line 528
    .line 529
    move-object/from16 v34, v19

    .line 530
    .line 531
    const/16 v19, 0x0

    .line 532
    .line 533
    move/from16 v35, v20

    .line 534
    .line 535
    const/16 v20, 0x0

    .line 536
    .line 537
    move-object/from16 v36, v23

    .line 538
    .line 539
    const/16 v23, 0x0

    .line 540
    .line 541
    move-object/from16 v22, p2

    .line 542
    .line 543
    move-object/from16 v0, v34

    .line 544
    .line 545
    move/from16 v38, v35

    .line 546
    .line 547
    move-object/from16 v37, v36

    .line 548
    .line 549
    invoke-static/range {v1 .. v25}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 550
    .line 551
    .line 552
    move-object/from16 v1, v22

    .line 553
    .line 554
    invoke-virtual/range {v29 .. v29}, LA11/a;->l1()F

    .line 555
    .line 556
    .line 557
    move-result v8

    .line 558
    const/16 v10, 0xb

    .line 559
    .line 560
    const/4 v11, 0x0

    .line 561
    const/4 v6, 0x0

    .line 562
    const/4 v7, 0x0

    .line 563
    const/4 v9, 0x0

    .line 564
    move-object/from16 v5, v31

    .line 565
    .line 566
    invoke-static/range {v5 .. v11}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 567
    .line 568
    .line 569
    move-result-object v2

    .line 570
    move-object v9, v5

    .line 571
    invoke-virtual/range {v29 .. v29}, LA11/a;->o0()F

    .line 572
    .line 573
    .line 574
    move-result v3

    .line 575
    invoke-static {v2, v3}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 576
    .line 577
    .line 578
    move-result-object v2

    .line 579
    move/from16 v10, v38

    .line 580
    .line 581
    invoke-virtual {v0, v1, v10}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 582
    .line 583
    .line 584
    move-result-object v3

    .line 585
    invoke-virtual {v3}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackground-0d7_KjU()J

    .line 586
    .line 587
    .line 588
    move-result-wide v3

    .line 589
    invoke-static {}, LR/i;->i()LR/h;

    .line 590
    .line 591
    .line 592
    move-result-object v5

    .line 593
    invoke-static {v2, v3, v4, v5}, Landroidx/compose/foundation/BackgroundKt;->c(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 594
    .line 595
    .line 596
    move-result-object v2

    .line 597
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 598
    .line 599
    .line 600
    move-result-object v3

    .line 601
    const/4 v5, 0x0

    .line 602
    invoke-static {v3, v5}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 603
    .line 604
    .line 605
    move-result-object v3

    .line 606
    invoke-static {v1, v5}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 607
    .line 608
    .line 609
    move-result v4

    .line 610
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 611
    .line 612
    .line 613
    move-result-object v5

    .line 614
    invoke-static {v1, v2}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 615
    .line 616
    .line 617
    move-result-object v2

    .line 618
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 619
    .line 620
    .line 621
    move-result-object v6

    .line 622
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 623
    .line 624
    .line 625
    move-result-object v7

    .line 626
    invoke-static {v7}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 627
    .line 628
    .line 629
    move-result v7

    .line 630
    if-nez v7, :cond_c

    .line 631
    .line 632
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 633
    .line 634
    .line 635
    :cond_c
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 636
    .line 637
    .line 638
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    .line 639
    .line 640
    .line 641
    move-result v7

    .line 642
    if-eqz v7, :cond_d

    .line 643
    .line 644
    invoke-interface {v1, v6}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 645
    .line 646
    .line 647
    goto :goto_3

    .line 648
    :cond_d
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 649
    .line 650
    .line 651
    :goto_3
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 652
    .line 653
    .line 654
    move-result-object v6

    .line 655
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 656
    .line 657
    .line 658
    move-result-object v7

    .line 659
    invoke-static {v6, v3, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 660
    .line 661
    .line 662
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 663
    .line 664
    .line 665
    move-result-object v3

    .line 666
    invoke-static {v6, v5, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 667
    .line 668
    .line 669
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 670
    .line 671
    .line 672
    move-result-object v3

    .line 673
    invoke-interface {v6}, Landroidx/compose/runtime/j;->B()Z

    .line 674
    .line 675
    .line 676
    move-result v5

    .line 677
    if-nez v5, :cond_e

    .line 678
    .line 679
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 680
    .line 681
    .line 682
    move-result-object v5

    .line 683
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 684
    .line 685
    .line 686
    move-result-object v7

    .line 687
    invoke-static {v5, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 688
    .line 689
    .line 690
    move-result v5

    .line 691
    if-nez v5, :cond_f

    .line 692
    .line 693
    :cond_e
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 694
    .line 695
    .line 696
    move-result-object v5

    .line 697
    invoke-interface {v6, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 698
    .line 699
    .line 700
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 701
    .line 702
    .line 703
    move-result-object v4

    .line 704
    invoke-interface {v6, v4, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 705
    .line 706
    .line 707
    :cond_f
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 708
    .line 709
    .line 710
    move-result-object v3

    .line 711
    invoke-static {v6, v2, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 712
    .line 713
    .line 714
    sget-object v2, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 715
    .line 716
    sget v3, LlZ0/h;->accordion_drawable_down:I

    .line 717
    .line 718
    const/4 v5, 0x0

    .line 719
    invoke-static {v3, v1, v5}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 720
    .line 721
    .line 722
    move-result-object v3

    .line 723
    invoke-virtual/range {v29 .. v29}, LA11/a;->U()F

    .line 724
    .line 725
    .line 726
    move-result v4

    .line 727
    invoke-static {v9, v4}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 728
    .line 729
    .line 730
    move-result-object v4

    .line 731
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/e$a;->e()Landroidx/compose/ui/e;

    .line 732
    .line 733
    .line 734
    move-result-object v5

    .line 735
    invoke-interface {v2, v4, v5}, Landroidx/compose/foundation/layout/h;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/e;)Landroidx/compose/ui/l;

    .line 736
    .line 737
    .line 738
    move-result-object v2

    .line 739
    invoke-static/range {v28 .. v28}, LIN0/v;->e(Landroidx/compose/runtime/r1;)F

    .line 740
    .line 741
    .line 742
    move-result v4

    .line 743
    invoke-static {v2, v4}, Landroidx/compose/ui/draw/m;->a(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 744
    .line 745
    .line 746
    move-result-object v2

    .line 747
    invoke-virtual {v0, v1, v10}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 748
    .line 749
    .line 750
    move-result-object v4

    .line 751
    invoke-virtual {v4}, Lorg/xbet/uikit/compose/color/ThemeColors;->getPrimary-0d7_KjU()J

    .line 752
    .line 753
    .line 754
    move-result-wide v4

    .line 755
    const/16 v7, 0x30

    .line 756
    .line 757
    const/4 v8, 0x0

    .line 758
    move-object v1, v3

    .line 759
    move-object v3, v2

    .line 760
    const/4 v2, 0x0

    .line 761
    move-object/from16 v6, p2

    .line 762
    .line 763
    invoke-static/range {v1 .. v8}, Landroidx/compose/material3/IconKt;->c(Landroidx/compose/ui/graphics/painter/Painter;Ljava/lang/String;Landroidx/compose/ui/l;JLandroidx/compose/runtime/j;II)V

    .line 764
    .line 765
    .line 766
    move-object v1, v6

    .line 767
    invoke-interface {v1}, Landroidx/compose/runtime/j;->j()V

    .line 768
    .line 769
    .line 770
    invoke-interface {v1}, Landroidx/compose/runtime/j;->j()V

    .line 771
    .line 772
    .line 773
    invoke-virtual {v0, v1, v10}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 774
    .line 775
    .line 776
    move-result-object v0

    .line 777
    invoke-virtual {v0}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 778
    .line 779
    .line 780
    move-result-wide v6

    .line 781
    move-object v5, v9

    .line 782
    const/4 v9, 0x2

    .line 783
    const/4 v10, 0x0

    .line 784
    const/4 v8, 0x0

    .line 785
    invoke-static/range {v5 .. v10}, Landroidx/compose/foundation/BackgroundKt;->d(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 786
    .line 787
    .line 788
    move-result-object v3

    .line 789
    new-instance v0, LIN0/v$a$a;

    .line 790
    .line 791
    move-object/from16 v10, v32

    .line 792
    .line 793
    move-object/from16 v11, v37

    .line 794
    .line 795
    invoke-direct {v0, v10, v11}, LIN0/v$a$a;-><init>(LOc/n;Landroidx/compose/foundation/layout/m;)V

    .line 796
    .line 797
    .line 798
    const/16 v2, 0x36

    .line 799
    .line 800
    const v4, 0x7eb37701

    .line 801
    .line 802
    .line 803
    const/4 v14, 0x1

    .line 804
    invoke-static {v4, v14, v0, v1, v2}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 805
    .line 806
    .line 807
    move-result-object v7

    .line 808
    const v9, 0x180006

    .line 809
    .line 810
    .line 811
    const/16 v10, 0x1c

    .line 812
    .line 813
    const/4 v4, 0x0

    .line 814
    const/4 v5, 0x0

    .line 815
    const/4 v6, 0x0

    .line 816
    move-object v8, v1

    .line 817
    move-object v1, v11

    .line 818
    move/from16 v2, v30

    .line 819
    .line 820
    invoke-static/range {v1 .. v10}, Landroidx/compose/animation/AnimatedVisibilityKt;->g(Landroidx/compose/foundation/layout/m;ZLandroidx/compose/ui/l;Landroidx/compose/animation/n;Landroidx/compose/animation/p;Ljava/lang/String;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 821
    .line 822
    .line 823
    invoke-interface/range {p2 .. p2}, Landroidx/compose/runtime/j;->j()V

    .line 824
    .line 825
    .line 826
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 827
    .line 828
    .line 829
    move-result v0

    .line 830
    if-eqz v0, :cond_10

    .line 831
    .line 832
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 833
    .line 834
    .line 835
    :cond_10
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/m;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/j;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, LIN0/v$a;->a(Landroidx/compose/foundation/layout/m;Landroidx/compose/runtime/j;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method
