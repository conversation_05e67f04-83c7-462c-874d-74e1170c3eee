.class public final Lorg/xbet/main_menu/impl/presentation/list/line/s;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lg60/a;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LDU/a;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LS00/e;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LFI/d;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfe0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LPo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lmo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final J:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lno0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final K:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/u0;",
            ">;"
        }
    .end annotation
.end field

.field public final L:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHg/d;",
            ">;"
        }
    .end annotation
.end field

.field public final M:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LDg/c;",
            ">;"
        }
    .end annotation
.end field

.field public final N:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/b;",
            ">;"
        }
    .end annotation
.end field

.field public final O:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfS/a;",
            ">;"
        }
    .end annotation
.end field

.field public final P:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LpS/b;",
            ">;"
        }
    .end annotation
.end field

.field public final Q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final R:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final S:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final T:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final U:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final V:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/c0;",
            ">;"
        }
    .end annotation
.end field

.field public final W:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LkW0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final X:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Luk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final Y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LZQ/a;",
            ">;"
        }
    .end annotation
.end field

.field public final Z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LPu/a;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final a0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LW81/a;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ldu/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw30/s;",
            ">;"
        }
    .end annotation
.end field

.field public final c0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw30/k;",
            ">;"
        }
    .end annotation
.end field

.field public final d0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final e0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LMR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final f0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LTX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public final g0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LTX/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/CyberAnalyticUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final h0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVX/a;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final i0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LKX/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final j0:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LMX/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LFi/b;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lek/a;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ld60/d;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LJT/c;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lt30/b;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LqS0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LTf0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LzV/a;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgl0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lnn0/f;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVg0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LbV0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXV/a;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lok/a;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lnm/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LS00/k;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;",
            "LBc/a<",
            "Lw30/s;",
            ">;",
            "LBc/a<",
            "Lw30/k;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/CyberAnalyticUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "LFi/b;",
            ">;",
            "LBc/a<",
            "Lek/a;",
            ">;",
            "LBc/a<",
            "Ld60/d;",
            ">;",
            "LBc/a<",
            "LJT/c;",
            ">;",
            "LBc/a<",
            "Lt30/b;",
            ">;",
            "LBc/a<",
            "LqS0/a;",
            ">;",
            "LBc/a<",
            "LTf0/a;",
            ">;",
            "LBc/a<",
            "LzV/a;",
            ">;",
            "LBc/a<",
            "Lgl0/a;",
            ">;",
            "LBc/a<",
            "Lnn0/f;",
            ">;",
            "LBc/a<",
            "LVg0/a;",
            ">;",
            "LBc/a<",
            "LbV0/a;",
            ">;",
            "LBc/a<",
            "LXV/a;",
            ">;",
            "LBc/a<",
            "Lok/a;",
            ">;",
            "LBc/a<",
            "Lnm/a;",
            ">;",
            "LBc/a<",
            "LS00/k;",
            ">;",
            "LBc/a<",
            "Lg60/a;",
            ">;",
            "LBc/a<",
            "LDU/a;",
            ">;",
            "LBc/a<",
            "LS00/e;",
            ">;",
            "LBc/a<",
            "LFI/d;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;",
            "LBc/a<",
            "Lfe0/a;",
            ">;",
            "LBc/a<",
            "LPo0/a;",
            ">;",
            "LBc/a<",
            "Lmo0/a;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "Lno0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/u0;",
            ">;",
            "LBc/a<",
            "LHg/d;",
            ">;",
            "LBc/a<",
            "LDg/c;",
            ">;",
            "LBc/a<",
            "LnR/b;",
            ">;",
            "LBc/a<",
            "LfS/a;",
            ">;",
            "LBc/a<",
            "LpS/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/c0;",
            ">;",
            "LBc/a<",
            "LkW0/a;",
            ">;",
            "LBc/a<",
            "Luk0/a;",
            ">;",
            "LBc/a<",
            "LZQ/a;",
            ">;",
            "LBc/a<",
            "LPu/a;",
            ">;",
            "LBc/a<",
            "LW81/a;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;",
            ">;",
            "LBc/a<",
            "LwX0/g;",
            ">;",
            "LBc/a<",
            "LMR/a;",
            ">;",
            "LBc/a<",
            "LTX/b;",
            ">;",
            "LBc/a<",
            "LTX/a;",
            ">;",
            "LBc/a<",
            "LVX/a;",
            ">;",
            "LBc/a<",
            "LKX/a;",
            ">;",
            "LBc/a<",
            "LMX/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->H:LBc/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->I:LBc/a;

    .line 113
    .line 114
    move-object/from16 p1, p36

    .line 115
    .line 116
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->J:LBc/a;

    .line 117
    .line 118
    move-object/from16 p1, p37

    .line 119
    .line 120
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->K:LBc/a;

    .line 121
    .line 122
    move-object/from16 p1, p38

    .line 123
    .line 124
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->L:LBc/a;

    .line 125
    .line 126
    move-object/from16 p1, p39

    .line 127
    .line 128
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->M:LBc/a;

    .line 129
    .line 130
    move-object/from16 p1, p40

    .line 131
    .line 132
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->N:LBc/a;

    .line 133
    .line 134
    move-object/from16 p1, p41

    .line 135
    .line 136
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->O:LBc/a;

    .line 137
    .line 138
    move-object/from16 p1, p42

    .line 139
    .line 140
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->P:LBc/a;

    .line 141
    .line 142
    move-object/from16 p1, p43

    .line 143
    .line 144
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Q:LBc/a;

    .line 145
    .line 146
    move-object/from16 p1, p44

    .line 147
    .line 148
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->R:LBc/a;

    .line 149
    .line 150
    move-object/from16 p1, p45

    .line 151
    .line 152
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->S:LBc/a;

    .line 153
    .line 154
    move-object/from16 p1, p46

    .line 155
    .line 156
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->T:LBc/a;

    .line 157
    .line 158
    move-object/from16 p1, p47

    .line 159
    .line 160
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->U:LBc/a;

    .line 161
    .line 162
    move-object/from16 p1, p48

    .line 163
    .line 164
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->V:LBc/a;

    .line 165
    .line 166
    move-object/from16 p1, p49

    .line 167
    .line 168
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->W:LBc/a;

    .line 169
    .line 170
    move-object/from16 p1, p50

    .line 171
    .line 172
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->X:LBc/a;

    .line 173
    .line 174
    move-object/from16 p1, p51

    .line 175
    .line 176
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Y:LBc/a;

    .line 177
    .line 178
    move-object/from16 p1, p52

    .line 179
    .line 180
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Z:LBc/a;

    .line 181
    .line 182
    move-object/from16 p1, p53

    .line 183
    .line 184
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->a0:LBc/a;

    .line 185
    .line 186
    move-object/from16 p1, p54

    .line 187
    .line 188
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->b0:LBc/a;

    .line 189
    .line 190
    move-object/from16 p1, p55

    .line 191
    .line 192
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->c0:LBc/a;

    .line 193
    .line 194
    move-object/from16 p1, p56

    .line 195
    .line 196
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->d0:LBc/a;

    .line 197
    .line 198
    move-object/from16 p1, p57

    .line 199
    .line 200
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->e0:LBc/a;

    .line 201
    .line 202
    move-object/from16 p1, p58

    .line 203
    .line 204
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->f0:LBc/a;

    .line 205
    .line 206
    move-object/from16 p1, p59

    .line 207
    .line 208
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->g0:LBc/a;

    .line 209
    .line 210
    move-object/from16 p1, p60

    .line 211
    .line 212
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->h0:LBc/a;

    .line 213
    .line 214
    move-object/from16 p1, p61

    .line 215
    .line 216
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->i0:LBc/a;

    .line 217
    .line 218
    move-object/from16 p1, p62

    .line 219
    .line 220
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->j0:LBc/a;

    .line 221
    .line 222
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/main_menu/impl/presentation/list/line/s;
    .locals 63
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;",
            "LBc/a<",
            "Lw30/s;",
            ">;",
            "LBc/a<",
            "Lw30/k;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/CyberAnalyticUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "LFi/b;",
            ">;",
            "LBc/a<",
            "Lek/a;",
            ">;",
            "LBc/a<",
            "Ld60/d;",
            ">;",
            "LBc/a<",
            "LJT/c;",
            ">;",
            "LBc/a<",
            "Lt30/b;",
            ">;",
            "LBc/a<",
            "LqS0/a;",
            ">;",
            "LBc/a<",
            "LTf0/a;",
            ">;",
            "LBc/a<",
            "LzV/a;",
            ">;",
            "LBc/a<",
            "Lgl0/a;",
            ">;",
            "LBc/a<",
            "Lnn0/f;",
            ">;",
            "LBc/a<",
            "LVg0/a;",
            ">;",
            "LBc/a<",
            "LbV0/a;",
            ">;",
            "LBc/a<",
            "LXV/a;",
            ">;",
            "LBc/a<",
            "Lok/a;",
            ">;",
            "LBc/a<",
            "Lnm/a;",
            ">;",
            "LBc/a<",
            "LS00/k;",
            ">;",
            "LBc/a<",
            "Lg60/a;",
            ">;",
            "LBc/a<",
            "LDU/a;",
            ">;",
            "LBc/a<",
            "LS00/e;",
            ">;",
            "LBc/a<",
            "LFI/d;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;",
            "LBc/a<",
            "Lfe0/a;",
            ">;",
            "LBc/a<",
            "LPo0/a;",
            ">;",
            "LBc/a<",
            "Lmo0/a;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "Lno0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/u0;",
            ">;",
            "LBc/a<",
            "LHg/d;",
            ">;",
            "LBc/a<",
            "LDg/c;",
            ">;",
            "LBc/a<",
            "LnR/b;",
            ">;",
            "LBc/a<",
            "LfS/a;",
            ">;",
            "LBc/a<",
            "LpS/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/c0;",
            ">;",
            "LBc/a<",
            "LkW0/a;",
            ">;",
            "LBc/a<",
            "Luk0/a;",
            ">;",
            "LBc/a<",
            "LZQ/a;",
            ">;",
            "LBc/a<",
            "LPu/a;",
            ">;",
            "LBc/a<",
            "LW81/a;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;",
            ">;",
            "LBc/a<",
            "LwX0/g;",
            ">;",
            "LBc/a<",
            "LMR/a;",
            ">;",
            "LBc/a<",
            "LTX/b;",
            ">;",
            "LBc/a<",
            "LTX/a;",
            ">;",
            "LBc/a<",
            "LVX/a;",
            ">;",
            "LBc/a<",
            "LKX/a;",
            ">;",
            "LBc/a<",
            "LMX/a;",
            ">;)",
            "Lorg/xbet/main_menu/impl/presentation/list/line/s;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    move-object/from16 v40, p39

    .line 82
    .line 83
    move-object/from16 v41, p40

    .line 84
    .line 85
    move-object/from16 v42, p41

    .line 86
    .line 87
    move-object/from16 v43, p42

    .line 88
    .line 89
    move-object/from16 v44, p43

    .line 90
    .line 91
    move-object/from16 v45, p44

    .line 92
    .line 93
    move-object/from16 v46, p45

    .line 94
    .line 95
    move-object/from16 v47, p46

    .line 96
    .line 97
    move-object/from16 v48, p47

    .line 98
    .line 99
    move-object/from16 v49, p48

    .line 100
    .line 101
    move-object/from16 v50, p49

    .line 102
    .line 103
    move-object/from16 v51, p50

    .line 104
    .line 105
    move-object/from16 v52, p51

    .line 106
    .line 107
    move-object/from16 v53, p52

    .line 108
    .line 109
    move-object/from16 v54, p53

    .line 110
    .line 111
    move-object/from16 v55, p54

    .line 112
    .line 113
    move-object/from16 v56, p55

    .line 114
    .line 115
    move-object/from16 v57, p56

    .line 116
    .line 117
    move-object/from16 v58, p57

    .line 118
    .line 119
    move-object/from16 v59, p58

    .line 120
    .line 121
    move-object/from16 v60, p59

    .line 122
    .line 123
    move-object/from16 v61, p60

    .line 124
    .line 125
    move-object/from16 v62, p61

    .line 126
    .line 127
    invoke-direct/range {v0 .. v62}, Lorg/xbet/main_menu/impl/presentation/list/line/s;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 128
    .line 129
    .line 130
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;Ldu/e;Lw30/s;Lw30/k;Lorg/xbet/main_menu/impl/domain/usecases/i;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/d;Lorg/xbet/analytics/domain/CyberAnalyticUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LFi/b;Lek/a;Ld60/d;LJT/c;Lt30/b;LqS0/a;LTf0/a;LzV/a;Lgl0/a;Lnn0/f;LVg0/a;LbV0/a;LXV/a;Lok/a;Lnm/a;LS00/k;Lg60/a;LDU/a;LS00/e;LFI/d;Lorg/xplatform/aggregator/api/navigation/a;Lfe0/a;LPo0/a;Lmo0/a;LwX0/a;Lno0/a;Lorg/xbet/analytics/domain/scope/u0;LHg/d;LDg/c;LnR/b;LfS/a;LpS/b;Lm8/a;LHX0/e;LfX/b;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/scope/c0;LkW0/a;Luk0/a;LZQ/a;LPu/a;LW81/a;Lfk/l;Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;LwX0/g;LMR/a;Lyb/a;Lyb/a;LVX/a;LKX/a;LMX/a;)Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;
    .locals 64
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/lifecycle/Q;",
            "Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;",
            "Ldu/e;",
            "Lw30/s;",
            "Lw30/k;",
            "Lorg/xbet/main_menu/impl/domain/usecases/i;",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            "Lorg/xbet/main_menu/impl/domain/usecases/d;",
            "Lorg/xbet/analytics/domain/CyberAnalyticUseCase;",
            "Lp9/c;",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            "LFi/b;",
            "Lek/a;",
            "Ld60/d;",
            "LJT/c;",
            "Lt30/b;",
            "LqS0/a;",
            "LTf0/a;",
            "LzV/a;",
            "Lgl0/a;",
            "Lnn0/f;",
            "LVg0/a;",
            "LbV0/a;",
            "LXV/a;",
            "Lok/a;",
            "Lnm/a;",
            "LS00/k;",
            "Lg60/a;",
            "LDU/a;",
            "LS00/e;",
            "LFI/d;",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            "Lfe0/a;",
            "LPo0/a;",
            "Lmo0/a;",
            "LwX0/a;",
            "Lno0/a;",
            "Lorg/xbet/analytics/domain/scope/u0;",
            "LHg/d;",
            "LDg/c;",
            "LnR/b;",
            "LfS/a;",
            "LpS/b;",
            "Lm8/a;",
            "LHX0/e;",
            "LfX/b;",
            "Lorg/xbet/ui_common/utils/M;",
            "LwX0/c;",
            "Lorg/xbet/analytics/domain/scope/c0;",
            "LkW0/a;",
            "Luk0/a;",
            "LZQ/a;",
            "LPu/a;",
            "LW81/a;",
            "Lfk/l;",
            "Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;",
            "LwX0/g;",
            "LMR/a;",
            "Lyb/a<",
            "LTX/b;",
            ">;",
            "Lyb/a<",
            "LTX/a;",
            ">;",
            "LVX/a;",
            "LKX/a;",
            "LMX/a;",
            ")",
            "Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    move-object/from16 v40, p39

    .line 82
    .line 83
    move-object/from16 v41, p40

    .line 84
    .line 85
    move-object/from16 v42, p41

    .line 86
    .line 87
    move-object/from16 v43, p42

    .line 88
    .line 89
    move-object/from16 v44, p43

    .line 90
    .line 91
    move-object/from16 v45, p44

    .line 92
    .line 93
    move-object/from16 v46, p45

    .line 94
    .line 95
    move-object/from16 v47, p46

    .line 96
    .line 97
    move-object/from16 v48, p47

    .line 98
    .line 99
    move-object/from16 v49, p48

    .line 100
    .line 101
    move-object/from16 v50, p49

    .line 102
    .line 103
    move-object/from16 v51, p50

    .line 104
    .line 105
    move-object/from16 v52, p51

    .line 106
    .line 107
    move-object/from16 v53, p52

    .line 108
    .line 109
    move-object/from16 v54, p53

    .line 110
    .line 111
    move-object/from16 v55, p54

    .line 112
    .line 113
    move-object/from16 v56, p55

    .line 114
    .line 115
    move-object/from16 v57, p56

    .line 116
    .line 117
    move-object/from16 v58, p57

    .line 118
    .line 119
    move-object/from16 v59, p58

    .line 120
    .line 121
    move-object/from16 v60, p59

    .line 122
    .line 123
    move-object/from16 v61, p60

    .line 124
    .line 125
    move-object/from16 v62, p61

    .line 126
    .line 127
    move-object/from16 v63, p62

    .line 128
    .line 129
    invoke-direct/range {v0 .. v63}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;-><init>(Landroidx/lifecycle/Q;Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;Ldu/e;Lw30/s;Lw30/k;Lorg/xbet/main_menu/impl/domain/usecases/i;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/d;Lorg/xbet/analytics/domain/CyberAnalyticUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LFi/b;Lek/a;Ld60/d;LJT/c;Lt30/b;LqS0/a;LTf0/a;LzV/a;Lgl0/a;Lnn0/f;LVg0/a;LbV0/a;LXV/a;Lok/a;Lnm/a;LS00/k;Lg60/a;LDU/a;LS00/e;LFI/d;Lorg/xplatform/aggregator/api/navigation/a;Lfe0/a;LPo0/a;Lmo0/a;LwX0/a;Lno0/a;Lorg/xbet/analytics/domain/scope/u0;LHg/d;LDg/c;LnR/b;LfS/a;LpS/b;Lm8/a;LHX0/e;LfX/b;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/scope/c0;LkW0/a;Luk0/a;LZQ/a;LPu/a;LW81/a;Lfk/l;Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;LwX0/g;LMR/a;Lyb/a;Lyb/a;LVX/a;LKX/a;LMX/a;)V

    .line 130
    .line 131
    .line 132
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;
    .locals 65

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v3, v1

    .line 10
    check-cast v3, Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v4, v1

    .line 19
    check-cast v4, Ldu/e;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v5, v1

    .line 28
    check-cast v5, Lw30/s;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v6, v1

    .line 37
    check-cast v6, Lw30/k;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v7, v1

    .line 46
    check-cast v7, Lorg/xbet/main_menu/impl/domain/usecases/i;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v8, v1

    .line 55
    check-cast v8, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, Lorg/xbet/main_menu/impl/domain/usecases/d;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v10, v1

    .line 73
    check-cast v10, Lorg/xbet/analytics/domain/CyberAnalyticUseCase;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v11, v1

    .line 82
    check-cast v11, Lp9/c;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v12, v1

    .line 91
    check-cast v12, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v13, v1

    .line 100
    check-cast v13, LFi/b;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    move-object v14, v1

    .line 109
    check-cast v14, Lek/a;

    .line 110
    .line 111
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->m:LBc/a;

    .line 112
    .line 113
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    move-object v15, v1

    .line 118
    check-cast v15, Ld60/d;

    .line 119
    .line 120
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->n:LBc/a;

    .line 121
    .line 122
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    move-object/from16 v16, v1

    .line 127
    .line 128
    check-cast v16, LJT/c;

    .line 129
    .line 130
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->o:LBc/a;

    .line 131
    .line 132
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    move-object/from16 v17, v1

    .line 137
    .line 138
    check-cast v17, Lt30/b;

    .line 139
    .line 140
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->p:LBc/a;

    .line 141
    .line 142
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    move-object/from16 v18, v1

    .line 147
    .line 148
    check-cast v18, LqS0/a;

    .line 149
    .line 150
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->q:LBc/a;

    .line 151
    .line 152
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    move-object/from16 v19, v1

    .line 157
    .line 158
    check-cast v19, LTf0/a;

    .line 159
    .line 160
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->r:LBc/a;

    .line 161
    .line 162
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 163
    .line 164
    .line 165
    move-result-object v1

    .line 166
    move-object/from16 v20, v1

    .line 167
    .line 168
    check-cast v20, LzV/a;

    .line 169
    .line 170
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->s:LBc/a;

    .line 171
    .line 172
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    move-object/from16 v21, v1

    .line 177
    .line 178
    check-cast v21, Lgl0/a;

    .line 179
    .line 180
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->t:LBc/a;

    .line 181
    .line 182
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    move-object/from16 v22, v1

    .line 187
    .line 188
    check-cast v22, Lnn0/f;

    .line 189
    .line 190
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->u:LBc/a;

    .line 191
    .line 192
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    move-object/from16 v23, v1

    .line 197
    .line 198
    check-cast v23, LVg0/a;

    .line 199
    .line 200
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->v:LBc/a;

    .line 201
    .line 202
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    move-object/from16 v24, v1

    .line 207
    .line 208
    check-cast v24, LbV0/a;

    .line 209
    .line 210
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->w:LBc/a;

    .line 211
    .line 212
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v1

    .line 216
    move-object/from16 v25, v1

    .line 217
    .line 218
    check-cast v25, LXV/a;

    .line 219
    .line 220
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->x:LBc/a;

    .line 221
    .line 222
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    move-object/from16 v26, v1

    .line 227
    .line 228
    check-cast v26, Lok/a;

    .line 229
    .line 230
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->y:LBc/a;

    .line 231
    .line 232
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v1

    .line 236
    move-object/from16 v27, v1

    .line 237
    .line 238
    check-cast v27, Lnm/a;

    .line 239
    .line 240
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->z:LBc/a;

    .line 241
    .line 242
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v1

    .line 246
    move-object/from16 v28, v1

    .line 247
    .line 248
    check-cast v28, LS00/k;

    .line 249
    .line 250
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->A:LBc/a;

    .line 251
    .line 252
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    move-object/from16 v29, v1

    .line 257
    .line 258
    check-cast v29, Lg60/a;

    .line 259
    .line 260
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->B:LBc/a;

    .line 261
    .line 262
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 263
    .line 264
    .line 265
    move-result-object v1

    .line 266
    move-object/from16 v30, v1

    .line 267
    .line 268
    check-cast v30, LDU/a;

    .line 269
    .line 270
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->C:LBc/a;

    .line 271
    .line 272
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v1

    .line 276
    move-object/from16 v31, v1

    .line 277
    .line 278
    check-cast v31, LS00/e;

    .line 279
    .line 280
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->D:LBc/a;

    .line 281
    .line 282
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 283
    .line 284
    .line 285
    move-result-object v1

    .line 286
    move-object/from16 v32, v1

    .line 287
    .line 288
    check-cast v32, LFI/d;

    .line 289
    .line 290
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->E:LBc/a;

    .line 291
    .line 292
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 293
    .line 294
    .line 295
    move-result-object v1

    .line 296
    move-object/from16 v33, v1

    .line 297
    .line 298
    check-cast v33, Lorg/xplatform/aggregator/api/navigation/a;

    .line 299
    .line 300
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->F:LBc/a;

    .line 301
    .line 302
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 303
    .line 304
    .line 305
    move-result-object v1

    .line 306
    move-object/from16 v34, v1

    .line 307
    .line 308
    check-cast v34, Lfe0/a;

    .line 309
    .line 310
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->G:LBc/a;

    .line 311
    .line 312
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 313
    .line 314
    .line 315
    move-result-object v1

    .line 316
    move-object/from16 v35, v1

    .line 317
    .line 318
    check-cast v35, LPo0/a;

    .line 319
    .line 320
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->H:LBc/a;

    .line 321
    .line 322
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 323
    .line 324
    .line 325
    move-result-object v1

    .line 326
    move-object/from16 v36, v1

    .line 327
    .line 328
    check-cast v36, Lmo0/a;

    .line 329
    .line 330
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->I:LBc/a;

    .line 331
    .line 332
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 333
    .line 334
    .line 335
    move-result-object v1

    .line 336
    move-object/from16 v37, v1

    .line 337
    .line 338
    check-cast v37, LwX0/a;

    .line 339
    .line 340
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->J:LBc/a;

    .line 341
    .line 342
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 343
    .line 344
    .line 345
    move-result-object v1

    .line 346
    move-object/from16 v38, v1

    .line 347
    .line 348
    check-cast v38, Lno0/a;

    .line 349
    .line 350
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->K:LBc/a;

    .line 351
    .line 352
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 353
    .line 354
    .line 355
    move-result-object v1

    .line 356
    move-object/from16 v39, v1

    .line 357
    .line 358
    check-cast v39, Lorg/xbet/analytics/domain/scope/u0;

    .line 359
    .line 360
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->L:LBc/a;

    .line 361
    .line 362
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 363
    .line 364
    .line 365
    move-result-object v1

    .line 366
    move-object/from16 v40, v1

    .line 367
    .line 368
    check-cast v40, LHg/d;

    .line 369
    .line 370
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->M:LBc/a;

    .line 371
    .line 372
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 373
    .line 374
    .line 375
    move-result-object v1

    .line 376
    move-object/from16 v41, v1

    .line 377
    .line 378
    check-cast v41, LDg/c;

    .line 379
    .line 380
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->N:LBc/a;

    .line 381
    .line 382
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 383
    .line 384
    .line 385
    move-result-object v1

    .line 386
    move-object/from16 v42, v1

    .line 387
    .line 388
    check-cast v42, LnR/b;

    .line 389
    .line 390
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->O:LBc/a;

    .line 391
    .line 392
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 393
    .line 394
    .line 395
    move-result-object v1

    .line 396
    move-object/from16 v43, v1

    .line 397
    .line 398
    check-cast v43, LfS/a;

    .line 399
    .line 400
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->P:LBc/a;

    .line 401
    .line 402
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 403
    .line 404
    .line 405
    move-result-object v1

    .line 406
    move-object/from16 v44, v1

    .line 407
    .line 408
    check-cast v44, LpS/b;

    .line 409
    .line 410
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Q:LBc/a;

    .line 411
    .line 412
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 413
    .line 414
    .line 415
    move-result-object v1

    .line 416
    move-object/from16 v45, v1

    .line 417
    .line 418
    check-cast v45, Lm8/a;

    .line 419
    .line 420
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->R:LBc/a;

    .line 421
    .line 422
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 423
    .line 424
    .line 425
    move-result-object v1

    .line 426
    move-object/from16 v46, v1

    .line 427
    .line 428
    check-cast v46, LHX0/e;

    .line 429
    .line 430
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->S:LBc/a;

    .line 431
    .line 432
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 433
    .line 434
    .line 435
    move-result-object v1

    .line 436
    move-object/from16 v47, v1

    .line 437
    .line 438
    check-cast v47, LfX/b;

    .line 439
    .line 440
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->T:LBc/a;

    .line 441
    .line 442
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 443
    .line 444
    .line 445
    move-result-object v1

    .line 446
    move-object/from16 v48, v1

    .line 447
    .line 448
    check-cast v48, Lorg/xbet/ui_common/utils/M;

    .line 449
    .line 450
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->U:LBc/a;

    .line 451
    .line 452
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 453
    .line 454
    .line 455
    move-result-object v1

    .line 456
    move-object/from16 v49, v1

    .line 457
    .line 458
    check-cast v49, LwX0/c;

    .line 459
    .line 460
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->V:LBc/a;

    .line 461
    .line 462
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 463
    .line 464
    .line 465
    move-result-object v1

    .line 466
    move-object/from16 v50, v1

    .line 467
    .line 468
    check-cast v50, Lorg/xbet/analytics/domain/scope/c0;

    .line 469
    .line 470
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->W:LBc/a;

    .line 471
    .line 472
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 473
    .line 474
    .line 475
    move-result-object v1

    .line 476
    move-object/from16 v51, v1

    .line 477
    .line 478
    check-cast v51, LkW0/a;

    .line 479
    .line 480
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->X:LBc/a;

    .line 481
    .line 482
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 483
    .line 484
    .line 485
    move-result-object v1

    .line 486
    move-object/from16 v52, v1

    .line 487
    .line 488
    check-cast v52, Luk0/a;

    .line 489
    .line 490
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Y:LBc/a;

    .line 491
    .line 492
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 493
    .line 494
    .line 495
    move-result-object v1

    .line 496
    move-object/from16 v53, v1

    .line 497
    .line 498
    check-cast v53, LZQ/a;

    .line 499
    .line 500
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->Z:LBc/a;

    .line 501
    .line 502
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 503
    .line 504
    .line 505
    move-result-object v1

    .line 506
    move-object/from16 v54, v1

    .line 507
    .line 508
    check-cast v54, LPu/a;

    .line 509
    .line 510
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->a0:LBc/a;

    .line 511
    .line 512
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 513
    .line 514
    .line 515
    move-result-object v1

    .line 516
    move-object/from16 v55, v1

    .line 517
    .line 518
    check-cast v55, LW81/a;

    .line 519
    .line 520
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->b0:LBc/a;

    .line 521
    .line 522
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 523
    .line 524
    .line 525
    move-result-object v1

    .line 526
    move-object/from16 v56, v1

    .line 527
    .line 528
    check-cast v56, Lfk/l;

    .line 529
    .line 530
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->c0:LBc/a;

    .line 531
    .line 532
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 533
    .line 534
    .line 535
    move-result-object v1

    .line 536
    move-object/from16 v57, v1

    .line 537
    .line 538
    check-cast v57, Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;

    .line 539
    .line 540
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->d0:LBc/a;

    .line 541
    .line 542
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 543
    .line 544
    .line 545
    move-result-object v1

    .line 546
    move-object/from16 v58, v1

    .line 547
    .line 548
    check-cast v58, LwX0/g;

    .line 549
    .line 550
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->e0:LBc/a;

    .line 551
    .line 552
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 553
    .line 554
    .line 555
    move-result-object v1

    .line 556
    move-object/from16 v59, v1

    .line 557
    .line 558
    check-cast v59, LMR/a;

    .line 559
    .line 560
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->f0:LBc/a;

    .line 561
    .line 562
    invoke-static {v1}, Ldagger/internal/c;->a(LBc/a;)Lyb/a;

    .line 563
    .line 564
    .line 565
    move-result-object v60

    .line 566
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->g0:LBc/a;

    .line 567
    .line 568
    invoke-static {v1}, Ldagger/internal/c;->a(LBc/a;)Lyb/a;

    .line 569
    .line 570
    .line 571
    move-result-object v61

    .line 572
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->h0:LBc/a;

    .line 573
    .line 574
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 575
    .line 576
    .line 577
    move-result-object v1

    .line 578
    move-object/from16 v62, v1

    .line 579
    .line 580
    check-cast v62, LVX/a;

    .line 581
    .line 582
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->i0:LBc/a;

    .line 583
    .line 584
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 585
    .line 586
    .line 587
    move-result-object v1

    .line 588
    move-object/from16 v63, v1

    .line 589
    .line 590
    check-cast v63, LKX/a;

    .line 591
    .line 592
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/list/line/s;->j0:LBc/a;

    .line 593
    .line 594
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 595
    .line 596
    .line 597
    move-result-object v1

    .line 598
    move-object/from16 v64, v1

    .line 599
    .line 600
    check-cast v64, LMX/a;

    .line 601
    .line 602
    move-object/from16 v2, p1

    .line 603
    .line 604
    invoke-static/range {v2 .. v64}, Lorg/xbet/main_menu/impl/presentation/list/line/s;->c(Landroidx/lifecycle/Q;Lorg/xbet/main_menu/impl/domain/scenario/GetMenuSectionsMapScenario;Ldu/e;Lw30/s;Lw30/k;Lorg/xbet/main_menu/impl/domain/usecases/i;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/d;Lorg/xbet/analytics/domain/CyberAnalyticUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LFi/b;Lek/a;Ld60/d;LJT/c;Lt30/b;LqS0/a;LTf0/a;LzV/a;Lgl0/a;Lnn0/f;LVg0/a;LbV0/a;LXV/a;Lok/a;Lnm/a;LS00/k;Lg60/a;LDU/a;LS00/e;LFI/d;Lorg/xplatform/aggregator/api/navigation/a;Lfe0/a;LPo0/a;Lmo0/a;LwX0/a;Lno0/a;Lorg/xbet/analytics/domain/scope/u0;LHg/d;LDg/c;LnR/b;LfS/a;LpS/b;Lm8/a;LHX0/e;LfX/b;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/scope/c0;LkW0/a;Luk0/a;LZQ/a;LPu/a;LW81/a;Lfk/l;Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;LwX0/g;LMR/a;Lyb/a;Lyb/a;LVX/a;LKX/a;LMX/a;)Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 605
    .line 606
    .line 607
    move-result-object v1

    .line 608
    return-object v1
.end method
