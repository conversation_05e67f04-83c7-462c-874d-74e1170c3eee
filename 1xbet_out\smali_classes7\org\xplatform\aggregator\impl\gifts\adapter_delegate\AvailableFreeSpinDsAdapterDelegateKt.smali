.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aM\u0010\u000c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000b0\n0\t2\"\u0010\u0006\u001a\u001e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0000j\u0002`\u00052\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0007H\u0000\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lkotlin/Function3;",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
        "Lha1/b;",
        "",
        "Lorg/xplatform/aggregator/impl/gifts/adapter/StateCallback;",
        "stateCallback",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "LA4/c;",
        "",
        "LVX0/i;",
        "f",
        "(LOc/n;Lkotlin/jvm/functions/Function0;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;->k(LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Lkotlin/jvm/functions/Function0;LOc/n;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;->i(LB4/a;Lkotlin/jvm/functions/Function0;LOc/n;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;->j(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function0;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;->h(Lkotlin/jvm/functions/Function0;LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;

    move-result-object p0

    return-object p0
.end method

.method public static final f(LOc/n;Lkotlin/jvm/functions/Function0;)LA4/c;
    .locals 3
    .param p0    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Lorg/xplatform/aggregator/api/model/PartitionType;",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
            "-",
            "Lha1/b;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/m;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/m;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/n;

    .line 7
    .line 8
    invoke-direct {v1, p1, p0}, Lba1/n;-><init>(Lkotlin/jvm/functions/Function0;LOc/n;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt$getAvailableFreeSpinDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt$getAvailableFreeSpinDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt$getAvailableFreeSpinDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableFreeSpinDsAdapterDelegateKt$getAvailableFreeSpinDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/n1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/n1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function0;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lba1/o;

    .line 2
    .line 3
    invoke-direct {v0, p2, p0, p1}, Lba1/o;-><init>(LB4/a;Lkotlin/jvm/functions/Function0;LOc/n;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p2, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final i(LB4/a;Lkotlin/jvm/functions/Function0;LOc/n;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, LS91/n1;

    .line 6
    .line 7
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lma1/e;

    .line 16
    .line 17
    invoke-virtual {v0}, Lma1/e;->e()Lf21/b;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Lf21/b;->g()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p3, v0}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setStyle(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 29
    .line 30
    .line 31
    move-result-object p3

    .line 32
    check-cast p3, LS91/n1;

    .line 33
    .line 34
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 35
    .line 36
    .line 37
    move-result-object p3

    .line 38
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    check-cast v0, Lma1/e;

    .line 43
    .line 44
    invoke-virtual {v0}, Lma1/e;->e()Lf21/b;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    check-cast v1, Lma1/e;

    .line 53
    .line 54
    invoke-virtual {v1}, Lma1/e;->d()Lf21/a;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {p3, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setModel(Lf21/b;Lf21/a;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 62
    .line 63
    .line 64
    move-result-object p3

    .line 65
    check-cast p3, LS91/n1;

    .line 66
    .line 67
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 68
    .line 69
    .line 70
    move-result-object p3

    .line 71
    new-instance v0, Lba1/p;

    .line 72
    .line 73
    invoke-direct {v0, p1}, Lba1/p;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p3, v0}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setStopTimerSubject(Lkotlin/jvm/functions/Function0;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    check-cast p1, LS91/n1;

    .line 84
    .line 85
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    new-instance p3, Lba1/q;

    .line 90
    .line 91
    invoke-direct {p3, p2, p0}, Lba1/q;-><init>(LOc/n;LB4/a;)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setBottomButtonListener(Lkotlin/jvm/functions/Function0;)V

    .line 95
    .line 96
    .line 97
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 98
    .line 99
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final k(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 8

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->PLAY_GAME:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    .line 4
    .line 5
    new-instance v2, Lha1/b;

    .line 6
    .line 7
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    check-cast v3, Lma1/e;

    .line 12
    .line 13
    invoke-virtual {v3}, Lma1/e;->f()Lxa1/f;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    invoke-virtual {v3}, Lxa1/f;->c()I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    check-cast p1, Lma1/e;

    .line 26
    .line 27
    invoke-virtual {p1}, Lma1/e;->f()Lxa1/f;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-virtual {p1}, Lxa1/f;->d()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    const/4 v6, 0x4

    .line 36
    const/4 v7, 0x0

    .line 37
    const/4 v5, 0x0

    .line 38
    invoke-direct/range {v2 .. v7}, Lha1/b;-><init>(ILjava/lang/String;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    .line 40
    .line 41
    invoke-interface {p0, v0, v1, v2}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 45
    .line 46
    return-object p0
.end method
