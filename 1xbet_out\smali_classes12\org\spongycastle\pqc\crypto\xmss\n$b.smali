.class public Lorg/spongycastle/pqc/crypto/xmss/n$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/pqc/crypto/xmss/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/l;

.field public b:[B

.field public c:[B

.field public d:[B


# direct methods
.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/l;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->b:[B

    .line 6
    .line 7
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->c:[B

    .line 8
    .line 9
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->d:[B

    .line 10
    .line 11
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->a:Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 12
    .line 13
    return-void
.end method

.method public static synthetic a(Lorg/spongycastle/pqc/crypto/xmss/n$b;)Lorg/spongycastle/pqc/crypto/xmss/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->a:Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lorg/spongycastle/pqc/crypto/xmss/n$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->d:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lorg/spongycastle/pqc/crypto/xmss/n$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->b:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lorg/spongycastle/pqc/crypto/xmss/n$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->c:[B

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public e()Lorg/spongycastle/pqc/crypto/xmss/n;
    .locals 2

    .line 1
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Lorg/spongycastle/pqc/crypto/xmss/n;-><init>(Lorg/spongycastle/pqc/crypto/xmss/n$b;Lorg/spongycastle/pqc/crypto/xmss/n$a;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method

.method public f([B)Lorg/spongycastle/pqc/crypto/xmss/n$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->c:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public g([B)Lorg/spongycastle/pqc/crypto/xmss/n$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/n$b;->b:[B

    .line 6
    .line 7
    return-object p0
.end method
