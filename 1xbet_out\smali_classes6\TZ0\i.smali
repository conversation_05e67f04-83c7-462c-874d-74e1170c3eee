.class public final synthetic LTZ0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/dialog/DialogView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/dialog/DialogView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTZ0/i;->a:Lorg/xbet/uikit/components/dialog/DialogView;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/i;->a:Lorg/xbet/uikit/components/dialog/DialogView;

    invoke-static {v0}, Lorg/xbet/uikit/components/dialog/DialogView;->a(Lorg/xbet/uikit/components/dialog/DialogView;)V

    return-void
.end method
