.class public final Lh2/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/Q;


# static fields
.field public static final a:Lh2/o;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lh2/o;

    .line 2
    .line 3
    invoke-direct {v0}, Lh2/o;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lh2/o;->a:Lh2/o;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
