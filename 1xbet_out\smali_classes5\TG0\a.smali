.class public final synthetic LTG0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LUG0/a;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LUG0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTG0/a;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LTG0/a;->b:LUG0/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LTG0/a;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LTG0/a;->b:LUG0/a;

    invoke-static {v0, v1}, LTG0/c;->a(Lkotlin/jvm/functions/Function1;LUG0/a;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
