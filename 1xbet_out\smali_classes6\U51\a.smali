.class public final LU51/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LV51/b;",
        "",
        "show24",
        "LP51/a;",
        "a",
        "(LV51/b;Z)LP51/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LV51/b;Z)LP51/a;
    .locals 15
    .param p0    # LV51/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LV51/b;->f()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    if-eqz v0, :cond_5

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v4

    .line 13
    invoke-virtual {p0}, LV51/b;->d()Ljava/lang/Double;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    if-eqz v0, :cond_4

    .line 18
    .line 19
    invoke-virtual {v0}, Ljava/lang/Double;->doubleValue()D

    .line 20
    .line 21
    .line 22
    move-result-wide v5

    .line 23
    invoke-virtual {p0}, LV51/b;->e()Ljava/lang/Double;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    if-eqz v0, :cond_3

    .line 28
    .line 29
    invoke-virtual {v0}, Ljava/lang/Double;->doubleValue()D

    .line 30
    .line 31
    .line 32
    move-result-wide v7

    .line 33
    invoke-virtual {p0}, LV51/b;->h()Ljava/lang/Integer;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    if-eqz v0, :cond_2

    .line 38
    .line 39
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 40
    .line 41
    .line 42
    move-result v9

    .line 43
    invoke-virtual {p0}, LV51/b;->f()Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    sget-object v3, Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;->Companion:Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel$a;

    .line 48
    .line 49
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    invoke-virtual {v3, v0}, Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel$a;->a(I)Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;

    .line 54
    .line 55
    .line 56
    move-result-object v10

    .line 57
    invoke-virtual {p0}, LV51/b;->i()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v11

    .line 61
    if-eqz v11, :cond_1

    .line 62
    .line 63
    invoke-virtual {p0}, LV51/b;->g()Ljava/lang/Long;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    if-eqz p0, :cond_0

    .line 68
    .line 69
    invoke-virtual {p0}, Ljava/lang/Long;->longValue()J

    .line 70
    .line 71
    .line 72
    move-result-wide v12

    .line 73
    new-instance v3, LP51/a;

    .line 74
    .line 75
    move/from16 v14, p1

    .line 76
    .line 77
    invoke-direct/range {v3 .. v14}, LP51/a;-><init>(IDDILorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;Ljava/lang/String;JZ)V

    .line 78
    .line 79
    .line 80
    return-object v3

    .line 81
    :cond_0
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 82
    .line 83
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 84
    .line 85
    .line 86
    throw p0

    .line 87
    :cond_1
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 88
    .line 89
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 90
    .line 91
    .line 92
    throw p0

    .line 93
    :cond_2
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 94
    .line 95
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 96
    .line 97
    .line 98
    throw p0

    .line 99
    :cond_3
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 100
    .line 101
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 102
    .line 103
    .line 104
    throw p0

    .line 105
    :cond_4
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 106
    .line 107
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 108
    .line 109
    .line 110
    throw p0

    .line 111
    :cond_5
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 112
    .line 113
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 114
    .line 115
    .line 116
    throw p0
.end method
