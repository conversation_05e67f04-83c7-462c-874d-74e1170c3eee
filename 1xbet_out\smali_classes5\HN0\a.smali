.class public final LHN0/a;
.super LA4/f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/f<",
        "Ljava/util/List<",
        "+",
        "LNN0/j;",
        ">;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001B\u001b\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LHN0/a;",
        "LA4/f;",
        "",
        "LNN0/j;",
        "Lkotlin/Function1;",
        "",
        "",
        "tabUiModelPosition",
        "<init>",
        "(Lkotlin/jvm/functions/Function1;)V",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, LA4/f;-><init>()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, LA4/a;->d:LA4/d;

    .line 5
    .line 6
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/presentation/delegates/ChipsAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 11
    .line 12
    .line 13
    return-void
.end method
