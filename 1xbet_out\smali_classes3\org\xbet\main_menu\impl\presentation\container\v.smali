.class public final synthetic Lorg/xbet/main_menu/impl/presentation/container/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/fragment/app/J;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/v;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/v;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    invoke-static {v0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->D2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method
