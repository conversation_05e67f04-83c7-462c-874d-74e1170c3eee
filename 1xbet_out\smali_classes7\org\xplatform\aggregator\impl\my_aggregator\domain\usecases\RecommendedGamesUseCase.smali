.class public final Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u0000 \u00112\u00020\u0001:\u0001\u000eB\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J.\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0086B\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0010\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;",
        "",
        "Lu81/b;",
        "repository",
        "<init>",
        "(Lu81/b;)V",
        "",
        "partitionId",
        "",
        "endPoint",
        "",
        "hasAggregatorBrands",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(JLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lu81/b;",
        "b",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;->b:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$a;

    return-void
.end method

.method public constructor <init>(Lu81/b;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;->a:Lu81/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(JLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 23
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p5

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v6, 0x0

    .line 41
    const/4 v7, 0x1

    .line 42
    if-eqz v4, :cond_4

    .line 43
    .line 44
    if-eq v4, v7, :cond_3

    .line 45
    .line 46
    if-ne v4, v5, :cond_2

    .line 47
    .line 48
    iget v4, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$1:I

    .line 49
    .line 50
    iget v8, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$0:I

    .line 51
    .line 52
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->Z$0:Z

    .line 53
    .line 54
    iget-wide v10, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->J$0:J

    .line 55
    .line 56
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$1:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v12, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;

    .line 59
    .line 60
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$0:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v13, Ljava/lang/String;

    .line 63
    .line 64
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    :cond_1
    move-wide/from16 v21, v10

    .line 68
    .line 69
    move-object v10, v1

    .line 70
    move v11, v4

    .line 71
    move v4, v9

    .line 72
    move-object v1, v13

    .line 73
    move-object v13, v12

    .line 74
    move v12, v8

    .line 75
    move-wide/from16 v8, v21

    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 79
    .line 80
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 81
    .line 82
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    throw v0

    .line 86
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$1:I

    .line 87
    .line 88
    iget v8, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$0:I

    .line 89
    .line 90
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->Z$0:Z

    .line 91
    .line 92
    iget-wide v10, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->J$0:J

    .line 93
    .line 94
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$1:Ljava/lang/Object;

    .line 95
    .line 96
    check-cast v12, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;

    .line 97
    .line 98
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$0:Ljava/lang/Object;

    .line 99
    .line 100
    check-cast v13, Ljava/lang/String;

    .line 101
    .line 102
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 103
    .line 104
    .line 105
    goto :goto_2

    .line 106
    :catchall_0
    move-exception v0

    .line 107
    goto/16 :goto_4

    .line 108
    .line 109
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 110
    .line 111
    .line 112
    move-wide/from16 v8, p1

    .line 113
    .line 114
    move/from16 v4, p4

    .line 115
    .line 116
    move-object v10, v1

    .line 117
    move-object v13, v2

    .line 118
    const/4 v11, 0x0

    .line 119
    const/4 v12, 0x0

    .line 120
    move-object/from16 v1, p3

    .line 121
    .line 122
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 123
    .line 124
    iget-object v14, v13, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;->a:Lu81/b;

    .line 125
    .line 126
    iput-object v1, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$0:Ljava/lang/Object;

    .line 127
    .line 128
    iput-object v13, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$1:Ljava/lang/Object;

    .line 129
    .line 130
    iput-wide v8, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->J$0:J

    .line 131
    .line 132
    iput-boolean v4, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->Z$0:Z

    .line 133
    .line 134
    iput v12, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$0:I

    .line 135
    .line 136
    iput v11, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$1:I

    .line 137
    .line 138
    iput v7, v10, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 139
    .line 140
    const/16 v15, 0x8

    .line 141
    .line 142
    move-object/from16 v18, v1

    .line 143
    .line 144
    move/from16 v19, v4

    .line 145
    .line 146
    move-wide/from16 v16, v8

    .line 147
    .line 148
    move-object/from16 v20, v10

    .line 149
    .line 150
    :try_start_2
    invoke-interface/range {v14 .. v20}, Lu81/b;->c(IJLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 154
    if-ne v0, v3, :cond_5

    .line 155
    .line 156
    goto/16 :goto_8

    .line 157
    .line 158
    :cond_5
    move v4, v11

    .line 159
    move v8, v12

    .line 160
    move-object v12, v13

    .line 161
    move-wide/from16 v10, v16

    .line 162
    .line 163
    move-object/from16 v13, v18

    .line 164
    .line 165
    move/from16 v9, v19

    .line 166
    .line 167
    move-object/from16 v1, v20

    .line 168
    .line 169
    :goto_2
    :try_start_3
    check-cast v0, Ljava/util/List;

    .line 170
    .line 171
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 172
    .line 173
    .line 174
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 175
    goto/16 :goto_a

    .line 176
    .line 177
    :catchall_1
    move-exception v0

    .line 178
    :goto_3
    move v4, v11

    .line 179
    move v8, v12

    .line 180
    move-object v12, v13

    .line 181
    move-wide/from16 v10, v16

    .line 182
    .line 183
    move-object/from16 v13, v18

    .line 184
    .line 185
    move/from16 v9, v19

    .line 186
    .line 187
    move-object/from16 v1, v20

    .line 188
    .line 189
    goto :goto_4

    .line 190
    :catchall_2
    move-exception v0

    .line 191
    move-object/from16 v18, v1

    .line 192
    .line 193
    move/from16 v19, v4

    .line 194
    .line 195
    move-wide/from16 v16, v8

    .line 196
    .line 197
    move-object/from16 v20, v10

    .line 198
    .line 199
    goto :goto_3

    .line 200
    :goto_4
    if-eqz v8, :cond_6

    .line 201
    .line 202
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 203
    .line 204
    if-eqz v14, :cond_6

    .line 205
    .line 206
    move-object v14, v0

    .line 207
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 208
    .line 209
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 210
    .line 211
    .line 212
    move-result v14

    .line 213
    if-eqz v14, :cond_6

    .line 214
    .line 215
    const/4 v14, 0x1

    .line 216
    goto :goto_5

    .line 217
    :cond_6
    const/4 v14, 0x0

    .line 218
    :goto_5
    instance-of v15, v0, Ljava/util/concurrent/CancellationException;

    .line 219
    .line 220
    if-nez v15, :cond_e

    .line 221
    .line 222
    instance-of v15, v0, Ljava/net/ConnectException;

    .line 223
    .line 224
    if-nez v15, :cond_e

    .line 225
    .line 226
    if-nez v14, :cond_e

    .line 227
    .line 228
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 229
    .line 230
    if-eqz v14, :cond_9

    .line 231
    .line 232
    move-object v14, v0

    .line 233
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 234
    .line 235
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 236
    .line 237
    .line 238
    move-result v15

    .line 239
    if-nez v15, :cond_8

    .line 240
    .line 241
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 242
    .line 243
    .line 244
    move-result v14

    .line 245
    if-eqz v14, :cond_7

    .line 246
    .line 247
    goto :goto_6

    .line 248
    :cond_7
    const/4 v14, 0x0

    .line 249
    goto :goto_7

    .line 250
    :cond_8
    :goto_6
    const/4 v14, 0x1

    .line 251
    goto :goto_7

    .line 252
    :cond_9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 253
    .line 254
    .line 255
    move-result v14

    .line 256
    if-nez v14, :cond_7

    .line 257
    .line 258
    goto :goto_6

    .line 259
    :goto_7
    add-int/2addr v4, v7

    .line 260
    const/4 v15, 0x3

    .line 261
    if-gt v4, v15, :cond_b

    .line 262
    .line 263
    if-eqz v14, :cond_a

    .line 264
    .line 265
    goto :goto_9

    .line 266
    :cond_a
    new-instance v14, Ljava/lang/StringBuilder;

    .line 267
    .line 268
    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    .line 269
    .line 270
    .line 271
    const-string v15, "error ("

    .line 272
    .line 273
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 274
    .line 275
    .line 276
    invoke-virtual {v14, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 277
    .line 278
    .line 279
    const-string v15, "): "

    .line 280
    .line 281
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 282
    .line 283
    .line 284
    invoke-virtual {v14, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 285
    .line 286
    .line 287
    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 288
    .line 289
    .line 290
    move-result-object v0

    .line 291
    sget-object v14, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 292
    .line 293
    invoke-virtual {v14, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 294
    .line 295
    .line 296
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$0:Ljava/lang/Object;

    .line 297
    .line 298
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->L$1:Ljava/lang/Object;

    .line 299
    .line 300
    iput-wide v10, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->J$0:J

    .line 301
    .line 302
    iput-boolean v9, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->Z$0:Z

    .line 303
    .line 304
    iput v8, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$0:I

    .line 305
    .line 306
    iput v4, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->I$1:I

    .line 307
    .line 308
    iput v5, v1, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    .line 309
    .line 310
    const-wide/16 v14, 0xbb8

    .line 311
    .line 312
    invoke-static {v14, v15, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 313
    .line 314
    .line 315
    move-result-object v0

    .line 316
    if-ne v0, v3, :cond_1

    .line 317
    .line 318
    :goto_8
    return-object v3

    .line 319
    :cond_b
    :goto_9
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 320
    .line 321
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 322
    .line 323
    .line 324
    move-result-object v0

    .line 325
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 326
    .line 327
    .line 328
    move-result-object v0

    .line 329
    :goto_a
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 330
    .line 331
    .line 332
    move-result v1

    .line 333
    if-eqz v1, :cond_c

    .line 334
    .line 335
    const/4 v0, 0x0

    .line 336
    :cond_c
    check-cast v0, Ljava/util/List;

    .line 337
    .line 338
    if-nez v0, :cond_d

    .line 339
    .line 340
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 341
    .line 342
    .line 343
    move-result-object v0

    .line 344
    :cond_d
    return-object v0

    .line 345
    :cond_e
    throw v0
.end method
