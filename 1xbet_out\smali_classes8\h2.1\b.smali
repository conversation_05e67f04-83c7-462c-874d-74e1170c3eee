.class public final Lh2/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lh2/b$k;,
        Lh2/b$e;,
        Lh2/b$h;,
        Lh2/b$i;,
        Lh2/b$j;,
        Lh2/b$f;,
        Lh2/b$b;,
        Lh2/b$l;,
        Lh2/b$d;,
        Lh2/b$g;,
        Lh2/b$c;,
        Lh2/b$a;
    }
.end annotation


# static fields
.field public static final a:[B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-string v0, "OpusHead"

    .line 2
    .line 3
    invoke-static {v0}, Lt1/a0;->u0(Ljava/lang/String;)[B

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sput-object v0, Lh2/b;->a:[B

    .line 8
    .line 9
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static A(Lh2/t;Landroidx/media3/container/d$b;LN1/E;)Lh2/w;
    .locals 36
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    move-object/from16 v1, p0

    move-object/from16 v0, p1

    const v3, 0x7374737a

    .line 1
    invoke-virtual {v0, v3}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 2
    new-instance v5, Lh2/b$i;

    iget-object v6, v1, Lh2/t;->g:Landroidx/media3/common/r;

    invoke-direct {v5, v3, v6}, Lh2/b$i;-><init>(Landroidx/media3/container/d$c;Landroidx/media3/common/r;)V

    goto :goto_0

    :cond_0
    const v3, 0x73747a32

    .line 3
    invoke-virtual {v0, v3}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v3

    if-eqz v3, :cond_33

    .line 4
    new-instance v5, Lh2/b$j;

    invoke-direct {v5, v3}, Lh2/b$j;-><init>(Landroidx/media3/container/d$c;)V

    .line 5
    :goto_0
    invoke-interface {v5}, Lh2/b$f;->b()I

    move-result v3

    const/4 v6, 0x0

    if-nez v3, :cond_1

    .line 6
    new-instance v0, Lh2/w;

    new-array v2, v6, [J

    new-array v3, v6, [I

    new-array v5, v6, [J

    new-array v6, v6, [I

    const-wide/16 v7, 0x0

    const/4 v4, 0x0

    invoke-direct/range {v0 .. v8}, Lh2/w;-><init>(Lh2/t;[J[II[J[IJ)V

    return-object v0

    .line 7
    :cond_1
    iget v7, v1, Lh2/t;->b:I

    const/4 v8, 0x2

    const-wide/16 v9, 0x0

    if-ne v7, v8, :cond_2

    iget-wide v11, v1, Lh2/t;->f:J

    cmp-long v7, v11, v9

    if-lez v7, :cond_2

    int-to-float v7, v3

    long-to-float v11, v11

    const v12, 0x49742400    # 1000000.0f

    div-float/2addr v11, v12

    div-float/2addr v7, v11

    .line 8
    iget-object v11, v1, Lh2/t;->g:Landroidx/media3/common/r;

    invoke-virtual {v11}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    move-result-object v11

    invoke-virtual {v11, v7}, Landroidx/media3/common/r$b;->b0(F)Landroidx/media3/common/r$b;

    move-result-object v7

    invoke-virtual {v7}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v7

    .line 9
    invoke-virtual {v1, v7}, Lh2/t;->a(Landroidx/media3/common/r;)Lh2/t;

    move-result-object v1

    :cond_2
    const v7, 0x7374636f

    .line 10
    invoke-virtual {v0, v7}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v7

    const/4 v11, 0x1

    if-nez v7, :cond_3

    const v7, 0x636f3634

    .line 11
    invoke-virtual {v0, v7}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v7

    invoke-static {v7}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroidx/media3/container/d$c;

    const/4 v12, 0x1

    goto :goto_1

    :cond_3
    const/4 v12, 0x0

    .line 12
    :goto_1
    iget-object v7, v7, Landroidx/media3/container/d$c;->b:Lt1/G;

    const v13, 0x73747363

    .line 13
    invoke-virtual {v0, v13}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v13

    invoke-static {v13}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Landroidx/media3/container/d$c;

    iget-object v13, v13, Landroidx/media3/container/d$c;->b:Lt1/G;

    const v14, 0x73747473

    .line 14
    invoke-virtual {v0, v14}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v14

    invoke-static {v14}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v14

    check-cast v14, Landroidx/media3/container/d$c;

    iget-object v14, v14, Landroidx/media3/container/d$c;->b:Lt1/G;

    const v15, 0x73747373

    .line 15
    invoke-virtual {v0, v15}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v15

    if-eqz v15, :cond_4

    .line 16
    iget-object v15, v15, Landroidx/media3/container/d$c;->b:Lt1/G;

    :goto_2
    move-wide/from16 v16, v9

    goto :goto_3

    :cond_4
    const/4 v15, 0x0

    goto :goto_2

    :goto_3
    const v9, 0x63747473

    .line 17
    invoke-virtual {v0, v9}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 18
    iget-object v0, v0, Landroidx/media3/container/d$c;->b:Lt1/G;

    goto :goto_4

    :cond_5
    const/4 v0, 0x0

    .line 19
    :goto_4
    new-instance v9, Lh2/b$b;

    invoke-direct {v9, v13, v7, v12}, Lh2/b$b;-><init>(Lt1/G;Lt1/G;Z)V

    const/16 v7, 0xc

    .line 20
    invoke-virtual {v14, v7}, Lt1/G;->W(I)V

    .line 21
    invoke-virtual {v14}, Lt1/G;->L()I

    move-result v10

    sub-int/2addr v10, v11

    .line 22
    invoke-virtual {v14}, Lt1/G;->L()I

    move-result v12

    .line 23
    invoke-virtual {v14}, Lt1/G;->L()I

    move-result v13

    if-eqz v0, :cond_6

    .line 24
    invoke-virtual {v0, v7}, Lt1/G;->W(I)V

    .line 25
    invoke-virtual {v0}, Lt1/G;->L()I

    move-result v18

    goto :goto_5

    :cond_6
    const/16 v18, 0x0

    :goto_5
    const/4 v4, -0x1

    if-eqz v15, :cond_8

    .line 26
    invoke-virtual {v15, v7}, Lt1/G;->W(I)V

    .line 27
    invoke-virtual {v15}, Lt1/G;->L()I

    move-result v7

    if-lez v7, :cond_7

    .line 28
    invoke-virtual {v15}, Lt1/G;->L()I

    move-result v19

    add-int/lit8 v19, v19, -0x1

    :goto_6
    const/16 v20, 0x0

    goto :goto_8

    :cond_7
    const/4 v15, 0x0

    :goto_7
    const/16 v19, -0x1

    goto :goto_6

    :cond_8
    const/4 v7, 0x0

    goto :goto_7

    .line 29
    :goto_8
    invoke-interface {v5}, Lh2/b$f;->c()I

    move-result v6

    .line 30
    iget-object v8, v1, Lh2/t;->g:Landroidx/media3/common/r;

    iget-object v8, v8, Landroidx/media3/common/r;->o:Ljava/lang/String;

    if-eq v6, v4, :cond_a

    .line 31
    const-string v4, "audio/raw"

    .line 32
    invoke-virtual {v4, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_9

    const-string v4, "audio/g711-mlaw"

    .line 33
    invoke-virtual {v4, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_9

    const-string v4, "audio/g711-alaw"

    .line 34
    invoke-virtual {v4, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_a

    :cond_9
    if-nez v10, :cond_a

    if-nez v18, :cond_a

    if-nez v7, :cond_a

    const/4 v4, 0x1

    goto :goto_9

    :cond_a
    const/4 v4, 0x0

    :goto_9
    if-eqz v4, :cond_c

    .line 35
    iget v0, v9, Lh2/b$b;->a:I

    new-array v4, v0, [J

    .line 36
    new-array v0, v0, [I

    .line 37
    :goto_a
    invoke-virtual {v9}, Lh2/b$b;->a()Z

    move-result v5

    if-eqz v5, :cond_b

    .line 38
    iget v5, v9, Lh2/b$b;->b:I

    iget-wide v7, v9, Lh2/b$b;->d:J

    aput-wide v7, v4, v5

    .line 39
    iget v7, v9, Lh2/b$b;->c:I

    aput v7, v0, v5

    goto :goto_a

    :cond_b
    int-to-long v7, v13

    .line 40
    invoke-static {v6, v4, v0, v7, v8}, Lh2/d;->a(I[J[IJ)Lh2/d$b;

    move-result-object v0

    .line 41
    iget-object v4, v0, Lh2/d$b;->a:[J

    .line 42
    iget-object v5, v0, Lh2/d$b;->b:[I

    .line 43
    iget v6, v0, Lh2/d$b;->c:I

    .line 44
    iget-object v7, v0, Lh2/d$b;->d:[J

    .line 45
    iget-object v8, v0, Lh2/d$b;->e:[I

    .line 46
    iget-wide v9, v0, Lh2/d$b;->f:J

    .line 47
    iget-wide v12, v0, Lh2/d$b;->g:J

    move-wide/from16 v22, v9

    const/16 p0, 0x1

    move-object v9, v7

    move-object v10, v8

    move v8, v6

    move-object v7, v5

    move-object v6, v4

    goto/16 :goto_14

    .line 48
    :cond_c
    new-array v4, v3, [J

    .line 49
    new-array v6, v3, [I

    .line 50
    new-array v8, v3, [J

    const/16 p0, 0x1

    .line 51
    new-array v11, v3, [I

    move-object/from16 p1, v0

    move-object/from16 v23, v5

    move v0, v13

    move-object/from16 v24, v14

    move-object/from16 v26, v15

    move-wide/from16 v27, v16

    move-wide/from16 v29, v27

    move/from16 v22, v18

    move/from16 v5, v19

    const/4 v14, 0x0

    const/4 v15, 0x0

    const/16 v25, 0x0

    const/16 v31, 0x0

    move v13, v12

    move-wide/from16 v18, v29

    move v12, v10

    move v10, v7

    const/4 v7, 0x0

    .line 52
    :goto_b
    const-string v2, "BoxParsers"

    if-ge v14, v3, :cond_15

    move-wide/from16 v32, v29

    move/from16 v29, v25

    const/16 v25, 0x1

    :goto_c
    if-nez v29, :cond_d

    .line 53
    invoke-virtual {v9}, Lh2/b$b;->a()Z

    move-result v25

    if-eqz v25, :cond_d

    move/from16 v30, v12

    move/from16 v34, v13

    .line 54
    iget-wide v12, v9, Lh2/b$b;->d:J

    move/from16 v35, v3

    .line 55
    iget v3, v9, Lh2/b$b;->c:I

    move/from16 v29, v3

    move-wide/from16 v32, v12

    move/from16 v12, v30

    move/from16 v13, v34

    move/from16 v3, v35

    goto :goto_c

    :cond_d
    move/from16 v35, v3

    move/from16 v30, v12

    move/from16 v34, v13

    if-nez v25, :cond_e

    .line 56
    const-string v0, "Unexpected end of chunk data"

    invoke-static {v2, v0}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    .line 57
    invoke-static {v4, v14}, Ljava/util/Arrays;->copyOf([JI)[J

    move-result-object v0

    .line 58
    invoke-static {v6, v14}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v3

    .line 59
    invoke-static {v8, v14}, Ljava/util/Arrays;->copyOf([JI)[J

    move-result-object v4

    .line 60
    invoke-static {v11, v14}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v5

    move-object v8, v4

    move-object v11, v5

    move-object v4, v0

    move-object v5, v3

    move v3, v14

    move/from16 v0, v29

    goto/16 :goto_10

    :cond_e
    if-eqz p1, :cond_10

    move/from16 v2, v31

    :goto_d
    if-nez v2, :cond_f

    if-lez v22, :cond_f

    .line 61
    invoke-virtual/range {p1 .. p1}, Lt1/G;->L()I

    move-result v2

    .line 62
    invoke-virtual/range {p1 .. p1}, Lt1/G;->q()I

    move-result v15

    add-int/lit8 v22, v22, -0x1

    goto :goto_d

    :cond_f
    add-int/lit8 v2, v2, -0x1

    move/from16 v31, v2

    .line 63
    :cond_10
    aput-wide v32, v4, v14

    .line 64
    invoke-interface/range {v23 .. v23}, Lh2/b$f;->a()I

    move-result v2

    aput v2, v6, v14

    int-to-long v12, v2

    add-long v18, v18, v12

    if-le v2, v7, :cond_11

    move v7, v2

    :cond_11
    int-to-long v2, v15

    add-long v2, v27, v2

    .line 65
    aput-wide v2, v8, v14

    if-nez v26, :cond_12

    const/4 v2, 0x1

    goto :goto_e

    :cond_12
    const/4 v2, 0x0

    .line 66
    :goto_e
    aput v2, v11, v14

    if-ne v14, v5, :cond_13

    .line 67
    aput p0, v11, v14

    add-int/lit8 v10, v10, -0x1

    if-lez v10, :cond_13

    .line 68
    invoke-static/range {v26 .. v26}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lt1/G;

    invoke-virtual {v2}, Lt1/G;->L()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    move v5, v2

    :cond_13
    int-to-long v2, v0

    add-long v27, v27, v2

    add-int/lit8 v13, v34, -0x1

    if-nez v13, :cond_14

    if-lez v30, :cond_14

    .line 69
    invoke-virtual/range {v24 .. v24}, Lt1/G;->L()I

    move-result v0

    .line 70
    invoke-virtual/range {v24 .. v24}, Lt1/G;->q()I

    move-result v2

    add-int/lit8 v12, v30, -0x1

    move v13, v0

    move v0, v2

    goto :goto_f

    :cond_14
    move/from16 v12, v30

    .line 71
    :goto_f
    aget v2, v6, v14

    int-to-long v2, v2

    add-long v2, v32, v2

    add-int/lit8 v25, v29, -0x1

    add-int/lit8 v14, v14, 0x1

    move-wide/from16 v29, v2

    move/from16 v3, v35

    goto/16 :goto_b

    :cond_15
    move/from16 v35, v3

    move/from16 v30, v12

    move/from16 v34, v13

    move-object v5, v6

    move/from16 v0, v25

    :goto_10
    int-to-long v12, v15

    add-long v12, v27, v12

    if-eqz p1, :cond_17

    :goto_11
    if-lez v22, :cond_17

    .line 72
    invoke-virtual/range {p1 .. p1}, Lt1/G;->L()I

    move-result v6

    if-eqz v6, :cond_16

    const/4 v6, 0x0

    goto :goto_12

    .line 73
    :cond_16
    invoke-virtual/range {p1 .. p1}, Lt1/G;->q()I

    add-int/lit8 v22, v22, -0x1

    goto :goto_11

    :cond_17
    const/4 v6, 0x1

    :goto_12
    if-nez v10, :cond_18

    if-nez v34, :cond_18

    if-nez v0, :cond_18

    if-nez v30, :cond_18

    if-nez v31, :cond_18

    if-nez v6, :cond_1a

    .line 74
    :cond_18
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "Inconsistent stbl box for track "

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v14, v1, Lh2/t;->a:I

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v14, ": remainingSynchronizationSamples "

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v10, ", remainingSamplesAtTimestampDelta "

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move/from16 v10, v34

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v10, ", remainingSamplesInChunk "

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", remainingTimestampDeltaChanges "

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move/from16 v10, v30

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", remainingSamplesAtTimestampOffset "

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move/from16 v0, v31

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    if-nez v6, :cond_19

    .line 75
    const-string v0, ", ctts invalid"

    goto :goto_13

    :cond_19
    const-string v0, ""

    :goto_13
    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 76
    invoke-static {v2, v0}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1a
    move-object v9, v8

    move-object v10, v11

    move-wide/from16 v22, v12

    move-wide/from16 v12, v18

    move v8, v7

    move-object v6, v4

    move-object v7, v5

    .line 77
    :goto_14
    iget-wide v4, v1, Lh2/t;->f:J

    const-wide/32 v18, 0x7fffffff

    cmp-long v0, v4, v16

    if-lez v0, :cond_1b

    const-wide/16 v14, 0x8

    mul-long v24, v12, v14

    const-wide/32 v26, 0xf4240

    .line 78
    sget-object v30, Ljava/math/RoundingMode;->HALF_DOWN:Ljava/math/RoundingMode;

    move-wide/from16 v28, v4

    .line 79
    invoke-static/range {v24 .. v30}, Lt1/a0;->e1(JJJLjava/math/RoundingMode;)J

    move-result-wide v4

    cmp-long v0, v4, v16

    if-lez v0, :cond_1b

    cmp-long v0, v4, v18

    if-gez v0, :cond_1b

    .line 80
    iget-object v0, v1, Lh2/t;->g:Landroidx/media3/common/r;

    invoke-virtual {v0}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    move-result-object v0

    long-to-int v2, v4

    invoke-virtual {v0, v2}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v0

    .line 81
    invoke-virtual {v1, v0}, Lh2/t;->a(Landroidx/media3/common/r;)Lh2/t;

    move-result-object v1

    :cond_1b
    move-object v5, v1

    .line 82
    iget-wide v0, v5, Lh2/t;->c:J

    const-wide/32 v24, 0xf4240

    move-wide/from16 v26, v0

    invoke-static/range {v22 .. v27}, Lt1/a0;->c1(JJJ)J

    move-result-wide v11

    .line 83
    iget-object v0, v5, Lh2/t;->i:[J

    const-wide/32 v1, 0xf4240

    if-nez v0, :cond_1c

    .line 84
    iget-wide v3, v5, Lh2/t;->c:J

    invoke-static {v9, v1, v2, v3, v4}, Lt1/a0;->d1([JJJ)V

    .line 85
    new-instance v4, Lh2/w;

    invoke-direct/range {v4 .. v12}, Lh2/w;-><init>(Lh2/t;[J[II[J[IJ)V

    return-object v4

    :cond_1c
    move v4, v8

    move-object v8, v10

    .line 86
    array-length v0, v0

    const/4 v10, 0x1

    if-ne v0, v10, :cond_1d

    iget v0, v5, Lh2/t;->b:I

    if-ne v0, v10, :cond_1d

    array-length v0, v9

    const/4 v10, 0x2

    if-lt v0, v10, :cond_1d

    .line 87
    iget-object v0, v5, Lh2/t;->j:[J

    invoke-static {v0}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [J

    aget-wide v12, v0, v20

    .line 88
    iget-object v0, v5, Lh2/t;->i:[J

    aget-wide v24, v0, v20

    iget-wide v10, v5, Lh2/t;->c:J

    iget-wide v14, v5, Lh2/t;->d:J

    move-wide/from16 v26, v10

    move-wide/from16 v28, v14

    .line 89
    invoke-static/range {v24 .. v29}, Lt1/a0;->c1(JJJ)J

    move-result-wide v10

    add-long v14, v12, v10

    move-wide/from16 v10, v22

    .line 90
    invoke-static/range {v9 .. v15}, Lh2/b;->b([JJJJ)Z

    move-result v0

    if-eqz v0, :cond_1d

    sub-long v24, v22, v14

    .line 91
    aget-wide v10, v9, v20

    sub-long v26, v12, v10

    iget-object v0, v5, Lh2/t;->g:Landroidx/media3/common/r;

    iget v0, v0, Landroidx/media3/common/r;->F:I

    int-to-long v10, v0

    iget-wide v12, v5, Lh2/t;->c:J

    move-wide/from16 v28, v10

    move-wide/from16 v30, v12

    .line 92
    invoke-static/range {v26 .. v31}, Lt1/a0;->c1(JJJ)J

    move-result-wide v10

    .line 93
    iget-object v0, v5, Lh2/t;->g:Landroidx/media3/common/r;

    iget v0, v0, Landroidx/media3/common/r;->F:I

    int-to-long v12, v0

    iget-wide v14, v5, Lh2/t;->c:J

    move-wide/from16 v26, v12

    move-wide/from16 v28, v14

    .line 94
    invoke-static/range {v24 .. v29}, Lt1/a0;->c1(JJJ)J

    move-result-wide v12

    cmp-long v0, v10, v16

    if-nez v0, :cond_1e

    cmp-long v0, v12, v16

    if-eqz v0, :cond_1d

    goto :goto_15

    :cond_1d
    move-object v10, v8

    move v8, v4

    goto :goto_16

    :cond_1e
    :goto_15
    cmp-long v0, v10, v18

    if-gtz v0, :cond_1d

    cmp-long v0, v12, v18

    if-gtz v0, :cond_1d

    long-to-int v0, v10

    move-object/from16 v3, p2

    .line 95
    iput v0, v3, LN1/E;->a:I

    long-to-int v0, v12

    .line 96
    iput v0, v3, LN1/E;->b:I

    .line 97
    iget-wide v10, v5, Lh2/t;->c:J

    invoke-static {v9, v1, v2, v10, v11}, Lt1/a0;->d1([JJJ)V

    .line 98
    iget-object v0, v5, Lh2/t;->i:[J

    aget-wide v10, v0, v20

    const-wide/32 v12, 0xf4240

    iget-wide v14, v5, Lh2/t;->d:J

    .line 99
    invoke-static/range {v10 .. v15}, Lt1/a0;->c1(JJJ)J

    move-result-wide v11

    move-object v10, v8

    move v8, v4

    .line 100
    new-instance v4, Lh2/w;

    invoke-direct/range {v4 .. v12}, Lh2/w;-><init>(Lh2/t;[J[II[J[IJ)V

    return-object v4

    .line 101
    :goto_16
    iget-object v0, v5, Lh2/t;->i:[J

    array-length v1, v0

    const/4 v2, 0x1

    if-ne v1, v2, :cond_20

    aget-wide v1, v0, v20

    cmp-long v4, v1, v16

    if-nez v4, :cond_20

    .line 102
    iget-object v0, v5, Lh2/t;->j:[J

    invoke-static {v0}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [J

    aget-wide v1, v0, v20

    const/4 v0, 0x0

    .line 103
    :goto_17
    array-length v3, v9

    if-ge v0, v3, :cond_1f

    .line 104
    aget-wide v3, v9, v0

    sub-long v11, v3, v1

    const-wide/32 v13, 0xf4240

    iget-wide v3, v5, Lh2/t;->c:J

    move-wide v15, v3

    .line 105
    invoke-static/range {v11 .. v16}, Lt1/a0;->c1(JJJ)J

    move-result-wide v3

    aput-wide v3, v9, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_17

    :cond_1f
    sub-long v11, v22, v1

    const-wide/32 v13, 0xf4240

    .line 106
    iget-wide v0, v5, Lh2/t;->c:J

    move-wide v15, v0

    .line 107
    invoke-static/range {v11 .. v16}, Lt1/a0;->c1(JJJ)J

    move-result-wide v11

    .line 108
    new-instance v4, Lh2/w;

    invoke-direct/range {v4 .. v12}, Lh2/w;-><init>(Lh2/t;[J[II[J[IJ)V

    return-object v4

    .line 109
    :cond_20
    iget v1, v5, Lh2/t;->b:I

    const/4 v2, 0x1

    if-ne v1, v2, :cond_21

    const/4 v1, 0x1

    goto :goto_18

    :cond_21
    const/4 v1, 0x0

    .line 110
    :goto_18
    array-length v2, v0

    new-array v2, v2, [I

    .line 111
    array-length v0, v0

    new-array v0, v0, [I

    .line 112
    iget-object v4, v5, Lh2/t;->j:[J

    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [J

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    .line 113
    :goto_19
    iget-object v15, v5, Lh2/t;->i:[J

    move-object/from16 v18, v0

    array-length v0, v15

    if-ge v11, v0, :cond_27

    move v0, v11

    move/from16 p1, v12

    .line 114
    aget-wide v11, v4, v0

    const-wide/16 v22, -0x1

    cmp-long v19, v11, v22

    if-eqz v19, :cond_26

    .line 115
    aget-wide v22, v15, v0

    move-object v15, v7

    move/from16 p2, v8

    iget-wide v7, v5, Lh2/t;->c:J

    move-wide/from16 v24, v7

    iget-wide v7, v5, Lh2/t;->d:J

    move-wide/from16 v26, v7

    .line 116
    invoke-static/range {v22 .. v27}, Lt1/a0;->c1(JJJ)J

    move-result-wide v7

    move/from16 v19, v0

    const/4 v0, 0x1

    .line 117
    invoke-static {v9, v11, v12, v0, v0}, Lt1/a0;->h([JJZZ)I

    move-result v22

    aput v22, v2, v19

    add-long/2addr v11, v7

    const/4 v8, 0x0

    .line 118
    invoke-static {v9, v11, v12, v1, v8}, Lt1/a0;->d([JJZZ)I

    move-result v7

    aput v7, v18, v19

    .line 119
    aget v7, v2, v19

    .line 120
    :goto_1a
    aget v20, v2, v19

    if-ltz v20, :cond_22

    aget v22, v10, v20

    and-int/lit8 v22, v22, 0x1

    if-nez v22, :cond_22

    add-int/lit8 v20, v20, -0x1

    .line 121
    aput v20, v2, v19

    const/4 v0, 0x1

    goto :goto_1a

    :cond_22
    if-gez v20, :cond_23

    .line 122
    aput v7, v2, v19

    .line 123
    :goto_1b
    aget v0, v2, v19

    aget v7, v18, v19

    if-ge v0, v7, :cond_23

    aget v7, v10, v0

    const/16 v20, 0x1

    and-int/lit8 v7, v7, 0x1

    if-nez v7, :cond_23

    add-int/lit8 v0, v0, 0x1

    .line 124
    aput v0, v2, v19

    goto :goto_1b

    .line 125
    :cond_23
    iget v0, v5, Lh2/t;->b:I

    const/4 v7, 0x2

    if-ne v0, v7, :cond_24

    aget v0, v2, v19

    aget v7, v18, v19

    if-eq v0, v7, :cond_24

    .line 126
    :goto_1c
    aget v0, v18, v19

    array-length v7, v9

    const/16 v20, 0x1

    add-int/lit8 v7, v7, -0x1

    if-ge v0, v7, :cond_24

    add-int/lit8 v7, v0, 0x1

    aget-wide v22, v9, v7

    cmp-long v7, v22, v11

    if-gtz v7, :cond_24

    add-int/lit8 v0, v0, 0x1

    .line 127
    aput v0, v18, v19

    goto :goto_1c

    .line 128
    :cond_24
    aget v0, v18, v19

    aget v7, v2, v19

    sub-int v11, v0, v7

    add-int/2addr v13, v11

    if-eq v14, v7, :cond_25

    const/4 v7, 0x1

    goto :goto_1d

    :cond_25
    const/4 v7, 0x0

    :goto_1d
    or-int v12, p1, v7

    move v14, v0

    goto :goto_1e

    :cond_26
    move/from16 v19, v0

    move-object v15, v7

    move/from16 p2, v8

    const/4 v8, 0x0

    move/from16 v12, p1

    :goto_1e
    add-int/lit8 v11, v19, 0x1

    move/from16 v8, p2

    move-object v7, v15

    move-object/from16 v0, v18

    const/16 v20, 0x0

    goto/16 :goto_19

    :cond_27
    move-object v15, v7

    move/from16 p2, v8

    move/from16 p1, v12

    const/4 v8, 0x0

    if-eq v13, v3, :cond_28

    const/4 v0, 0x1

    goto :goto_1f

    :cond_28
    const/4 v0, 0x0

    :goto_1f
    or-int v0, p1, v0

    if-eqz v0, :cond_29

    .line 129
    new-array v1, v13, [J

    goto :goto_20

    :cond_29
    move-object v1, v6

    :goto_20
    if-eqz v0, :cond_2a

    .line 130
    new-array v7, v13, [I

    goto :goto_21

    :cond_2a
    move-object v7, v15

    :goto_21
    if-eqz v0, :cond_2b

    const/4 v3, 0x0

    goto :goto_22

    :cond_2b
    move/from16 v3, p2

    :goto_22
    if-eqz v0, :cond_2c

    .line 131
    new-array v4, v13, [I

    goto :goto_23

    :cond_2c
    move-object v4, v10

    .line 132
    :goto_23
    new-array v11, v13, [J

    move/from16 v23, v3

    move-wide/from16 v24, v16

    const/4 v3, 0x0

    const/4 v12, 0x0

    .line 133
    :goto_24
    iget-object v13, v5, Lh2/t;->i:[J

    array-length v13, v13

    if-ge v8, v13, :cond_31

    .line 134
    iget-object v13, v5, Lh2/t;->j:[J

    aget-wide v19, v13, v8

    .line 135
    aget v13, v2, v8

    .line 136
    aget v14, v18, v8

    move/from16 p1, v0

    if-eqz v0, :cond_2d

    sub-int v0, v14, v13

    .line 137
    invoke-static {v6, v13, v1, v12, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 138
    invoke-static {v15, v13, v7, v12, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 139
    invoke-static {v10, v13, v4, v12, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    :cond_2d
    move/from16 v0, v23

    :goto_25
    if-ge v13, v14, :cond_30

    const-wide/32 v26, 0xf4240

    move-object/from16 v22, v1

    move-object/from16 v21, v2

    .line 140
    iget-wide v1, v5, Lh2/t;->d:J

    move-wide/from16 v28, v1

    invoke-static/range {v24 .. v29}, Lt1/a0;->c1(JJJ)J

    move-result-wide v1

    .line 141
    aget-wide v26, v9, v13

    sub-long v28, v26, v19

    const-wide/32 v30, 0xf4240

    move-wide/from16 v26, v1

    iget-wide v1, v5, Lh2/t;->c:J

    move-wide/from16 v32, v1

    .line 142
    invoke-static/range {v28 .. v33}, Lt1/a0;->c1(JJJ)J

    move-result-wide v1

    cmp-long v23, v1, v16

    if-gez v23, :cond_2e

    const/4 v3, 0x1

    :cond_2e
    add-long v1, v26, v1

    .line 143
    aput-wide v1, v11, v12

    if-eqz p1, :cond_2f

    .line 144
    aget v1, v7, v12

    if-le v1, v0, :cond_2f

    .line 145
    aget v0, v15, v13

    :cond_2f
    add-int/lit8 v12, v12, 0x1

    add-int/lit8 v13, v13, 0x1

    move-object/from16 v2, v21

    move-object/from16 v1, v22

    goto :goto_25

    :cond_30
    move-object/from16 v22, v1

    move-object/from16 v21, v2

    .line 146
    iget-object v1, v5, Lh2/t;->i:[J

    aget-wide v13, v1, v8

    add-long v24, v24, v13

    add-int/lit8 v8, v8, 0x1

    move/from16 v23, v0

    move-object/from16 v1, v22

    move/from16 v0, p1

    goto :goto_24

    :cond_31
    move-object/from16 v22, v1

    const-wide/32 v26, 0xf4240

    .line 147
    iget-wide v0, v5, Lh2/t;->d:J

    move-wide/from16 v28, v0

    .line 148
    invoke-static/range {v24 .. v29}, Lt1/a0;->c1(JJJ)J

    move-result-wide v26

    if-eqz v3, :cond_32

    .line 149
    iget-object v0, v5, Lh2/t;->g:Landroidx/media3/common/r;

    invoke-virtual {v0}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    move-result-object v0

    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Landroidx/media3/common/r$b;->c0(Z)Landroidx/media3/common/r$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v0

    .line 150
    invoke-virtual {v5, v0}, Lh2/t;->a(Landroidx/media3/common/r;)Lh2/t;

    move-result-object v5

    :cond_32
    move-object/from16 v20, v5

    .line 151
    new-instance v19, Lh2/w;

    move-object/from16 v25, v4

    move-object/from16 v24, v11

    move-object/from16 v21, v22

    move-object/from16 v22, v7

    invoke-direct/range {v19 .. v27}, Lh2/w;-><init>(Lh2/t;[J[II[J[IJ)V

    return-object v19

    .line 152
    :cond_33
    const-string v0, "Track has no sample table size information"

    const/4 v1, 0x0

    invoke-static {v0, v1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0
.end method

.method public static B(Lt1/G;II)Lh2/b$d;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    add-int/lit8 v0, p1, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    :goto_0
    sub-int v1, v0, p1

    .line 11
    .line 12
    if-ge v1, p2, :cond_5

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    const/4 v2, 0x0

    .line 22
    const/4 v3, 0x1

    .line 23
    if-lez v1, :cond_0

    .line 24
    .line 25
    const/4 v4, 0x1

    .line 26
    goto :goto_1

    .line 27
    :cond_0
    const/4 v4, 0x0

    .line 28
    :goto_1
    const-string v5, "childAtomSize must be positive"

    .line 29
    .line 30
    invoke-static {v4, v5}, LN1/u;->a(ZLjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    const v5, 0x73747269

    .line 38
    .line 39
    .line 40
    if-ne v4, v5, :cond_4

    .line 41
    .line 42
    const/4 p1, 0x4

    .line 43
    invoke-virtual {p0, p1}, Lt1/G;->X(I)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 47
    .line 48
    .line 49
    move-result p0

    .line 50
    new-instance p1, Lh2/b$d;

    .line 51
    .line 52
    new-instance p2, Lh2/b$g;

    .line 53
    .line 54
    and-int/lit8 v0, p0, 0x1

    .line 55
    .line 56
    if-ne v0, v3, :cond_1

    .line 57
    .line 58
    const/4 v0, 0x1

    .line 59
    goto :goto_2

    .line 60
    :cond_1
    const/4 v0, 0x0

    .line 61
    :goto_2
    and-int/lit8 v1, p0, 0x2

    .line 62
    .line 63
    const/4 v4, 0x2

    .line 64
    if-ne v1, v4, :cond_2

    .line 65
    .line 66
    const/4 v1, 0x1

    .line 67
    goto :goto_3

    .line 68
    :cond_2
    const/4 v1, 0x0

    .line 69
    :goto_3
    const/16 v4, 0x8

    .line 70
    .line 71
    and-int/2addr p0, v4

    .line 72
    if-ne p0, v4, :cond_3

    .line 73
    .line 74
    const/4 v2, 0x1

    .line 75
    :cond_3
    invoke-direct {p2, v0, v1, v2}, Lh2/b$g;-><init>(ZZZ)V

    .line 76
    .line 77
    .line 78
    invoke-direct {p1, p2}, Lh2/b$d;-><init>(Lh2/b$g;)V

    .line 79
    .line 80
    .line 81
    return-object p1

    .line 82
    :cond_4
    add-int/2addr v0, v1

    .line 83
    goto :goto_0

    .line 84
    :cond_5
    const/4 p0, 0x0

    .line 85
    return-object p0
.end method

.method public static C(Lt1/G;IILjava/lang/String;Landroidx/media3/common/DrmInitData;Z)Lh2/b$h;
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    const/16 v0, 0xc

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    new-instance v9, Lh2/b$h;

    .line 11
    .line 12
    invoke-direct {v9, v0}, Lh2/b$h;-><init>(I)V

    .line 13
    .line 14
    .line 15
    const/4 v11, 0x0

    .line 16
    const/4 v10, 0x0

    .line 17
    :goto_0
    if-ge v10, v0, :cond_9

    .line 18
    .line 19
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-lez v4, :cond_0

    .line 28
    .line 29
    const/4 v1, 0x1

    .line 30
    goto :goto_1

    .line 31
    :cond_0
    const/4 v1, 0x0

    .line 32
    :goto_1
    const-string v2, "childAtomSize must be positive"

    .line 33
    .line 34
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    const v1, 0x61766331

    .line 42
    .line 43
    .line 44
    if-eq v2, v1, :cond_1

    .line 45
    .line 46
    const v1, 0x61766333

    .line 47
    .line 48
    .line 49
    if-eq v2, v1, :cond_1

    .line 50
    .line 51
    const v1, 0x656e6376

    .line 52
    .line 53
    .line 54
    if-eq v2, v1, :cond_1

    .line 55
    .line 56
    const v1, 0x6d317620

    .line 57
    .line 58
    .line 59
    if-eq v2, v1, :cond_1

    .line 60
    .line 61
    const v1, 0x6d703476

    .line 62
    .line 63
    .line 64
    if-eq v2, v1, :cond_1

    .line 65
    .line 66
    const v1, 0x68766331

    .line 67
    .line 68
    .line 69
    if-eq v2, v1, :cond_1

    .line 70
    .line 71
    const v1, 0x68657631

    .line 72
    .line 73
    .line 74
    if-eq v2, v1, :cond_1

    .line 75
    .line 76
    const v1, 0x73323633

    .line 77
    .line 78
    .line 79
    if-eq v2, v1, :cond_1

    .line 80
    .line 81
    const v1, 0x48323633

    .line 82
    .line 83
    .line 84
    if-eq v2, v1, :cond_1

    .line 85
    .line 86
    const v1, 0x68323633

    .line 87
    .line 88
    .line 89
    if-eq v2, v1, :cond_1

    .line 90
    .line 91
    const v1, 0x76703038

    .line 92
    .line 93
    .line 94
    if-eq v2, v1, :cond_1

    .line 95
    .line 96
    const v1, 0x76703039

    .line 97
    .line 98
    .line 99
    if-eq v2, v1, :cond_1

    .line 100
    .line 101
    const v1, 0x61763031

    .line 102
    .line 103
    .line 104
    if-eq v2, v1, :cond_1

    .line 105
    .line 106
    const v1, 0x64766176

    .line 107
    .line 108
    .line 109
    if-eq v2, v1, :cond_1

    .line 110
    .line 111
    const v1, 0x64766131

    .line 112
    .line 113
    .line 114
    if-eq v2, v1, :cond_1

    .line 115
    .line 116
    const v1, 0x64766865

    .line 117
    .line 118
    .line 119
    if-eq v2, v1, :cond_1

    .line 120
    .line 121
    const v1, 0x64766831

    .line 122
    .line 123
    .line 124
    if-eq v2, v1, :cond_1

    .line 125
    .line 126
    const v1, 0x61707631

    .line 127
    .line 128
    .line 129
    if-ne v2, v1, :cond_2

    .line 130
    .line 131
    :cond_1
    move-object v1, p0

    .line 132
    move v5, p1

    .line 133
    move v7, p2

    .line 134
    move-object v6, p3

    .line 135
    move-object/from16 v8, p4

    .line 136
    .line 137
    goto/16 :goto_4

    .line 138
    .line 139
    :cond_2
    const v1, 0x6d703461

    .line 140
    .line 141
    .line 142
    if-eq v2, v1, :cond_3

    .line 143
    .line 144
    const v1, 0x656e6361

    .line 145
    .line 146
    .line 147
    if-eq v2, v1, :cond_3

    .line 148
    .line 149
    const v1, 0x61632d33

    .line 150
    .line 151
    .line 152
    if-eq v2, v1, :cond_3

    .line 153
    .line 154
    const v1, 0x65632d33

    .line 155
    .line 156
    .line 157
    if-eq v2, v1, :cond_3

    .line 158
    .line 159
    const v1, 0x61632d34

    .line 160
    .line 161
    .line 162
    if-eq v2, v1, :cond_3

    .line 163
    .line 164
    const v1, 0x6d6c7061

    .line 165
    .line 166
    .line 167
    if-eq v2, v1, :cond_3

    .line 168
    .line 169
    const v1, 0x64747363

    .line 170
    .line 171
    .line 172
    if-eq v2, v1, :cond_3

    .line 173
    .line 174
    const v1, 0x64747365

    .line 175
    .line 176
    .line 177
    if-eq v2, v1, :cond_3

    .line 178
    .line 179
    const v1, 0x64747368

    .line 180
    .line 181
    .line 182
    if-eq v2, v1, :cond_3

    .line 183
    .line 184
    const v1, 0x6474736c

    .line 185
    .line 186
    .line 187
    if-eq v2, v1, :cond_3

    .line 188
    .line 189
    const v1, 0x64747378

    .line 190
    .line 191
    .line 192
    if-eq v2, v1, :cond_3

    .line 193
    .line 194
    const v1, 0x73616d72

    .line 195
    .line 196
    .line 197
    if-eq v2, v1, :cond_3

    .line 198
    .line 199
    const v1, 0x73617762

    .line 200
    .line 201
    .line 202
    if-eq v2, v1, :cond_3

    .line 203
    .line 204
    const v1, 0x6c70636d

    .line 205
    .line 206
    .line 207
    if-eq v2, v1, :cond_3

    .line 208
    .line 209
    const v1, 0x736f7774

    .line 210
    .line 211
    .line 212
    if-eq v2, v1, :cond_3

    .line 213
    .line 214
    const v1, 0x74776f73

    .line 215
    .line 216
    .line 217
    if-eq v2, v1, :cond_3

    .line 218
    .line 219
    const v1, 0x2e6d7032

    .line 220
    .line 221
    .line 222
    if-eq v2, v1, :cond_3

    .line 223
    .line 224
    const v1, 0x2e6d7033

    .line 225
    .line 226
    .line 227
    if-eq v2, v1, :cond_3

    .line 228
    .line 229
    const v1, 0x6d686131

    .line 230
    .line 231
    .line 232
    if-eq v2, v1, :cond_3

    .line 233
    .line 234
    const v1, 0x6d686d31

    .line 235
    .line 236
    .line 237
    if-eq v2, v1, :cond_3

    .line 238
    .line 239
    const v1, 0x616c6163

    .line 240
    .line 241
    .line 242
    if-eq v2, v1, :cond_3

    .line 243
    .line 244
    const v1, 0x616c6177

    .line 245
    .line 246
    .line 247
    if-eq v2, v1, :cond_3

    .line 248
    .line 249
    const v1, 0x756c6177

    .line 250
    .line 251
    .line 252
    if-eq v2, v1, :cond_3

    .line 253
    .line 254
    const v1, 0x4f707573

    .line 255
    .line 256
    .line 257
    if-eq v2, v1, :cond_3

    .line 258
    .line 259
    const v1, 0x664c6143

    .line 260
    .line 261
    .line 262
    if-eq v2, v1, :cond_3

    .line 263
    .line 264
    const v1, 0x69616d66

    .line 265
    .line 266
    .line 267
    if-ne v2, v1, :cond_4

    .line 268
    .line 269
    :cond_3
    move-object v1, p0

    .line 270
    move v5, p1

    .line 271
    move-object v6, p3

    .line 272
    move-object/from16 v8, p4

    .line 273
    .line 274
    move/from16 v7, p5

    .line 275
    .line 276
    goto :goto_3

    .line 277
    :cond_4
    const v1, 0x54544d4c

    .line 278
    .line 279
    .line 280
    if-eq v2, v1, :cond_5

    .line 281
    .line 282
    const v1, 0x74783367

    .line 283
    .line 284
    .line 285
    if-eq v2, v1, :cond_5

    .line 286
    .line 287
    const v1, 0x77767474

    .line 288
    .line 289
    .line 290
    if-eq v2, v1, :cond_5

    .line 291
    .line 292
    const v1, 0x73747070

    .line 293
    .line 294
    .line 295
    if-eq v2, v1, :cond_5

    .line 296
    .line 297
    const v1, 0x63363038

    .line 298
    .line 299
    .line 300
    if-ne v2, v1, :cond_6

    .line 301
    .line 302
    :cond_5
    move-object v1, p0

    .line 303
    move v5, p1

    .line 304
    move-object v6, p3

    .line 305
    move-object v7, v9

    .line 306
    goto :goto_2

    .line 307
    :cond_6
    const v1, 0x6d657474

    .line 308
    .line 309
    .line 310
    if-ne v2, v1, :cond_7

    .line 311
    .line 312
    invoke-static {p0, v2, v3, p1, v9}, Lh2/b;->u(Lt1/G;IIILh2/b$h;)V

    .line 313
    .line 314
    .line 315
    goto :goto_5

    .line 316
    :cond_7
    const v1, 0x63616d6d

    .line 317
    .line 318
    .line 319
    if-ne v2, v1, :cond_8

    .line 320
    .line 321
    new-instance v1, Landroidx/media3/common/r$b;

    .line 322
    .line 323
    invoke-direct {v1}, Landroidx/media3/common/r$b;-><init>()V

    .line 324
    .line 325
    .line 326
    invoke-virtual {v1, p1}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    .line 327
    .line 328
    .line 329
    move-result-object v1

    .line 330
    const-string v2, "application/x-camera-motion"

    .line 331
    .line 332
    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 333
    .line 334
    .line 335
    move-result-object v1

    .line 336
    invoke-virtual {v1}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 337
    .line 338
    .line 339
    move-result-object v1

    .line 340
    iput-object v1, v9, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 341
    .line 342
    goto :goto_5

    .line 343
    :goto_2
    invoke-static/range {v1 .. v7}, Lh2/b;->D(Lt1/G;IIIILjava/lang/String;Lh2/b$h;)V

    .line 344
    .line 345
    .line 346
    move-object v9, v7

    .line 347
    goto :goto_5

    .line 348
    :goto_3
    invoke-static/range {v1 .. v10}, Lh2/b;->h(Lt1/G;IIIILjava/lang/String;ZLandroidx/media3/common/DrmInitData;Lh2/b$h;I)V

    .line 349
    .line 350
    .line 351
    goto :goto_5

    .line 352
    :goto_4
    invoke-static/range {v1 .. v10}, Lh2/b;->K(Lt1/G;IIIILjava/lang/String;ILandroidx/media3/common/DrmInitData;Lh2/b$h;I)V

    .line 353
    .line 354
    .line 355
    :cond_8
    :goto_5
    add-int/2addr v3, v4

    .line 356
    invoke-virtual {p0, v3}, Lt1/G;->W(I)V

    .line 357
    .line 358
    .line 359
    add-int/lit8 v10, v10, 0x1

    .line 360
    .line 361
    goto/16 :goto_0

    .line 362
    .line 363
    :cond_9
    return-object v9
.end method

.method public static D(Lt1/G;IIIILjava/lang/String;Lh2/b$h;)V
    .locals 4

    .line 1
    add-int/lit8 p2, p2, 0x10

    .line 2
    .line 3
    invoke-virtual {p0, p2}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    const p2, 0x54544d4c

    .line 7
    .line 8
    .line 9
    const-string v0, "application/ttml+xml"

    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    const-wide v2, 0x7fffffffffffffffL

    .line 13
    .line 14
    .line 15
    .line 16
    .line 17
    if-ne p1, p2, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const p2, 0x74783367

    .line 21
    .line 22
    .line 23
    if-ne p1, p2, :cond_1

    .line 24
    .line 25
    add-int/lit8 p3, p3, -0x10

    .line 26
    .line 27
    new-array p1, p3, [B

    .line 28
    .line 29
    const/4 p2, 0x0

    .line 30
    invoke-virtual {p0, p1, p2, p3}, Lt1/G;->l([BII)V

    .line 31
    .line 32
    .line 33
    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    const-string v0, "application/x-quicktime-tx3g"

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_1
    const p0, 0x77767474

    .line 41
    .line 42
    .line 43
    if-ne p1, p0, :cond_2

    .line 44
    .line 45
    const-string v0, "application/x-mp4-vtt"

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_2
    const p0, 0x73747070

    .line 49
    .line 50
    .line 51
    if-ne p1, p0, :cond_3

    .line 52
    .line 53
    const-wide/16 v2, 0x0

    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_3
    const p0, 0x63363038

    .line 57
    .line 58
    .line 59
    if-ne p1, p0, :cond_4

    .line 60
    .line 61
    const/4 p0, 0x1

    .line 62
    iput p0, p6, Lh2/b$h;->d:I

    .line 63
    .line 64
    const-string v0, "application/x-mp4-cea-608"

    .line 65
    .line 66
    :goto_0
    new-instance p0, Landroidx/media3/common/r$b;

    .line 67
    .line 68
    invoke-direct {p0}, Landroidx/media3/common/r$b;-><init>()V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p0, p4}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    invoke-virtual {p0, v0}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    invoke-virtual {p0, p5}, Landroidx/media3/common/r$b;->j0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 80
    .line 81
    .line 82
    move-result-object p0

    .line 83
    invoke-virtual {p0, v2, v3}, Landroidx/media3/common/r$b;->y0(J)Landroidx/media3/common/r$b;

    .line 84
    .line 85
    .line 86
    move-result-object p0

    .line 87
    invoke-virtual {p0, v1}, Landroidx/media3/common/r$b;->g0(Ljava/util/List;)Landroidx/media3/common/r$b;

    .line 88
    .line 89
    .line 90
    move-result-object p0

    .line 91
    invoke-virtual {p0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 92
    .line 93
    .line 94
    move-result-object p0

    .line 95
    iput-object p0, p6, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 96
    .line 97
    return-void

    .line 98
    :cond_4
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 99
    .line 100
    invoke-direct {p0}, Ljava/lang/IllegalStateException;-><init>()V

    .line 101
    .line 102
    .line 103
    throw p0
.end method

.method public static E(Lt1/G;)Lh2/b$k;
    .locals 11

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    invoke-static {v1}, Lh2/b;->p(I)I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-nez v1, :cond_0

    .line 15
    .line 16
    const/16 v2, 0x8

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/16 v2, 0x10

    .line 20
    .line 21
    :goto_0
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    const/4 v2, 0x4

    .line 29
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    if-nez v1, :cond_1

    .line 37
    .line 38
    const/4 v0, 0x4

    .line 39
    :cond_1
    const/4 v5, 0x0

    .line 40
    const/4 v6, 0x0

    .line 41
    :goto_1
    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    .line 42
    .line 43
    .line 44
    .line 45
    .line 46
    if-ge v6, v0, :cond_5

    .line 47
    .line 48
    invoke-virtual {p0}, Lt1/G;->e()[B

    .line 49
    .line 50
    .line 51
    move-result-object v9

    .line 52
    add-int v10, v3, v6

    .line 53
    .line 54
    aget-byte v9, v9, v10

    .line 55
    .line 56
    const/4 v10, -0x1

    .line 57
    if-eq v9, v10, :cond_4

    .line 58
    .line 59
    if-nez v1, :cond_2

    .line 60
    .line 61
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 62
    .line 63
    .line 64
    move-result-wide v0

    .line 65
    goto :goto_2

    .line 66
    :cond_2
    invoke-virtual {p0}, Lt1/G;->O()J

    .line 67
    .line 68
    .line 69
    move-result-wide v0

    .line 70
    :goto_2
    const-wide/16 v9, 0x0

    .line 71
    .line 72
    cmp-long v3, v0, v9

    .line 73
    .line 74
    if-nez v3, :cond_3

    .line 75
    .line 76
    goto :goto_3

    .line 77
    :cond_3
    move-wide v7, v0

    .line 78
    goto :goto_3

    .line 79
    :cond_4
    add-int/lit8 v6, v6, 0x1

    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_5
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 83
    .line 84
    .line 85
    :goto_3
    const/16 v0, 0xa

    .line 86
    .line 87
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 88
    .line 89
    .line 90
    move-wide v5, v7

    .line 91
    const/4 v0, 0x0

    .line 92
    invoke-virtual {p0}, Lt1/G;->P()I

    .line 93
    .line 94
    .line 95
    move-result v7

    .line 96
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 100
    .line 101
    .line 102
    move-result v1

    .line 103
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 104
    .line 105
    .line 106
    move-result v3

    .line 107
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 108
    .line 109
    .line 110
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 111
    .line 112
    .line 113
    move-result v2

    .line 114
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 115
    .line 116
    .line 117
    move-result p0

    .line 118
    const/high16 v8, -0x10000

    .line 119
    .line 120
    const/high16 v9, 0x10000

    .line 121
    .line 122
    if-nez v1, :cond_6

    .line 123
    .line 124
    if-ne v3, v9, :cond_6

    .line 125
    .line 126
    if-ne v2, v8, :cond_6

    .line 127
    .line 128
    if-nez p0, :cond_6

    .line 129
    .line 130
    const/16 p0, 0x5a

    .line 131
    .line 132
    const/16 v8, 0x5a

    .line 133
    .line 134
    goto :goto_4

    .line 135
    :cond_6
    if-nez v1, :cond_7

    .line 136
    .line 137
    if-ne v3, v8, :cond_7

    .line 138
    .line 139
    if-ne v2, v9, :cond_7

    .line 140
    .line 141
    if-nez p0, :cond_7

    .line 142
    .line 143
    const/16 p0, 0x10e

    .line 144
    .line 145
    const/16 v8, 0x10e

    .line 146
    .line 147
    goto :goto_4

    .line 148
    :cond_7
    if-ne v1, v8, :cond_8

    .line 149
    .line 150
    if-nez v3, :cond_8

    .line 151
    .line 152
    if-nez v2, :cond_8

    .line 153
    .line 154
    if-ne p0, v8, :cond_8

    .line 155
    .line 156
    const/16 p0, 0xb4

    .line 157
    .line 158
    const/16 v8, 0xb4

    .line 159
    .line 160
    goto :goto_4

    .line 161
    :cond_8
    const/4 v8, 0x0

    .line 162
    :goto_4
    new-instance v3, Lh2/b$k;

    .line 163
    .line 164
    invoke-direct/range {v3 .. v8}, Lh2/b$k;-><init>(IJII)V

    .line 165
    .line 166
    .line 167
    return-object v3
.end method

.method public static F(Landroidx/media3/container/d$b;Landroidx/media3/container/d$c;JLandroidx/media3/common/DrmInitData;ZZ)Lh2/t;
    .locals 22
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    const v3, 0x6d646961

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v3}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 9
    .line 10
    .line 11
    move-result-object v3

    .line 12
    invoke-static {v3}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    check-cast v3, Landroidx/media3/container/d$b;

    .line 17
    .line 18
    const v4, 0x68646c72

    .line 19
    .line 20
    .line 21
    invoke-virtual {v3, v4}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    check-cast v4, Landroidx/media3/container/d$c;

    .line 30
    .line 31
    iget-object v4, v4, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 32
    .line 33
    invoke-static {v4}, Lh2/b;->q(Lt1/G;)I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    invoke-static {v4}, Lh2/b;->e(I)I

    .line 38
    .line 39
    .line 40
    move-result v7

    .line 41
    const/4 v4, -0x1

    .line 42
    const/4 v5, 0x0

    .line 43
    if-ne v7, v4, :cond_0

    .line 44
    .line 45
    return-object v5

    .line 46
    :cond_0
    const v4, 0x746b6864

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, v4}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    check-cast v4, Landroidx/media3/container/d$c;

    .line 58
    .line 59
    iget-object v4, v4, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 60
    .line 61
    invoke-static {v4}, Lh2/b;->E(Lt1/G;)Lh2/b$k;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    const-wide v8, -0x7fffffffffffffffL    # -4.9E-324

    .line 66
    .line 67
    .line 68
    .line 69
    .line 70
    cmp-long v6, p2, v8

    .line 71
    .line 72
    if-nez v6, :cond_1

    .line 73
    .line 74
    invoke-static {v4}, Lh2/b$k;->a(Lh2/b$k;)J

    .line 75
    .line 76
    .line 77
    move-result-wide v10

    .line 78
    move-wide v12, v10

    .line 79
    :goto_0
    move-object/from16 v6, p1

    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_1
    move-wide/from16 v12, p2

    .line 83
    .line 84
    goto :goto_0

    .line 85
    :goto_1
    iget-object v6, v6, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 86
    .line 87
    invoke-static {v6}, Lh2/b;->v(Lt1/G;)Landroidx/media3/container/f;

    .line 88
    .line 89
    .line 90
    move-result-object v6

    .line 91
    iget-wide v10, v6, Landroidx/media3/container/f;->c:J

    .line 92
    .line 93
    cmp-long v6, v12, v8

    .line 94
    .line 95
    if-nez v6, :cond_2

    .line 96
    .line 97
    :goto_2
    move-wide v12, v8

    .line 98
    goto :goto_3

    .line 99
    :cond_2
    const-wide/32 v14, 0xf4240

    .line 100
    .line 101
    .line 102
    move-wide/from16 v16, v10

    .line 103
    .line 104
    invoke-static/range {v12 .. v17}, Lt1/a0;->c1(JJJ)J

    .line 105
    .line 106
    .line 107
    move-result-wide v8

    .line 108
    goto :goto_2

    .line 109
    :goto_3
    const v6, 0x6d696e66

    .line 110
    .line 111
    .line 112
    invoke-virtual {v3, v6}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 113
    .line 114
    .line 115
    move-result-object v6

    .line 116
    invoke-static {v6}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v6

    .line 120
    check-cast v6, Landroidx/media3/container/d$b;

    .line 121
    .line 122
    const v8, 0x7374626c

    .line 123
    .line 124
    .line 125
    invoke-virtual {v6, v8}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 126
    .line 127
    .line 128
    move-result-object v6

    .line 129
    invoke-static {v6}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    move-result-object v6

    .line 133
    check-cast v6, Landroidx/media3/container/d$b;

    .line 134
    .line 135
    const v8, 0x6d646864

    .line 136
    .line 137
    .line 138
    invoke-virtual {v3, v8}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 139
    .line 140
    .line 141
    move-result-object v3

    .line 142
    invoke-static {v3}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v3

    .line 146
    check-cast v3, Landroidx/media3/container/d$c;

    .line 147
    .line 148
    iget-object v3, v3, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 149
    .line 150
    invoke-static {v3}, Lh2/b;->s(Lt1/G;)Lh2/b$e;

    .line 151
    .line 152
    .line 153
    move-result-object v3

    .line 154
    const v8, 0x73747364

    .line 155
    .line 156
    .line 157
    invoke-virtual {v6, v8}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 158
    .line 159
    .line 160
    move-result-object v6

    .line 161
    if-eqz v6, :cond_7

    .line 162
    .line 163
    iget-object v14, v6, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 164
    .line 165
    invoke-static {v4}, Lh2/b$k;->b(Lh2/b$k;)I

    .line 166
    .line 167
    .line 168
    move-result v15

    .line 169
    invoke-static {v4}, Lh2/b$k;->c(Lh2/b$k;)I

    .line 170
    .line 171
    .line 172
    move-result v16

    .line 173
    invoke-static {v3}, Lh2/b$e;->a(Lh2/b$e;)Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v17

    .line 177
    move-object/from16 v18, p4

    .line 178
    .line 179
    move/from16 v19, p6

    .line 180
    .line 181
    invoke-static/range {v14 .. v19}, Lh2/b;->C(Lt1/G;IILjava/lang/String;Landroidx/media3/common/DrmInitData;Z)Lh2/b$h;

    .line 182
    .line 183
    .line 184
    move-result-object v6

    .line 185
    if-nez p5, :cond_3

    .line 186
    .line 187
    const v8, 0x65647473

    .line 188
    .line 189
    .line 190
    invoke-virtual {v0, v8}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    if-eqz v0, :cond_3

    .line 195
    .line 196
    invoke-static {v0}, Lh2/b;->l(Landroidx/media3/container/d$b;)Landroid/util/Pair;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    if-eqz v0, :cond_3

    .line 201
    .line 202
    iget-object v8, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    .line 203
    .line 204
    check-cast v8, [J

    .line 205
    .line 206
    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    .line 207
    .line 208
    check-cast v0, [J

    .line 209
    .line 210
    move-object/from16 v21, v0

    .line 211
    .line 212
    move-object/from16 v20, v8

    .line 213
    .line 214
    goto :goto_4

    .line 215
    :cond_3
    move-object/from16 v20, v5

    .line 216
    .line 217
    move-object/from16 v21, v20

    .line 218
    .line 219
    :goto_4
    iget-object v0, v6, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 220
    .line 221
    if-nez v0, :cond_4

    .line 222
    .line 223
    return-object v5

    .line 224
    :cond_4
    invoke-static {v4}, Lh2/b$k;->d(Lh2/b$k;)I

    .line 225
    .line 226
    .line 227
    move-result v0

    .line 228
    if-eqz v0, :cond_6

    .line 229
    .line 230
    new-instance v0, Landroidx/media3/container/c;

    .line 231
    .line 232
    invoke-static {v4}, Lh2/b$k;->d(Lh2/b$k;)I

    .line 233
    .line 234
    .line 235
    move-result v5

    .line 236
    invoke-direct {v0, v5}, Landroidx/media3/container/c;-><init>(I)V

    .line 237
    .line 238
    .line 239
    iget-object v5, v6, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 240
    .line 241
    invoke-virtual {v5}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    .line 242
    .line 243
    .line 244
    move-result-object v5

    .line 245
    iget-object v8, v6, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 246
    .line 247
    iget-object v8, v8, Landroidx/media3/common/r;->l:Landroidx/media3/common/x;

    .line 248
    .line 249
    if-eqz v8, :cond_5

    .line 250
    .line 251
    new-array v2, v2, [Landroidx/media3/common/x$a;

    .line 252
    .line 253
    aput-object v0, v2, v1

    .line 254
    .line 255
    invoke-virtual {v8, v2}, Landroidx/media3/common/x;->a([Landroidx/media3/common/x$a;)Landroidx/media3/common/x;

    .line 256
    .line 257
    .line 258
    move-result-object v0

    .line 259
    goto :goto_5

    .line 260
    :cond_5
    new-instance v8, Landroidx/media3/common/x;

    .line 261
    .line 262
    new-array v2, v2, [Landroidx/media3/common/x$a;

    .line 263
    .line 264
    aput-object v0, v2, v1

    .line 265
    .line 266
    invoke-direct {v8, v2}, Landroidx/media3/common/x;-><init>([Landroidx/media3/common/x$a;)V

    .line 267
    .line 268
    .line 269
    move-object v0, v8

    .line 270
    :goto_5
    invoke-virtual {v5, v0}, Landroidx/media3/common/r$b;->n0(Landroidx/media3/common/x;)Landroidx/media3/common/r$b;

    .line 271
    .line 272
    .line 273
    move-result-object v0

    .line 274
    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 275
    .line 276
    .line 277
    move-result-object v0

    .line 278
    :goto_6
    move-object/from16 v16, v0

    .line 279
    .line 280
    goto :goto_7

    .line 281
    :cond_6
    iget-object v0, v6, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 282
    .line 283
    goto :goto_6

    .line 284
    :goto_7
    new-instance v5, Lh2/t;

    .line 285
    .line 286
    invoke-static {v4}, Lh2/b$k;->b(Lh2/b$k;)I

    .line 287
    .line 288
    .line 289
    move-result v0

    .line 290
    invoke-static {v3}, Lh2/b$e;->b(Lh2/b$e;)J

    .line 291
    .line 292
    .line 293
    move-result-wide v8

    .line 294
    invoke-static {v3}, Lh2/b$e;->c(Lh2/b$e;)J

    .line 295
    .line 296
    .line 297
    move-result-wide v14

    .line 298
    iget v1, v6, Lh2/b$h;->d:I

    .line 299
    .line 300
    iget-object v2, v6, Lh2/b$h;->a:[Lh2/u;

    .line 301
    .line 302
    iget v3, v6, Lh2/b$h;->c:I

    .line 303
    .line 304
    move v6, v0

    .line 305
    move/from16 v17, v1

    .line 306
    .line 307
    move-object/from16 v18, v2

    .line 308
    .line 309
    move/from16 v19, v3

    .line 310
    .line 311
    invoke-direct/range {v5 .. v21}, Lh2/t;-><init>(IIJJJJLandroidx/media3/common/r;I[Lh2/u;I[J[J)V

    .line 312
    .line 313
    .line 314
    return-object v5

    .line 315
    :cond_7
    const-string v0, "Malformed sample table (stbl) missing sample description (stsd)"

    .line 316
    .line 317
    invoke-static {v0, v5}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 318
    .line 319
    .line 320
    move-result-object v0

    .line 321
    throw v0
.end method

.method public static G(Landroidx/media3/container/d$b;LN1/E;JLandroidx/media3/common/DrmInitData;ZZLcom/google/common/base/Function;)Ljava/util/List;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/container/d$b;",
            "LN1/E;",
            "J",
            "Landroidx/media3/common/DrmInitData;",
            "ZZ",
            "Lcom/google/common/base/Function<",
            "Lh2/t;",
            "Lh2/t;",
            ">;)",
            "Ljava/util/List<",
            "Lh2/w;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    :goto_0
    iget-object v2, p0, Landroidx/media3/container/d$b;->d:Ljava/util/List;

    .line 8
    .line 9
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    if-ge v1, v2, :cond_2

    .line 14
    .line 15
    iget-object v2, p0, Landroidx/media3/container/d$b;->d:Ljava/util/List;

    .line 16
    .line 17
    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    move-object v3, v2

    .line 22
    check-cast v3, Landroidx/media3/container/d$b;

    .line 23
    .line 24
    iget v2, v3, Landroidx/media3/container/d;->a:I

    .line 25
    .line 26
    const v4, 0x7472616b

    .line 27
    .line 28
    .line 29
    if-eq v2, v4, :cond_0

    .line 30
    .line 31
    move-object/from16 v3, p7

    .line 32
    .line 33
    goto :goto_1

    .line 34
    :cond_0
    const v2, 0x6d766864

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0, v2}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    invoke-static {v2}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    move-object v4, v2

    .line 46
    check-cast v4, Landroidx/media3/container/d$c;

    .line 47
    .line 48
    move-wide v5, p2

    .line 49
    move-object v7, p4

    .line 50
    move v8, p5

    .line 51
    move/from16 v9, p6

    .line 52
    .line 53
    invoke-static/range {v3 .. v9}, Lh2/b;->F(Landroidx/media3/container/d$b;Landroidx/media3/container/d$c;JLandroidx/media3/common/DrmInitData;ZZ)Lh2/t;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    move-object v4, v3

    .line 58
    move-object/from16 v3, p7

    .line 59
    .line 60
    invoke-interface {v3, v2}, Lcom/google/common/base/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    check-cast v2, Lh2/t;

    .line 65
    .line 66
    if-nez v2, :cond_1

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_1
    const v5, 0x6d646961

    .line 70
    .line 71
    .line 72
    invoke-virtual {v4, v5}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 73
    .line 74
    .line 75
    move-result-object v4

    .line 76
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v4

    .line 80
    check-cast v4, Landroidx/media3/container/d$b;

    .line 81
    .line 82
    const v5, 0x6d696e66

    .line 83
    .line 84
    .line 85
    invoke-virtual {v4, v5}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 86
    .line 87
    .line 88
    move-result-object v4

    .line 89
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v4

    .line 93
    check-cast v4, Landroidx/media3/container/d$b;

    .line 94
    .line 95
    const v5, 0x7374626c

    .line 96
    .line 97
    .line 98
    invoke-virtual {v4, v5}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 99
    .line 100
    .line 101
    move-result-object v4

    .line 102
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v4

    .line 106
    check-cast v4, Landroidx/media3/container/d$b;

    .line 107
    .line 108
    invoke-static {v2, v4, p1}, Lh2/b;->A(Lh2/t;Landroidx/media3/container/d$b;LN1/E;)Lh2/w;

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 113
    .line 114
    .line 115
    :goto_1
    add-int/lit8 v1, v1, 0x1

    .line 116
    .line 117
    goto :goto_0

    .line 118
    :cond_2
    return-object v0
.end method

.method public static H(Landroidx/media3/container/d$c;)Landroidx/media3/common/x;
    .locals 6

    .line 1
    iget-object p0, p0, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 2
    .line 3
    const/16 v0, 0x8

    .line 4
    .line 5
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 6
    .line 7
    .line 8
    new-instance v1, Landroidx/media3/common/x;

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    new-array v2, v2, [Landroidx/media3/common/x$a;

    .line 12
    .line 13
    invoke-direct {v1, v2}, Landroidx/media3/common/x;-><init>([Landroidx/media3/common/x$a;)V

    .line 14
    .line 15
    .line 16
    :goto_0
    invoke-virtual {p0}, Lt1/G;->a()I

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    if-lt v2, v0, :cond_3

    .line 21
    .line 22
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 27
    .line 28
    .line 29
    move-result v3

    .line 30
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 31
    .line 32
    .line 33
    move-result v4

    .line 34
    const v5, 0x6d657461

    .line 35
    .line 36
    .line 37
    if-ne v4, v5, :cond_0

    .line 38
    .line 39
    invoke-virtual {p0, v2}, Lt1/G;->W(I)V

    .line 40
    .line 41
    .line 42
    add-int v4, v2, v3

    .line 43
    .line 44
    invoke-static {p0, v4}, Lh2/b;->I(Lt1/G;I)Landroidx/media3/common/x;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    invoke-virtual {v1, v4}, Landroidx/media3/common/x;->b(Landroidx/media3/common/x;)Landroidx/media3/common/x;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    goto :goto_1

    .line 53
    :cond_0
    const v5, 0x736d7461

    .line 54
    .line 55
    .line 56
    if-ne v4, v5, :cond_1

    .line 57
    .line 58
    invoke-virtual {p0, v2}, Lt1/G;->W(I)V

    .line 59
    .line 60
    .line 61
    add-int v4, v2, v3

    .line 62
    .line 63
    invoke-static {p0, v4}, Lh2/r;->b(Lt1/G;I)Landroidx/media3/common/x;

    .line 64
    .line 65
    .line 66
    move-result-object v4

    .line 67
    invoke-virtual {v1, v4}, Landroidx/media3/common/x;->b(Landroidx/media3/common/x;)Landroidx/media3/common/x;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    goto :goto_1

    .line 72
    :cond_1
    const v5, -0x56878686

    .line 73
    .line 74
    .line 75
    if-ne v4, v5, :cond_2

    .line 76
    .line 77
    invoke-static {p0}, Lh2/b;->L(Lt1/G;)Landroidx/media3/common/x;

    .line 78
    .line 79
    .line 80
    move-result-object v4

    .line 81
    invoke-virtual {v1, v4}, Landroidx/media3/common/x;->b(Landroidx/media3/common/x;)Landroidx/media3/common/x;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    :cond_2
    :goto_1
    add-int/2addr v2, v3

    .line 86
    invoke-virtual {p0, v2}, Lt1/G;->W(I)V

    .line 87
    .line 88
    .line 89
    goto :goto_0

    .line 90
    :cond_3
    return-object v1
.end method

.method public static I(Lt1/G;I)Landroidx/media3/common/x;
    .locals 4

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0}, Lh2/b;->f(Lt1/G;)V

    .line 7
    .line 8
    .line 9
    :goto_0
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-ge v0, p1, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const v3, 0x696c7374

    .line 28
    .line 29
    .line 30
    if-ne v2, v3, :cond_0

    .line 31
    .line 32
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 33
    .line 34
    .line 35
    add-int/2addr v0, v1

    .line 36
    invoke-static {p0, v0}, Lh2/b;->r(Lt1/G;I)Landroidx/media3/common/x;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    return-object p0

    .line 41
    :cond_0
    add-int/2addr v0, v1

    .line 42
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 43
    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_1
    const/4 p0, 0x0

    .line 47
    return-object p0
.end method

.method public static J(Lt1/G;II)Lh2/b$l;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    add-int/lit8 v0, p1, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    const/4 v1, 0x0

    .line 11
    move-object v2, v1

    .line 12
    :goto_0
    sub-int v3, v0, p1

    .line 13
    .line 14
    if-ge v3, p2, :cond_2

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-lez v3, :cond_0

    .line 24
    .line 25
    const/4 v4, 0x1

    .line 26
    goto :goto_1

    .line 27
    :cond_0
    const/4 v4, 0x0

    .line 28
    :goto_1
    const-string v5, "childAtomSize must be positive"

    .line 29
    .line 30
    invoke-static {v4, v5}, LN1/u;->a(ZLjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    const v5, 0x65796573

    .line 38
    .line 39
    .line 40
    if-ne v4, v5, :cond_1

    .line 41
    .line 42
    invoke-static {p0, v0, v3}, Lh2/b;->B(Lt1/G;II)Lh2/b$d;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    :cond_1
    add-int/2addr v0, v3

    .line 47
    goto :goto_0

    .line 48
    :cond_2
    if-nez v2, :cond_3

    .line 49
    .line 50
    return-object v1

    .line 51
    :cond_3
    new-instance p0, Lh2/b$l;

    .line 52
    .line 53
    invoke-direct {p0, v2}, Lh2/b$l;-><init>(Lh2/b$d;)V

    .line 54
    .line 55
    .line 56
    return-object p0
.end method

.method public static K(Lt1/G;IIIILjava/lang/String;ILandroidx/media3/common/DrmInitData;Lh2/b$h;I)V
    .locals 41
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    move-object/from16 v0, p0

    move/from16 v1, p2

    move/from16 v2, p3

    move-object/from16 v3, p7

    move-object/from16 v4, p8

    add-int/lit8 v5, v1, 0x10

    .line 1
    invoke-virtual {v0, v5}, Lt1/G;->W(I)V

    const/16 v5, 0x10

    .line 2
    invoke-virtual {v0, v5}, Lt1/G;->X(I)V

    .line 3
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v5

    .line 4
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v6

    const/16 v7, 0x32

    .line 5
    invoke-virtual {v0, v7}, Lt1/G;->X(I)V

    .line 6
    invoke-virtual {v0}, Lt1/G;->f()I

    move-result v7

    const v8, 0x656e6376

    move/from16 v10, p1

    if-ne v10, v8, :cond_2

    .line 7
    invoke-static {v0, v1, v2}, Lh2/b;->y(Lt1/G;II)Landroid/util/Pair;

    move-result-object v8

    if-eqz v8, :cond_1

    .line 8
    iget-object v10, v8, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v10, Ljava/lang/Integer;

    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    move-result v10

    if-nez v3, :cond_0

    const/4 v3, 0x0

    goto :goto_0

    .line 9
    :cond_0
    iget-object v11, v8, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v11, Lh2/u;

    iget-object v11, v11, Lh2/u;->b:Ljava/lang/String;

    invoke-virtual {v3, v11}, Landroidx/media3/common/DrmInitData;->c(Ljava/lang/String;)Landroidx/media3/common/DrmInitData;

    move-result-object v3

    .line 10
    :goto_0
    iget-object v11, v4, Lh2/b$h;->a:[Lh2/u;

    iget-object v8, v8, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v8, Lh2/u;

    aput-object v8, v11, p9

    .line 11
    :cond_1
    invoke-virtual {v0, v7}, Lt1/G;->W(I)V

    :cond_2
    const v8, 0x6d317620

    .line 12
    const-string v11, "video/3gpp"

    if-ne v10, v8, :cond_3

    .line 13
    const-string v8, "video/mpeg"

    goto :goto_1

    :cond_3
    const v8, 0x48323633

    if-ne v10, v8, :cond_4

    move-object v8, v11

    goto :goto_1

    :cond_4
    const/4 v8, 0x0

    :goto_1
    const/high16 v14, 0x3f800000    # 1.0f

    const/16 v15, 0x8

    const/16 v13, 0x8

    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, -0x1

    const/16 v20, -0x1

    const/16 v21, -0x1

    const/16 v22, -0x1

    const/16 v23, -0x1

    const/16 v24, -0x1

    const/16 v25, 0x0

    const/16 v26, 0x0

    const/16 v27, 0x0

    const/16 v28, 0x0

    const/16 v29, 0x0

    :goto_2
    sub-int v12, v7, v1

    if-ge v12, v2, :cond_5

    .line 14
    invoke-virtual {v0, v7}, Lt1/G;->W(I)V

    .line 15
    invoke-virtual {v0}, Lt1/G;->f()I

    move-result v12

    .line 16
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v9

    if-nez v9, :cond_6

    .line 17
    invoke-virtual {v0}, Lt1/G;->f()I

    move-result v30

    sub-int v1, v30, p2

    if-ne v1, v2, :cond_6

    :cond_5
    move-object/from16 v28, v3

    move-object/from16 v35, v16

    move/from16 v40, v19

    move/from16 v1, v22

    move/from16 v33, v23

    move/from16 v3, v24

    const/4 v2, 0x0

    move/from16 v22, v13

    move/from16 v24, v15

    goto/16 :goto_2d

    :cond_6
    if-lez v9, :cond_7

    const/4 v1, 0x1

    :goto_3
    const/16 p9, 0x1

    goto :goto_4

    :cond_7
    const/4 v1, 0x0

    goto :goto_3

    .line 18
    :goto_4
    const-string v2, "childAtomSize must be positive"

    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    .line 19
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v1

    const v2, 0x61766343

    if-ne v1, v2, :cond_a

    if-nez v8, :cond_8

    const/4 v1, 0x1

    :goto_5
    const/4 v2, 0x0

    goto :goto_6

    :cond_8
    const/4 v1, 0x0

    goto :goto_5

    .line 20
    :goto_6
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    add-int/lit8 v12, v12, 0x8

    .line 21
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    .line 22
    invoke-static {v0}, LN1/d;->b(Lt1/G;)LN1/d;

    move-result-object v1

    .line 23
    iget-object v2, v1, LN1/d;->a:Ljava/util/List;

    .line 24
    iget v8, v1, LN1/d;->b:I

    iput v8, v4, Lh2/b$h;->c:I

    if-nez v29, :cond_9

    .line 25
    iget v14, v1, LN1/d;->k:F

    .line 26
    :cond_9
    iget-object v8, v1, LN1/d;->l:Ljava/lang/String;

    .line 27
    iget v12, v1, LN1/d;->j:I

    .line 28
    iget v13, v1, LN1/d;->g:I

    .line 29
    iget v15, v1, LN1/d;->h:I

    move-object/from16 v16, v2

    .line 30
    iget v2, v1, LN1/d;->i:I

    move/from16 v17, v2

    .line 31
    iget v2, v1, LN1/d;->e:I

    .line 32
    iget v1, v1, LN1/d;->f:I

    .line 33
    const-string v20, "video/avc"

    move/from16 v30, v7

    move/from16 v23, v10

    move-object/from16 v31, v11

    move/from16 v22, v13

    move/from16 v33, v15

    move/from16 v24, v17

    const/4 v7, 0x0

    move v13, v1

    move v15, v2

    move-object/from16 v17, v8

    move-object/from16 v8, v20

    move-object/from16 v1, v28

    const/4 v2, 0x0

    move-object/from16 v28, v3

    move/from16 v20, v12

    :goto_7
    const/4 v12, -0x1

    goto/16 :goto_2c

    :cond_a
    const v2, 0x68766343

    move/from16 v30, v7

    const-string v7, "video/hevc"

    if-ne v1, v2, :cond_e

    if-nez v8, :cond_b

    const/4 v1, 0x1

    :goto_8
    const/4 v2, 0x0

    goto :goto_9

    :cond_b
    const/4 v1, 0x0

    goto :goto_8

    .line 34
    :goto_9
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    add-int/lit8 v12, v12, 0x8

    .line 35
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    .line 36
    invoke-static {v0}, LN1/F;->a(Lt1/G;)LN1/F;

    move-result-object v1

    .line 37
    iget-object v2, v1, LN1/F;->a:Ljava/util/List;

    .line 38
    iget v8, v1, LN1/F;->b:I

    iput v8, v4, Lh2/b$h;->c:I

    if-nez v29, :cond_c

    .line 39
    iget v14, v1, LN1/F;->l:F

    .line 40
    :cond_c
    iget v8, v1, LN1/F;->m:I

    .line 41
    iget v12, v1, LN1/F;->c:I

    .line 42
    iget-object v13, v1, LN1/F;->n:Ljava/lang/String;

    .line 43
    iget v15, v1, LN1/F;->k:I

    move-object/from16 v16, v2

    const/4 v2, -0x1

    if-eq v15, v2, :cond_d

    move/from16 v19, v15

    .line 44
    :cond_d
    iget v2, v1, LN1/F;->h:I

    .line 45
    iget v15, v1, LN1/F;->i:I

    move/from16 v17, v2

    .line 46
    iget v2, v1, LN1/F;->j:I

    move/from16 v20, v2

    .line 47
    iget v2, v1, LN1/F;->f:I

    move/from16 v21, v2

    .line 48
    iget v2, v1, LN1/F;->g:I

    .line 49
    iget-object v1, v1, LN1/F;->o:Landroidx/media3/container/g$k;

    move-object/from16 v28, v3

    move/from16 v23, v10

    move-object/from16 v31, v11

    move/from16 v33, v15

    move/from16 v22, v17

    move/from16 v24, v20

    move/from16 v15, v21

    move/from16 v20, v8

    move/from16 v21, v12

    move-object/from16 v17, v13

    const/4 v12, -0x1

    move v13, v2

    move-object v8, v7

    :goto_a
    const/4 v2, 0x0

    :goto_b
    const/4 v7, 0x0

    goto/16 :goto_2c

    :cond_e
    const v2, 0x6c687643

    move-object/from16 v31, v11

    const/4 v11, 0x2

    if-ne v1, v2, :cond_1a

    .line 50
    invoke-virtual {v7, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const-string v2, "lhvC must follow hvcC atom"

    .line 51
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    move-object/from16 v2, v28

    if-eqz v2, :cond_f

    .line 52
    iget-object v1, v2, Landroidx/media3/container/g$k;->b:Lcom/google/common/collect/ImmutableList;

    .line 53
    invoke-virtual {v1}, Ljava/util/AbstractCollection;->size()I

    move-result v1

    if-lt v1, v11, :cond_f

    const/4 v1, 0x1

    goto :goto_c

    :cond_f
    const/4 v1, 0x0

    :goto_c
    const-string v7, "must have at least two layers"

    .line 54
    invoke-static {v1, v7}, LN1/u;->a(ZLjava/lang/String;)V

    add-int/lit8 v12, v12, 0x8

    .line 55
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    .line 56
    invoke-static {v2}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/container/g$k;

    invoke-static {v0, v1}, LN1/F;->c(Lt1/G;Landroidx/media3/container/g$k;)LN1/F;

    move-result-object v1

    .line 57
    iget v7, v4, Lh2/b$h;->c:I

    iget v8, v1, LN1/F;->b:I

    if-ne v7, v8, :cond_10

    const/4 v7, 0x1

    goto :goto_d

    :cond_10
    const/4 v7, 0x0

    :goto_d
    const-string v8, "nalUnitLengthFieldLength must be same for both hvcC and lhvC atoms"

    invoke-static {v7, v8}, LN1/u;->a(ZLjava/lang/String;)V

    .line 58
    iget v7, v1, LN1/F;->h:I

    const/4 v8, -0x1

    move/from16 v11, v22

    if-eq v7, v8, :cond_12

    if-ne v11, v7, :cond_11

    const/4 v7, 0x1

    goto :goto_e

    :cond_11
    const/4 v7, 0x0

    .line 59
    :goto_e
    const-string v12, "colorSpace must be the same for both views"

    invoke-static {v7, v12}, LN1/u;->a(ZLjava/lang/String;)V

    .line 60
    :cond_12
    iget v7, v1, LN1/F;->i:I

    move/from16 v12, v23

    if-eq v7, v8, :cond_14

    if-ne v12, v7, :cond_13

    const/4 v7, 0x1

    goto :goto_f

    :cond_13
    const/4 v7, 0x0

    .line 61
    :goto_f
    const-string v8, "colorRange must be the same for both views"

    invoke-static {v7, v8}, LN1/u;->a(ZLjava/lang/String;)V

    .line 62
    :cond_14
    iget v7, v1, LN1/F;->j:I

    const/4 v8, -0x1

    if-eq v7, v8, :cond_16

    move/from16 v8, v24

    if-ne v8, v7, :cond_15

    const/4 v7, 0x1

    :goto_10
    move/from16 v17, v8

    goto :goto_11

    :cond_15
    const/4 v7, 0x0

    goto :goto_10

    .line 63
    :goto_11
    const-string v8, "colorTransfer must be the same for both views"

    invoke-static {v7, v8}, LN1/u;->a(ZLjava/lang/String;)V

    goto :goto_12

    :cond_16
    move/from16 v17, v24

    .line 64
    :goto_12
    iget v7, v1, LN1/F;->f:I

    if-ne v15, v7, :cond_17

    const/4 v7, 0x1

    goto :goto_13

    :cond_17
    const/4 v7, 0x0

    :goto_13
    const-string v8, "bitdepthLuma must be the same for both views"

    invoke-static {v7, v8}, LN1/u;->a(ZLjava/lang/String;)V

    .line 65
    iget v7, v1, LN1/F;->g:I

    if-ne v13, v7, :cond_18

    const/4 v7, 0x1

    goto :goto_14

    :cond_18
    const/4 v7, 0x0

    :goto_14
    const-string v8, "bitdepthChroma must be the same for both views"

    invoke-static {v7, v8}, LN1/u;->a(ZLjava/lang/String;)V

    move-object/from16 v7, v16

    if-eqz v7, :cond_19

    .line 66
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->builder()Lcom/google/common/collect/ImmutableList$Builder;

    move-result-object v8

    .line 67
    invoke-virtual {v8, v7}, Lcom/google/common/collect/ImmutableList$Builder;->l(Ljava/lang/Iterable;)Lcom/google/common/collect/ImmutableList$Builder;

    move-result-object v7

    iget-object v8, v1, LN1/F;->a:Ljava/util/List;

    .line 68
    invoke-virtual {v7, v8}, Lcom/google/common/collect/ImmutableList$Builder;->l(Ljava/lang/Iterable;)Lcom/google/common/collect/ImmutableList$Builder;

    move-result-object v7

    .line 69
    invoke-virtual {v7}, Lcom/google/common/collect/ImmutableList$Builder;->n()Lcom/google/common/collect/ImmutableList;

    move-result-object v16

    move-object/from16 v7, v16

    move/from16 v16, v11

    goto :goto_15

    .line 70
    :cond_19
    const-string v8, "initializationData must be already set from hvcC atom"

    move/from16 v16, v11

    const/4 v11, 0x0

    invoke-static {v11, v8}, LN1/u;->a(ZLjava/lang/String;)V

    .line 71
    :goto_15
    iget-object v1, v1, LN1/F;->n:Ljava/lang/String;

    .line 72
    const-string v8, "video/mv-hevc"

    move-object/from16 v28, v3

    move/from16 v23, v10

    move/from16 v33, v12

    move/from16 v22, v16

    move/from16 v24, v17

    const/4 v12, -0x1

    move-object/from16 v17, v1

    move-object v1, v2

    move-object/from16 v16, v7

    goto/16 :goto_a

    :cond_1a
    move-object/from16 v7, v16

    move/from16 v32, v22

    move/from16 v33, v23

    move/from16 v34, v24

    move-object/from16 v2, v28

    const v11, 0x76657875

    if-ne v1, v11, :cond_1f

    .line 73
    invoke-static {v0, v12, v9}, Lh2/b;->J(Lt1/G;II)Lh2/b$l;

    move-result-object v1

    if-eqz v1, :cond_1b

    .line 74
    invoke-static {v1}, Lh2/b$l;->a(Lh2/b$l;)Lh2/b$d;

    move-result-object v11

    if-eqz v11, :cond_1b

    if-eqz v2, :cond_1c

    .line 75
    iget-object v11, v2, Landroidx/media3/container/g$k;->b:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v11}, Ljava/util/AbstractCollection;->size()I

    move-result v11

    const/4 v12, 0x2

    if-lt v11, v12, :cond_1c

    .line 76
    invoke-virtual {v1}, Lh2/b$l;->b()Z

    move-result v11

    const-string v12, "both eye views must be marked as available"

    .line 77
    invoke-static {v11, v12}, LN1/u;->a(ZLjava/lang/String;)V

    .line 78
    invoke-static {v1}, Lh2/b$l;->a(Lh2/b$l;)Lh2/b$d;

    move-result-object v1

    invoke-static {v1}, Lh2/b$d;->a(Lh2/b$d;)Lh2/b$g;

    move-result-object v1

    invoke-static {v1}, Lh2/b$g;->c(Lh2/b$g;)Z

    move-result v1

    xor-int/lit8 v1, v1, 0x1

    const-string v11, "for MV-HEVC, eye_views_reversed must be set to false"

    .line 79
    invoke-static {v1, v11}, LN1/u;->a(ZLjava/lang/String;)V

    :cond_1b
    move/from16 v11, v19

    goto :goto_16

    :cond_1c
    move/from16 v11, v19

    const/4 v12, -0x1

    if-ne v11, v12, :cond_1e

    .line 80
    invoke-static {v1}, Lh2/b$l;->a(Lh2/b$l;)Lh2/b$d;

    move-result-object v1

    invoke-static {v1}, Lh2/b$d;->a(Lh2/b$d;)Lh2/b$g;

    move-result-object v1

    invoke-static {v1}, Lh2/b$g;->c(Lh2/b$g;)Z

    move-result v1

    if-eqz v1, :cond_1d

    const/4 v1, 0x5

    const/16 v19, 0x5

    goto :goto_17

    :cond_1d
    const/4 v1, 0x4

    const/16 v19, 0x4

    goto :goto_17

    :cond_1e
    :goto_16
    move/from16 v19, v11

    :goto_17
    move-object v1, v2

    move-object/from16 v28, v3

    move-object/from16 v16, v7

    move/from16 v23, v10

    move/from16 v22, v32

    move/from16 v24, v34

    :goto_18
    const/4 v2, 0x0

    :goto_19
    const/4 v7, 0x0

    goto/16 :goto_7

    :cond_1f
    move/from16 v11, v19

    move-object/from16 v19, v2

    const v2, 0x64766343

    if-eq v1, v2, :cond_20

    const v2, 0x64767643

    if-ne v1, v2, :cond_21

    :cond_20
    move-object/from16 v28, v3

    move-object/from16 v35, v7

    move/from16 v23, v10

    move/from16 v40, v11

    move/from16 v22, v13

    move/from16 v24, v15

    move/from16 v1, v32

    move/from16 v3, v34

    const/4 v2, 0x0

    const/4 v7, 0x0

    const/4 v12, -0x1

    goto/16 :goto_2b

    :cond_21
    const v2, 0x76706343

    if-ne v1, v2, :cond_27

    if-nez v8, :cond_22

    const/4 v1, 0x1

    :goto_1a
    const/4 v2, 0x0

    goto :goto_1b

    :cond_22
    const/4 v1, 0x0

    goto :goto_1a

    .line 81
    :goto_1b
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    const v1, 0x76703038

    .line 82
    const-string v2, "video/x-vnd.on2.vp9"

    if-ne v10, v1, :cond_23

    const-string v1, "video/x-vnd.on2.vp8"

    goto :goto_1c

    :cond_23
    move-object v1, v2

    :goto_1c
    add-int/lit8 v12, v12, 0xc

    .line 83
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    .line 84
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v8

    int-to-byte v8, v8

    .line 85
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v12

    int-to-byte v12, v12

    .line 86
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v13

    shr-int/lit8 v15, v13, 0x4

    shr-int/lit8 v22, v13, 0x1

    move/from16 v23, v10

    and-int/lit8 v10, v22, 0x7

    int-to-byte v10, v10

    .line 87
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_24

    int-to-byte v2, v15

    .line 88
    invoke-static {v8, v12, v2, v10}, Lt1/i;->h(BBBB)Lcom/google/common/collect/ImmutableList;

    move-result-object v2

    move-object v7, v2

    :cond_24
    and-int/lit8 v2, v13, 0x1

    if-eqz v2, :cond_25

    const/4 v2, 0x1

    goto :goto_1d

    :cond_25
    const/4 v2, 0x0

    .line 89
    :goto_1d
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v8

    .line 90
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v10

    .line 91
    invoke-static {v8}, Landroidx/media3/common/i;->j(I)I

    move-result v22

    if-eqz v2, :cond_26

    const/16 v16, 0x1

    goto :goto_1e

    :cond_26
    const/16 v16, 0x2

    .line 92
    :goto_1e
    invoke-static {v10}, Landroidx/media3/common/i;->k(I)I

    move-result v24

    move-object v8, v1

    move-object/from16 v28, v3

    move v13, v15

    move/from16 v33, v16

    move-object/from16 v1, v19

    const/4 v2, 0x0

    const/4 v12, -0x1

    move-object/from16 v16, v7

    move/from16 v19, v11

    goto/16 :goto_b

    :cond_27
    move/from16 v23, v10

    const v2, 0x61763143

    if-ne v1, v2, :cond_28

    add-int/lit8 v1, v9, -0x8

    .line 93
    new-array v2, v1, [B

    const/4 v7, 0x0

    .line 94
    invoke-virtual {v0, v2, v7, v1}, Lt1/G;->l([BII)V

    .line 95
    invoke-static {v2}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v16

    add-int/lit8 v12, v12, 0x8

    .line 96
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    .line 97
    invoke-static {v0}, Lh2/b;->i(Lt1/G;)Landroidx/media3/common/i;

    move-result-object v1

    .line 98
    iget v2, v1, Landroidx/media3/common/i;->e:I

    .line 99
    iget v7, v1, Landroidx/media3/common/i;->f:I

    .line 100
    iget v8, v1, Landroidx/media3/common/i;->a:I

    .line 101
    iget v10, v1, Landroidx/media3/common/i;->b:I

    .line 102
    iget v1, v1, Landroidx/media3/common/i;->c:I

    .line 103
    const-string v12, "video/av01"

    move/from16 v24, v1

    move v15, v2

    move-object/from16 v28, v3

    move v13, v7

    move/from16 v22, v8

    move/from16 v33, v10

    move-object v8, v12

    move-object/from16 v1, v19

    :goto_1f
    const/4 v2, 0x0

    const/4 v7, 0x0

    const/4 v12, -0x1

    move/from16 v19, v11

    goto/16 :goto_2c

    :cond_28
    const v2, 0x636c6c69

    if-ne v1, v2, :cond_2a

    if-nez v25, :cond_29

    .line 104
    invoke-static {}, Lh2/b;->a()Ljava/nio/ByteBuffer;

    move-result-object v25

    :cond_29
    move-object/from16 v1, v25

    const/16 v2, 0x15

    .line 105
    invoke-virtual {v1, v2}, Ljava/nio/ByteBuffer;->position(I)Ljava/nio/Buffer;

    .line 106
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v2

    invoke-virtual {v1, v2}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 107
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v2

    invoke-virtual {v1, v2}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    move-object/from16 v25, v1

    move-object/from16 v28, v3

    move-object/from16 v16, v7

    move-object/from16 v1, v19

    move/from16 v22, v32

    move/from16 v24, v34

    goto :goto_1f

    :cond_2a
    const v2, 0x6d646376

    if-ne v1, v2, :cond_2c

    if-nez v25, :cond_2b

    .line 108
    invoke-static {}, Lh2/b;->a()Ljava/nio/ByteBuffer;

    move-result-object v25

    :cond_2b
    move-object/from16 v1, v25

    .line 109
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v2

    .line 110
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v10

    .line 111
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v12

    .line 112
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v4

    move/from16 v22, v13

    .line 113
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v13

    move/from16 v24, v15

    .line 114
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v15

    move-object/from16 v28, v3

    .line 115
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v3

    move-object/from16 v35, v7

    .line 116
    invoke-virtual {v0}, Lt1/G;->D()S

    move-result v7

    .line 117
    invoke-virtual {v0}, Lt1/G;->J()J

    move-result-wide v36

    .line 118
    invoke-virtual {v0}, Lt1/G;->J()J

    move-result-wide v38

    move/from16 v40, v11

    const/4 v11, 0x1

    .line 119
    invoke-virtual {v1, v11}, Ljava/nio/ByteBuffer;->position(I)Ljava/nio/Buffer;

    .line 120
    invoke-virtual {v1, v13}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 121
    invoke-virtual {v1, v15}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 122
    invoke-virtual {v1, v2}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 123
    invoke-virtual {v1, v10}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 124
    invoke-virtual {v1, v12}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 125
    invoke-virtual {v1, v4}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 126
    invoke-virtual {v1, v3}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 127
    invoke-virtual {v1, v7}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    const-wide/16 v2, 0x2710

    .line 128
    div-long v10, v36, v2

    long-to-int v4, v10

    int-to-short v4, v4

    invoke-virtual {v1, v4}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 129
    div-long v2, v38, v2

    long-to-int v3, v2

    int-to-short v2, v3

    invoke-virtual {v1, v2}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    move-object/from16 v25, v1

    move-object/from16 v1, v19

    move/from16 v13, v22

    move/from16 v15, v24

    move/from16 v22, v32

    move/from16 v24, v34

    move-object/from16 v16, v35

    move/from16 v19, v40

    goto/16 :goto_18

    :cond_2c
    move-object/from16 v28, v3

    move-object/from16 v35, v7

    move/from16 v40, v11

    move/from16 v22, v13

    move/from16 v24, v15

    const v2, 0x64323633

    if-ne v1, v2, :cond_2e

    if-nez v8, :cond_2d

    const/4 v1, 0x1

    :goto_20
    const/4 v2, 0x0

    goto :goto_21

    :cond_2d
    const/4 v1, 0x0

    goto :goto_20

    .line 130
    :goto_21
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    move-object/from16 v1, v19

    move/from16 v13, v22

    move/from16 v15, v24

    move-object/from16 v8, v31

    :goto_22
    move/from16 v22, v32

    move/from16 v24, v34

    move-object/from16 v16, v35

    :goto_23
    move/from16 v19, v40

    goto/16 :goto_19

    :cond_2e
    const/4 v2, 0x0

    const v3, 0x65736473

    if-ne v1, v3, :cond_31

    if-nez v8, :cond_2f

    const/4 v1, 0x1

    goto :goto_24

    :cond_2f
    const/4 v1, 0x0

    .line 131
    :goto_24
    invoke-static {v1, v2}, LN1/u;->a(ZLjava/lang/String;)V

    .line 132
    invoke-static {v0, v12}, Lh2/b;->m(Lt1/G;I)Lh2/b$c;

    move-result-object v27

    .line 133
    invoke-static/range {v27 .. v27}, Lh2/b$c;->a(Lh2/b$c;)Ljava/lang/String;

    move-result-object v1

    .line 134
    invoke-static/range {v27 .. v27}, Lh2/b$c;->b(Lh2/b$c;)[B

    move-result-object v3

    if-eqz v3, :cond_30

    .line 135
    invoke-static {v3}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v16

    goto :goto_25

    :cond_30
    move-object/from16 v16, v35

    :goto_25
    move-object v8, v1

    move-object/from16 v1, v19

    move/from16 v13, v22

    move/from16 v15, v24

    move/from16 v22, v32

    move/from16 v24, v34

    goto :goto_23

    :cond_31
    const v3, 0x62747274

    if-ne v1, v3, :cond_33

    .line 136
    invoke-static {v0, v12}, Lh2/b;->j(Lt1/G;I)Lh2/b$a;

    move-result-object v26

    :cond_32
    :goto_26
    move-object/from16 v1, v19

    move/from16 v13, v22

    move/from16 v15, v24

    goto :goto_22

    :cond_33
    const v3, 0x70617370

    if-ne v1, v3, :cond_34

    .line 137
    invoke-static {v0, v12}, Lh2/b;->w(Lt1/G;I)F

    move-result v1

    move v14, v1

    move-object/from16 v1, v19

    move/from16 v13, v22

    move/from16 v15, v24

    move/from16 v22, v32

    move/from16 v24, v34

    move-object/from16 v16, v35

    move/from16 v19, v40

    const/4 v7, 0x0

    const/4 v12, -0x1

    const/16 v29, 0x1

    goto/16 :goto_2c

    :cond_34
    const v3, 0x73763364

    if-ne v1, v3, :cond_35

    .line 138
    invoke-static {v0, v12, v9}, Lh2/b;->x(Lt1/G;II)[B

    move-result-object v18

    goto :goto_26

    :cond_35
    const v3, 0x73743364

    if-ne v1, v3, :cond_3a

    .line 139
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v1

    const/4 v3, 0x3

    .line 140
    invoke-virtual {v0, v3}, Lt1/G;->X(I)V

    if-nez v1, :cond_32

    .line 141
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v1

    if-eqz v1, :cond_39

    const/4 v11, 0x1

    if-eq v1, v11, :cond_38

    const/4 v12, 0x2

    if-eq v1, v12, :cond_37

    if-eq v1, v3, :cond_36

    goto :goto_26

    :cond_36
    const/16 v40, 0x3

    goto :goto_26

    :cond_37
    const/16 v40, 0x2

    goto :goto_26

    :cond_38
    const/16 v40, 0x1

    goto :goto_26

    :cond_39
    const/16 v40, 0x0

    goto :goto_26

    :cond_3a
    const/4 v11, 0x1

    const v3, 0x61707643

    if-ne v1, v3, :cond_3b

    add-int/lit8 v1, v9, -0xc

    .line 142
    new-array v3, v1, [B

    add-int/lit8 v12, v12, 0xc

    .line 143
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    const/4 v7, 0x0

    .line 144
    invoke-virtual {v0, v3, v7, v1}, Lt1/G;->l([BII)V

    .line 145
    invoke-static {v3}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v16

    .line 146
    new-instance v1, Lt1/G;

    invoke-direct {v1, v3}, Lt1/G;-><init>([B)V

    invoke-static {v1}, Lh2/b;->g(Lt1/G;)Landroidx/media3/common/i;

    move-result-object v1

    .line 147
    iget v3, v1, Landroidx/media3/common/i;->e:I

    .line 148
    iget v4, v1, Landroidx/media3/common/i;->f:I

    .line 149
    iget v8, v1, Landroidx/media3/common/i;->a:I

    .line 150
    iget v10, v1, Landroidx/media3/common/i;->b:I

    .line 151
    iget v1, v1, Landroidx/media3/common/i;->c:I

    .line 152
    const-string v11, "video/apv"

    move/from16 v24, v1

    move v15, v3

    move v13, v4

    move/from16 v22, v8

    move/from16 v33, v10

    move-object v8, v11

    move-object/from16 v1, v19

    move/from16 v19, v40

    goto/16 :goto_7

    :cond_3b
    const/4 v7, 0x0

    const v3, 0x636f6c72

    if-ne v1, v3, :cond_41

    move/from16 v1, v32

    const/4 v12, -0x1

    move/from16 v3, v34

    if-ne v1, v12, :cond_40

    if-ne v3, v12, :cond_40

    .line 153
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v4

    const v10, 0x6e636c78

    if-eq v4, v10, :cond_3d

    const v10, 0x6e636c63

    if-ne v4, v10, :cond_3c

    goto :goto_27

    .line 154
    :cond_3c
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    const-string v11, "Unsupported color type: "

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v4}, Landroidx/media3/container/d;->a(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v10, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v10, "BoxParsers"

    invoke-static {v10, v4}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2a

    .line 155
    :cond_3d
    :goto_27
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v1

    .line 156
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v3

    const/4 v4, 0x2

    .line 157
    invoke-virtual {v0, v4}, Lt1/G;->X(I)V

    const/16 v10, 0x13

    if-ne v9, v10, :cond_3e

    .line 158
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v10

    and-int/lit16 v10, v10, 0x80

    if-eqz v10, :cond_3e

    const/4 v10, 0x1

    goto :goto_28

    :cond_3e
    const/4 v10, 0x0

    .line 159
    :goto_28
    invoke-static {v1}, Landroidx/media3/common/i;->j(I)I

    move-result v1

    if-eqz v10, :cond_3f

    goto :goto_29

    :cond_3f
    const/4 v11, 0x2

    .line 160
    :goto_29
    invoke-static {v3}, Landroidx/media3/common/i;->k(I)I

    move-result v3

    move/from16 v33, v11

    :cond_40
    :goto_2a
    move/from16 v13, v22

    move/from16 v15, v24

    move-object/from16 v16, v35

    move/from16 v22, v1

    move/from16 v24, v3

    move-object/from16 v1, v19

    move/from16 v19, v40

    goto :goto_2c

    :cond_41
    move/from16 v1, v32

    move/from16 v3, v34

    const/4 v12, -0x1

    goto :goto_2a

    .line 161
    :goto_2b
    invoke-static {v0}, LN1/o;->a(Lt1/G;)LN1/o;

    move-result-object v4

    if-eqz v4, :cond_40

    .line 162
    iget-object v4, v4, LN1/o;->c:Ljava/lang/String;

    .line 163
    const-string v8, "video/dolby-vision"

    move-object/from16 v17, v4

    goto :goto_2a

    :goto_2c
    add-int v3, v30, v9

    move/from16 v2, p3

    move-object/from16 v4, p8

    move v7, v3

    move/from16 v10, v23

    move-object/from16 v3, v28

    move-object/from16 v11, v31

    move/from16 v23, v33

    move-object/from16 v28, v1

    move/from16 v1, p2

    goto/16 :goto_2

    :goto_2d
    if-nez v8, :cond_42

    return-void

    .line 164
    :cond_42
    new-instance v0, Landroidx/media3/common/r$b;

    invoke-direct {v0}, Landroidx/media3/common/r$b;-><init>()V

    move/from16 v4, p4

    .line 165
    invoke-virtual {v0, v4}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 166
    invoke-virtual {v0, v8}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    move-object/from16 v9, v17

    .line 167
    invoke-virtual {v0, v9}, Landroidx/media3/common/r$b;->S(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 168
    invoke-virtual {v0, v5}, Landroidx/media3/common/r$b;->B0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 169
    invoke-virtual {v0, v6}, Landroidx/media3/common/r$b;->d0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 170
    invoke-virtual {v0, v14}, Landroidx/media3/common/r$b;->q0(F)Landroidx/media3/common/r$b;

    move-result-object v0

    move/from16 v4, p6

    .line 171
    invoke-virtual {v0, v4}, Landroidx/media3/common/r$b;->t0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    move-object/from16 v9, v18

    .line 172
    invoke-virtual {v0, v9}, Landroidx/media3/common/r$b;->r0([B)Landroidx/media3/common/r$b;

    move-result-object v0

    move/from16 v11, v40

    .line 173
    invoke-virtual {v0, v11}, Landroidx/media3/common/r$b;->x0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    move-object/from16 v7, v35

    .line 174
    invoke-virtual {v0, v7}, Landroidx/media3/common/r$b;->g0(Ljava/util/List;)Landroidx/media3/common/r$b;

    move-result-object v0

    move/from16 v12, v20

    .line 175
    invoke-virtual {v0, v12}, Landroidx/media3/common/r$b;->l0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    move/from16 v12, v21

    .line 176
    invoke-virtual {v0, v12}, Landroidx/media3/common/r$b;->m0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    move-object/from16 v4, v28

    .line 177
    invoke-virtual {v0, v4}, Landroidx/media3/common/r$b;->Y(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r$b;

    move-result-object v0

    move-object/from16 v4, p5

    .line 178
    invoke-virtual {v0, v4}, Landroidx/media3/common/r$b;->j0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    new-instance v4, Landroidx/media3/common/i$b;

    invoke-direct {v4}, Landroidx/media3/common/i$b;-><init>()V

    .line 179
    invoke-virtual {v4, v1}, Landroidx/media3/common/i$b;->d(I)Landroidx/media3/common/i$b;

    move-result-object v1

    move/from16 v12, v33

    .line 180
    invoke-virtual {v1, v12}, Landroidx/media3/common/i$b;->c(I)Landroidx/media3/common/i$b;

    move-result-object v1

    .line 181
    invoke-virtual {v1, v3}, Landroidx/media3/common/i$b;->e(I)Landroidx/media3/common/i$b;

    move-result-object v1

    if-eqz v25, :cond_43

    .line 182
    invoke-virtual/range {v25 .. v25}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v9

    goto :goto_2e

    :cond_43
    move-object v9, v2

    :goto_2e
    invoke-virtual {v1, v9}, Landroidx/media3/common/i$b;->f([B)Landroidx/media3/common/i$b;

    move-result-object v1

    move/from16 v15, v24

    .line 183
    invoke-virtual {v1, v15}, Landroidx/media3/common/i$b;->g(I)Landroidx/media3/common/i$b;

    move-result-object v1

    move/from16 v13, v22

    .line 184
    invoke-virtual {v1, v13}, Landroidx/media3/common/i$b;->b(I)Landroidx/media3/common/i$b;

    move-result-object v1

    .line 185
    invoke-virtual {v1}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    move-result-object v1

    .line 186
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->T(Landroidx/media3/common/i;)Landroidx/media3/common/r$b;

    move-result-object v0

    if-eqz v26, :cond_44

    .line 187
    invoke-static/range {v26 .. v26}, Lh2/b$a;->b(Lh2/b$a;)J

    move-result-wide v1

    invoke-static {v1, v2}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    move-result-object v1

    .line 188
    invoke-static/range {v26 .. v26}, Lh2/b$a;->a(Lh2/b$a;)J

    move-result-wide v2

    invoke-static {v2, v3}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->p0(I)Landroidx/media3/common/r$b;

    goto :goto_2f

    :cond_44
    if-eqz v27, :cond_45

    .line 189
    invoke-static/range {v27 .. v27}, Lh2/b$c;->d(Lh2/b$c;)J

    move-result-wide v1

    invoke-static {v1, v2}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    move-result-object v1

    .line 190
    invoke-static/range {v27 .. v27}, Lh2/b$c;->c(Lh2/b$c;)J

    move-result-wide v2

    invoke-static {v2, v3}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->p0(I)Landroidx/media3/common/r$b;

    .line 191
    :cond_45
    :goto_2f
    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v0

    move-object/from16 v4, p8

    iput-object v0, v4, Lh2/b$h;->b:Landroidx/media3/common/r;

    return-void
.end method

.method public static L(Lt1/G;)Landroidx/media3/common/x;
    .locals 5

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    invoke-virtual {p0}, Lt1/G;->D()S

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    const/4 v3, 0x2

    .line 8
    invoke-virtual {p0, v3}, Lt1/G;->X(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, v2}, Lt1/G;->E(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    const/16 v2, 0x2b

    .line 16
    .line 17
    invoke-virtual {p0, v2}, Ljava/lang/String;->lastIndexOf(I)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    const/16 v3, 0x2d

    .line 22
    .line 23
    invoke-virtual {p0, v3}, Ljava/lang/String;->lastIndexOf(I)I

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    invoke-static {v2, v3}, Ljava/lang/Math;->max(II)I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    :try_start_0
    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    invoke-static {v3}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    sub-int/2addr v4, v0

    .line 44
    invoke-virtual {p0, v2, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-static {p0}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    .line 49
    .line 50
    .line 51
    move-result p0

    .line 52
    new-instance v2, Landroidx/media3/common/x;

    .line 53
    .line 54
    new-instance v4, Landroidx/media3/container/e;

    .line 55
    .line 56
    invoke-direct {v4, v3, p0}, Landroidx/media3/container/e;-><init>(FF)V

    .line 57
    .line 58
    .line 59
    new-array p0, v0, [Landroidx/media3/common/x$a;

    .line 60
    .line 61
    aput-object v4, p0, v1

    .line 62
    .line 63
    invoke-direct {v2, p0}, Landroidx/media3/common/x;-><init>([Landroidx/media3/common/x$a;)V
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    .line 64
    .line 65
    .line 66
    return-object v2

    .line 67
    :catch_0
    const/4 p0, 0x0

    .line 68
    return-object p0
.end method

.method public static a()Ljava/nio/ByteBuffer;
    .locals 2

    .line 1
    const/16 v0, 0x19

    .line 2
    .line 3
    invoke-static {v0}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Ljava/nio/ByteOrder;->LITTLE_ENDIAN:Ljava/nio/ByteOrder;

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Ljava/nio/ByteBuffer;->order(Ljava/nio/ByteOrder;)Ljava/nio/ByteBuffer;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public static b([JJJJ)Z
    .locals 7

    .line 1
    array-length v0, p0

    .line 2
    const/4 v1, 0x1

    .line 3
    sub-int/2addr v0, v1

    .line 4
    const/4 v2, 0x4

    .line 5
    const/4 v3, 0x0

    .line 6
    invoke-static {v2, v3, v0}, Lt1/a0;->p(III)I

    .line 7
    .line 8
    .line 9
    move-result v4

    .line 10
    array-length v5, p0

    .line 11
    sub-int/2addr v5, v2

    .line 12
    invoke-static {v5, v3, v0}, Lt1/a0;->p(III)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    aget-wide v5, p0, v3

    .line 17
    .line 18
    cmp-long v2, v5, p3

    .line 19
    .line 20
    if-gtz v2, :cond_0

    .line 21
    .line 22
    aget-wide v4, p0, v4

    .line 23
    .line 24
    cmp-long v2, p3, v4

    .line 25
    .line 26
    if-gez v2, :cond_0

    .line 27
    .line 28
    aget-wide p3, p0, v0

    .line 29
    .line 30
    cmp-long p0, p3, p5

    .line 31
    .line 32
    if-gez p0, :cond_0

    .line 33
    .line 34
    cmp-long p0, p5, p1

    .line 35
    .line 36
    if-gtz p0, :cond_0

    .line 37
    .line 38
    return v1

    .line 39
    :cond_0
    return v3
.end method

.method public static c(Lt1/G;III)I
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    if-lt v0, p2, :cond_0

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v3, 0x0

    .line 12
    :goto_0
    const/4 v4, 0x0

    .line 13
    invoke-static {v3, v4}, LN1/u;->a(ZLjava/lang/String;)V

    .line 14
    .line 15
    .line 16
    :goto_1
    sub-int v3, v0, p2

    .line 17
    .line 18
    if-ge v3, p3, :cond_3

    .line 19
    .line 20
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-lez v3, :cond_1

    .line 28
    .line 29
    const/4 v4, 0x1

    .line 30
    goto :goto_2

    .line 31
    :cond_1
    const/4 v4, 0x0

    .line 32
    :goto_2
    const-string v5, "childAtomSize must be positive"

    .line 33
    .line 34
    invoke-static {v4, v5}, LN1/u;->a(ZLjava/lang/String;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 38
    .line 39
    .line 40
    move-result v4

    .line 41
    if-ne v4, p1, :cond_2

    .line 42
    .line 43
    return v0

    .line 44
    :cond_2
    add-int/2addr v0, v3

    .line 45
    goto :goto_1

    .line 46
    :cond_3
    const/4 p0, -0x1

    .line 47
    return p0
.end method

.method public static d(I)Ljava/lang/String;
    .locals 5

    .line 1
    shr-int/lit8 v0, p0, 0xa

    .line 2
    .line 3
    and-int/lit8 v0, v0, 0x1f

    .line 4
    .line 5
    add-int/lit8 v0, v0, 0x60

    .line 6
    .line 7
    int-to-char v0, v0

    .line 8
    shr-int/lit8 v1, p0, 0x5

    .line 9
    .line 10
    and-int/lit8 v1, v1, 0x1f

    .line 11
    .line 12
    add-int/lit8 v1, v1, 0x60

    .line 13
    .line 14
    int-to-char v1, v1

    .line 15
    and-int/lit8 p0, p0, 0x1f

    .line 16
    .line 17
    add-int/lit8 p0, p0, 0x60

    .line 18
    .line 19
    int-to-char p0, p0

    .line 20
    const/4 v2, 0x3

    .line 21
    new-array v3, v2, [C

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    aput-char v0, v3, v4

    .line 25
    .line 26
    const/4 v0, 0x1

    .line 27
    aput-char v1, v3, v0

    .line 28
    .line 29
    const/4 v1, 0x2

    .line 30
    aput-char p0, v3, v1

    .line 31
    .line 32
    :goto_0
    if-ge v4, v2, :cond_2

    .line 33
    .line 34
    aget-char p0, v3, v4

    .line 35
    .line 36
    const/16 v1, 0x61

    .line 37
    .line 38
    if-lt p0, v1, :cond_1

    .line 39
    .line 40
    const/16 v1, 0x7a

    .line 41
    .line 42
    if-le p0, v1, :cond_0

    .line 43
    .line 44
    goto :goto_1

    .line 45
    :cond_0
    add-int/2addr v4, v0

    .line 46
    goto :goto_0

    .line 47
    :cond_1
    :goto_1
    const/4 p0, 0x0

    .line 48
    return-object p0

    .line 49
    :cond_2
    new-instance p0, Ljava/lang/String;

    .line 50
    .line 51
    invoke-direct {p0, v3}, Ljava/lang/String;-><init>([C)V

    .line 52
    .line 53
    .line 54
    return-object p0
.end method

.method public static e(I)I
    .locals 1

    .line 1
    const v0, 0x736f756e

    .line 2
    .line 3
    .line 4
    if-ne p0, v0, :cond_0

    .line 5
    .line 6
    const/4 p0, 0x1

    .line 7
    return p0

    .line 8
    :cond_0
    const v0, 0x76696465

    .line 9
    .line 10
    .line 11
    if-ne p0, v0, :cond_1

    .line 12
    .line 13
    const/4 p0, 0x2

    .line 14
    return p0

    .line 15
    :cond_1
    const v0, 0x74657874

    .line 16
    .line 17
    .line 18
    if-eq p0, v0, :cond_4

    .line 19
    .line 20
    const v0, 0x7362746c

    .line 21
    .line 22
    .line 23
    if-eq p0, v0, :cond_4

    .line 24
    .line 25
    const v0, 0x73756274

    .line 26
    .line 27
    .line 28
    if-eq p0, v0, :cond_4

    .line 29
    .line 30
    const v0, 0x636c6370

    .line 31
    .line 32
    .line 33
    if-ne p0, v0, :cond_2

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_2
    const v0, 0x6d657461

    .line 37
    .line 38
    .line 39
    if-ne p0, v0, :cond_3

    .line 40
    .line 41
    const/4 p0, 0x5

    .line 42
    return p0

    .line 43
    :cond_3
    const/4 p0, -0x1

    .line 44
    return p0

    .line 45
    :cond_4
    :goto_0
    const/4 p0, 0x3

    .line 46
    return p0
.end method

.method public static f(Lt1/G;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x4

    .line 6
    invoke-virtual {p0, v1}, Lt1/G;->X(I)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const v2, 0x68646c72

    .line 14
    .line 15
    .line 16
    if-eq v1, v2, :cond_0

    .line 17
    .line 18
    add-int/lit8 v0, v0, 0x4

    .line 19
    .line 20
    :cond_0
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public static g(Lt1/G;)Landroidx/media3/common/i;
    .locals 11

    .line 1
    new-instance v0, Landroidx/media3/common/i$b;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/media3/common/i$b;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lt1/F;

    .line 7
    .line 8
    invoke-virtual {p0}, Lt1/G;->e()[B

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    invoke-direct {v1, v2}, Lt1/F;-><init>([B)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 16
    .line 17
    .line 18
    move-result p0

    .line 19
    const/16 v2, 0x8

    .line 20
    .line 21
    mul-int/lit8 p0, p0, 0x8

    .line 22
    .line 23
    invoke-virtual {v1, p0}, Lt1/F;->p(I)V

    .line 24
    .line 25
    .line 26
    const/4 p0, 0x1

    .line 27
    invoke-virtual {v1, p0}, Lt1/F;->s(I)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 31
    .line 32
    .line 33
    move-result v3

    .line 34
    const/4 v4, 0x0

    .line 35
    const/4 v5, 0x0

    .line 36
    :goto_0
    if-ge v5, v3, :cond_3

    .line 37
    .line 38
    invoke-virtual {v1, p0}, Lt1/F;->s(I)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 42
    .line 43
    .line 44
    move-result v6

    .line 45
    const/4 v7, 0x0

    .line 46
    :goto_1
    if-ge v7, v6, :cond_2

    .line 47
    .line 48
    const/4 v8, 0x6

    .line 49
    invoke-virtual {v1, v8}, Lt1/F;->r(I)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 53
    .line 54
    .line 55
    move-result v8

    .line 56
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 57
    .line 58
    .line 59
    const/16 v9, 0xb

    .line 60
    .line 61
    invoke-virtual {v1, v9}, Lt1/F;->s(I)V

    .line 62
    .line 63
    .line 64
    const/4 v9, 0x4

    .line 65
    invoke-virtual {v1, v9}, Lt1/F;->r(I)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {v1, v9}, Lt1/F;->h(I)I

    .line 69
    .line 70
    .line 71
    move-result v9

    .line 72
    add-int/2addr v9, v2

    .line 73
    invoke-virtual {v0, v9}, Landroidx/media3/common/i$b;->g(I)Landroidx/media3/common/i$b;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v0, v9}, Landroidx/media3/common/i$b;->b(I)Landroidx/media3/common/i$b;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v1, p0}, Lt1/F;->s(I)V

    .line 80
    .line 81
    .line 82
    if-eqz v8, :cond_1

    .line 83
    .line 84
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 85
    .line 86
    .line 87
    move-result v8

    .line 88
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 89
    .line 90
    .line 91
    move-result v9

    .line 92
    invoke-virtual {v1, p0}, Lt1/F;->s(I)V

    .line 93
    .line 94
    .line 95
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 96
    .line 97
    .line 98
    move-result v10

    .line 99
    invoke-static {v8}, Landroidx/media3/common/i;->j(I)I

    .line 100
    .line 101
    .line 102
    move-result v8

    .line 103
    invoke-virtual {v0, v8}, Landroidx/media3/common/i$b;->d(I)Landroidx/media3/common/i$b;

    .line 104
    .line 105
    .line 106
    move-result-object v8

    .line 107
    if-eqz v10, :cond_0

    .line 108
    .line 109
    const/4 v10, 0x1

    .line 110
    goto :goto_2

    .line 111
    :cond_0
    const/4 v10, 0x2

    .line 112
    :goto_2
    invoke-virtual {v8, v10}, Landroidx/media3/common/i$b;->c(I)Landroidx/media3/common/i$b;

    .line 113
    .line 114
    .line 115
    move-result-object v8

    .line 116
    invoke-static {v9}, Landroidx/media3/common/i;->k(I)I

    .line 117
    .line 118
    .line 119
    move-result v9

    .line 120
    invoke-virtual {v8, v9}, Landroidx/media3/common/i$b;->e(I)Landroidx/media3/common/i$b;

    .line 121
    .line 122
    .line 123
    :cond_1
    add-int/lit8 v7, v7, 0x1

    .line 124
    .line 125
    goto :goto_1

    .line 126
    :cond_2
    add-int/lit8 v5, v5, 0x1

    .line 127
    .line 128
    goto :goto_0

    .line 129
    :cond_3
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    return-object p0
.end method

.method public static h(Lt1/G;IIIILjava/lang/String;ZLandroidx/media3/common/DrmInitData;Lh2/b$h;I)V
    .locals 26
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    move-object/from16 v0, p0

    move/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move/from16 v4, p4

    move-object/from16 v5, p5

    move-object/from16 v6, p7

    move-object/from16 v7, p8

    const/4 v8, 0x1

    const/16 v9, 0x10

    add-int/lit8 v10, v2, 0x10

    .line 1
    invoke-virtual {v0, v10}, Lt1/G;->W(I)V

    const/4 v10, 0x6

    const/16 v11, 0x8

    if-eqz p6, :cond_0

    .line 2
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v13

    .line 3
    invoke-virtual {v0, v10}, Lt1/G;->X(I)V

    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {v0, v11}, Lt1/G;->X(I)V

    const/4 v13, 0x0

    :goto_0
    const/4 v14, 0x4

    const/16 v17, 0x3

    const/16 v18, 0x0

    const/4 v12, 0x2

    if-eqz v13, :cond_1

    if-ne v13, v8, :cond_2

    :cond_1
    const/16 v21, 0x2

    const/16 v22, 0x4

    goto/16 :goto_5

    :cond_2
    if-ne v13, v12, :cond_49

    .line 5
    invoke-virtual {v0, v9}, Lt1/G;->X(I)V

    .line 6
    invoke-virtual {v0}, Lt1/G;->o()D

    move-result-wide v19

    const/16 v21, 0x2

    invoke-static/range {v19 .. v20}, Ljava/lang/Math;->round(D)J

    move-result-wide v12

    long-to-int v10, v12

    .line 7
    invoke-virtual {v0}, Lt1/G;->L()I

    move-result v12

    .line 8
    invoke-virtual {v0, v14}, Lt1/G;->X(I)V

    .line 9
    invoke-virtual {v0}, Lt1/G;->L()I

    move-result v13

    .line 10
    invoke-virtual {v0}, Lt1/G;->L()I

    move-result v19

    and-int/lit8 v20, v19, 0x1

    if-eqz v20, :cond_3

    const/16 v20, 0x1

    goto :goto_1

    :cond_3
    const/16 v20, 0x0

    :goto_1
    and-int/lit8 v19, v19, 0x2

    if-eqz v19, :cond_4

    const/16 v19, 0x1

    :goto_2
    const/16 v22, 0x4

    goto :goto_3

    :cond_4
    const/16 v19, 0x0

    goto :goto_2

    :goto_3
    const/16 v14, 0x20

    if-nez v20, :cond_b

    if-ne v13, v11, :cond_5

    const/4 v9, 0x3

    goto :goto_4

    :cond_5
    if-ne v13, v9, :cond_7

    if-eqz v19, :cond_6

    const/high16 v9, 0x10000000

    goto :goto_4

    :cond_6
    const/4 v9, 0x2

    goto :goto_4

    :cond_7
    const/16 v9, 0x18

    if-ne v13, v9, :cond_9

    if-eqz v19, :cond_8

    const/high16 v9, 0x50000000

    goto :goto_4

    :cond_8
    const/16 v9, 0x15

    goto :goto_4

    :cond_9
    if-ne v13, v14, :cond_c

    if-eqz v19, :cond_a

    const/high16 v9, 0x60000000

    goto :goto_4

    :cond_a
    const/16 v9, 0x16

    goto :goto_4

    :cond_b
    if-ne v13, v14, :cond_c

    const/4 v9, 0x4

    goto :goto_4

    :cond_c
    const/4 v9, -0x1

    .line 11
    :goto_4
    invoke-virtual {v0, v11}, Lt1/G;->X(I)V

    const/4 v14, 0x0

    goto :goto_6

    .line 12
    :goto_5
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v12

    .line 13
    invoke-virtual {v0, v10}, Lt1/G;->X(I)V

    .line 14
    invoke-virtual {v0}, Lt1/G;->I()I

    move-result v10

    .line 15
    invoke-virtual {v0}, Lt1/G;->f()I

    move-result v14

    add-int/lit8 v14, v14, -0x4

    invoke-virtual {v0, v14}, Lt1/G;->W(I)V

    .line 16
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v14

    if-ne v13, v8, :cond_d

    .line 17
    invoke-virtual {v0, v9}, Lt1/G;->X(I)V

    :cond_d
    const/4 v9, -0x1

    :goto_6
    const v13, 0x73617762

    const/16 v19, 0x8

    const v11, 0x73616d72

    const v8, 0x69616d66

    if-ne v1, v8, :cond_e

    const/4 v10, -0x1

    const/4 v12, -0x1

    goto :goto_8

    :cond_e
    if-ne v1, v11, :cond_f

    const/16 v10, 0x1f40

    :goto_7
    const/4 v12, 0x1

    goto :goto_8

    :cond_f
    if-ne v1, v13, :cond_10

    const/16 v10, 0x3e80

    goto :goto_7

    .line 18
    :cond_10
    :goto_8
    invoke-virtual {v0}, Lt1/G;->f()I

    move-result v8

    const v15, 0x656e6361

    if-ne v1, v15, :cond_13

    .line 19
    invoke-static {v0, v2, v3}, Lh2/b;->y(Lt1/G;II)Landroid/util/Pair;

    move-result-object v15

    if-eqz v15, :cond_12

    .line 20
    iget-object v1, v15, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    if-nez v6, :cond_11

    const/4 v6, 0x0

    goto :goto_9

    .line 21
    :cond_11
    iget-object v13, v15, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v13, Lh2/u;

    iget-object v13, v13, Lh2/u;->b:Ljava/lang/String;

    invoke-virtual {v6, v13}, Landroidx/media3/common/DrmInitData;->c(Ljava/lang/String;)Landroidx/media3/common/DrmInitData;

    move-result-object v6

    .line 22
    :goto_9
    iget-object v13, v7, Lh2/b$h;->a:[Lh2/u;

    iget-object v15, v15, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v15, Lh2/u;

    aput-object v15, v13, p9

    .line 23
    :cond_12
    invoke-virtual {v0, v8}, Lt1/G;->W(I)V

    :cond_13
    const v13, 0x61632d33

    .line 24
    const-string v15, "audio/mhm1"

    if-ne v1, v13, :cond_14

    .line 25
    const-string v1, "audio/ac3"

    goto/16 :goto_d

    :cond_14
    const v13, 0x65632d33

    if-ne v1, v13, :cond_15

    .line 26
    const-string v1, "audio/eac3"

    goto/16 :goto_d

    :cond_15
    const v13, 0x61632d34

    if-ne v1, v13, :cond_16

    .line 27
    const-string v1, "audio/ac4"

    goto/16 :goto_d

    :cond_16
    const v13, 0x64747363

    if-ne v1, v13, :cond_17

    .line 28
    const-string v1, "audio/vnd.dts"

    goto/16 :goto_d

    :cond_17
    const v13, 0x64747368

    if-eq v1, v13, :cond_2c

    const v13, 0x6474736c

    if-ne v1, v13, :cond_18

    goto/16 :goto_c

    :cond_18
    const v13, 0x64747365

    if-ne v1, v13, :cond_19

    .line 29
    const-string v1, "audio/vnd.dts.hd;profile=lbr"

    goto/16 :goto_d

    :cond_19
    const v13, 0x64747378

    if-ne v1, v13, :cond_1a

    .line 30
    const-string v1, "audio/vnd.dts.uhd;profile=p2"

    goto/16 :goto_d

    :cond_1a
    if-ne v1, v11, :cond_1b

    .line 31
    const-string v1, "audio/3gpp"

    goto/16 :goto_d

    :cond_1b
    const v11, 0x73617762

    if-ne v1, v11, :cond_1c

    .line 32
    const-string v1, "audio/amr-wb"

    goto/16 :goto_d

    :cond_1c
    const v11, 0x736f7774

    .line 33
    const-string v13, "audio/raw"

    if-ne v1, v11, :cond_1d

    :goto_a
    move-object v1, v13

    const/4 v9, 0x2

    goto/16 :goto_d

    :cond_1d
    const v11, 0x74776f73

    if-ne v1, v11, :cond_1e

    move-object v1, v13

    const/high16 v9, 0x10000000

    goto/16 :goto_d

    :cond_1e
    const v11, 0x6c70636d

    if-ne v1, v11, :cond_20

    const/4 v11, -0x1

    if-ne v9, v11, :cond_1f

    goto :goto_a

    :cond_1f
    move-object v1, v13

    goto/16 :goto_d

    :cond_20
    const v11, 0x2e6d7032

    if-eq v1, v11, :cond_2b

    const v11, 0x2e6d7033

    if-ne v1, v11, :cond_21

    goto :goto_b

    :cond_21
    const v11, 0x6d686131

    if-ne v1, v11, :cond_22

    .line 34
    const-string v1, "audio/mha1"

    goto :goto_d

    :cond_22
    const v11, 0x6d686d31

    if-ne v1, v11, :cond_23

    move-object v1, v15

    goto :goto_d

    :cond_23
    const v11, 0x616c6163

    if-ne v1, v11, :cond_24

    .line 35
    const-string v1, "audio/alac"

    goto :goto_d

    :cond_24
    const v11, 0x616c6177

    if-ne v1, v11, :cond_25

    .line 36
    const-string v1, "audio/g711-alaw"

    goto :goto_d

    :cond_25
    const v11, 0x756c6177

    if-ne v1, v11, :cond_26

    .line 37
    const-string v1, "audio/g711-mlaw"

    goto :goto_d

    :cond_26
    const v11, 0x4f707573

    if-ne v1, v11, :cond_27

    .line 38
    const-string v1, "audio/opus"

    goto :goto_d

    :cond_27
    const v11, 0x664c6143

    if-ne v1, v11, :cond_28

    .line 39
    const-string v1, "audio/flac"

    goto :goto_d

    :cond_28
    const v11, 0x6d6c7061

    if-ne v1, v11, :cond_29

    .line 40
    const-string v1, "audio/true-hd"

    goto :goto_d

    :cond_29
    const v11, 0x69616d66

    if-ne v1, v11, :cond_2a

    .line 41
    const-string v1, "audio/iamf"

    goto :goto_d

    :cond_2a
    const/4 v1, 0x0

    goto :goto_d

    .line 42
    :cond_2b
    :goto_b
    const-string v1, "audio/mpeg"

    goto :goto_d

    .line 43
    :cond_2c
    :goto_c
    const-string v1, "audio/vnd.dts.hd"

    :goto_d
    const/4 v11, 0x0

    const/4 v13, 0x0

    const/16 v16, 0x0

    const/16 v23, 0x0

    :goto_e
    sub-int v2, v8, p2

    if-ge v2, v3, :cond_46

    .line 44
    invoke-virtual {v0, v8}, Lt1/G;->W(I)V

    .line 45
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v2

    if-lez v2, :cond_2d

    const/4 v3, 0x1

    :goto_f
    move/from16 v24, v9

    goto :goto_10

    :cond_2d
    const/4 v3, 0x0

    goto :goto_f

    .line 46
    :goto_10
    const-string v9, "childAtomSize must be positive"

    invoke-static {v3, v9}, LN1/u;->a(ZLjava/lang/String;)V

    .line 47
    invoke-virtual {v0}, Lt1/G;->q()I

    move-result v3

    const v9, 0x6d686143

    if-ne v3, v9, :cond_31

    add-int/lit8 v11, v8, 0x8

    .line 48
    invoke-virtual {v0, v11}, Lt1/G;->W(I)V

    const/4 v3, 0x1

    .line 49
    invoke-virtual {v0, v3}, Lt1/G;->X(I)V

    .line 50
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v9

    .line 51
    invoke-virtual {v0, v3}, Lt1/G;->X(I)V

    .line 52
    invoke-static {v1, v15}, Lj$/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_2e

    .line 53
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    new-array v11, v3, [Ljava/lang/Object;

    aput-object v9, v11, v18

    const-string v9, "mhm1.%02X"

    invoke-static {v9, v11}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v9

    move-object v11, v9

    goto :goto_11

    .line 54
    :cond_2e
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    new-array v11, v3, [Ljava/lang/Object;

    aput-object v9, v11, v18

    const-string v3, "mha1.%02X"

    invoke-static {v3, v11}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    move-object v11, v3

    .line 55
    :goto_11
    invoke-virtual {v0}, Lt1/G;->P()I

    move-result v3

    .line 56
    new-array v9, v3, [B

    move-object/from16 p7, v11

    const/4 v11, 0x0

    .line 57
    invoke-virtual {v0, v9, v11, v3}, Lt1/G;->l([BII)V

    if-nez v13, :cond_2f

    .line 58
    invoke-static {v9}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v3

    :goto_12
    move-object v13, v3

    goto :goto_13

    .line 59
    :cond_2f
    invoke-interface {v13, v11}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [B

    invoke-static {v9, v3}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v3

    goto :goto_12

    :goto_13
    move-object/from16 v11, p7

    :cond_30
    move-object/from16 p7, v15

    :goto_14
    const/4 v9, -0x1

    :goto_15
    const/4 v15, 0x4

    const/16 v18, 0x0

    const/16 v20, 0x1

    :goto_16
    const v22, 0x616c6163

    goto/16 :goto_1e

    :cond_31
    const v9, 0x6d686150

    if-ne v3, v9, :cond_33

    add-int/lit8 v3, v8, 0x8

    .line 60
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 61
    invoke-virtual {v0}, Lt1/G;->H()I

    move-result v3

    if-lez v3, :cond_30

    .line 62
    new-array v9, v3, [B

    move-object/from16 p7, v15

    const/4 v15, 0x0

    .line 63
    invoke-virtual {v0, v9, v15, v3}, Lt1/G;->l([BII)V

    if-nez v13, :cond_32

    .line 64
    invoke-static {v9}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    goto :goto_14

    .line 65
    :cond_32
    invoke-interface {v13, v15}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [B

    invoke-static {v3, v9}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    goto :goto_14

    :cond_33
    move-object/from16 p7, v15

    const v9, 0x65736473

    if-eq v3, v9, :cond_34

    if-eqz p6, :cond_35

    const v15, 0x77617665

    if-ne v3, v15, :cond_35

    :cond_34
    const/4 v15, 0x4

    const/16 v18, 0x0

    const/16 v20, 0x1

    const v22, 0x616c6163

    goto/16 :goto_1b

    :cond_35
    const v9, 0x62747274

    if-ne v3, v9, :cond_36

    .line 66
    invoke-static {v0, v8}, Lh2/b;->j(Lt1/G;I)Lh2/b$a;

    move-result-object v23

    goto :goto_14

    :cond_36
    const v9, 0x64616333

    if-ne v3, v9, :cond_37

    add-int/lit8 v3, v8, 0x8

    .line 67
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 68
    invoke-static {v4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3, v5, v6}, LN1/b;->d(Lt1/G;Ljava/lang/String;Ljava/lang/String;Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r;

    move-result-object v3

    iput-object v3, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    :goto_17
    const/4 v15, 0x4

    const/16 v18, 0x0

    const/16 v20, 0x1

    goto/16 :goto_1a

    :cond_37
    const v9, 0x64656333

    if-ne v3, v9, :cond_38

    add-int/lit8 v3, v8, 0x8

    .line 69
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 70
    invoke-static {v4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3, v5, v6}, LN1/b;->h(Lt1/G;Ljava/lang/String;Ljava/lang/String;Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r;

    move-result-object v3

    iput-object v3, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    goto :goto_17

    :cond_38
    const v9, 0x64616334

    if-ne v3, v9, :cond_39

    add-int/lit8 v3, v8, 0x8

    .line 71
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 72
    invoke-static {v4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3, v5, v6}, LN1/c;->d(Lt1/G;Ljava/lang/String;Ljava/lang/String;Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r;

    move-result-object v3

    iput-object v3, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    goto :goto_17

    :cond_39
    const v9, 0x646d6c70

    if-ne v3, v9, :cond_3b

    if-lez v14, :cond_3a

    move v10, v14

    const/4 v9, -0x1

    const/4 v12, 0x2

    goto/16 :goto_15

    .line 73
    :cond_3a
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Invalid sample rate for Dolby TrueHD MLP stream: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v15, 0x0

    invoke-static {v0, v15}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_3b
    const/4 v15, 0x0

    const v9, 0x64647473

    if-eq v3, v9, :cond_3c

    const v9, 0x75647473

    if-ne v3, v9, :cond_3d

    :cond_3c
    const/4 v15, 0x4

    const/16 v18, 0x0

    const/16 v20, 0x1

    goto/16 :goto_19

    :cond_3d
    const v9, 0x644f7073

    if-ne v3, v9, :cond_3e

    add-int/lit8 v3, v2, -0x8

    .line 74
    sget-object v9, Lh2/b;->a:[B

    array-length v13, v9

    add-int/2addr v13, v3

    invoke-static {v9, v13}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v13

    add-int/lit8 v15, v8, 0x8

    .line 75
    invoke-virtual {v0, v15}, Lt1/G;->W(I)V

    .line 76
    array-length v9, v9

    invoke-virtual {v0, v13, v9, v3}, Lt1/G;->l([BII)V

    .line 77
    invoke-static {v13}, LN1/K;->a([B)Ljava/util/List;

    move-result-object v13

    goto/16 :goto_14

    :cond_3e
    const v9, 0x64664c61

    if-ne v3, v9, :cond_3f

    add-int/lit8 v3, v2, -0xc

    add-int/lit8 v9, v2, -0x8

    .line 78
    new-array v9, v9, [B

    const/16 v13, 0x66

    const/16 v18, 0x0

    .line 79
    aput-byte v13, v9, v18

    const/16 v13, 0x4c

    const/16 v20, 0x1

    .line 80
    aput-byte v13, v9, v20

    const/16 v13, 0x61

    .line 81
    aput-byte v13, v9, v21

    const/16 v13, 0x43

    .line 82
    aput-byte v13, v9, v17

    add-int/lit8 v13, v8, 0xc

    .line 83
    invoke-virtual {v0, v13}, Lt1/G;->W(I)V

    const/4 v15, 0x4

    .line 84
    invoke-virtual {v0, v9, v15, v3}, Lt1/G;->l([BII)V

    .line 85
    invoke-static {v9}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    :goto_18
    const/4 v9, -0x1

    const/16 v18, 0x0

    goto/16 :goto_16

    :cond_3f
    const v9, 0x616c6163

    const/4 v15, 0x4

    const/16 v20, 0x1

    if-ne v3, v9, :cond_40

    add-int/lit8 v3, v2, -0xc

    .line 86
    new-array v10, v3, [B

    add-int/lit8 v12, v8, 0xc

    .line 87
    invoke-virtual {v0, v12}, Lt1/G;->W(I)V

    const/4 v12, 0x0

    .line 88
    invoke-virtual {v0, v10, v12, v3}, Lt1/G;->l([BII)V

    .line 89
    invoke-static {v10}, Lt1/i;->u([B)Landroid/util/Pair;

    move-result-object v3

    .line 90
    iget-object v12, v3, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v12, Ljava/lang/Integer;

    invoke-virtual {v12}, Ljava/lang/Integer;->intValue()I

    move-result v12

    .line 91
    iget-object v3, v3, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    .line 92
    invoke-static {v10}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    move v10, v12

    const/4 v9, -0x1

    const/16 v18, 0x0

    const v22, 0x616c6163

    move v12, v3

    goto/16 :goto_1e

    :cond_40
    const v9, 0x69616362

    if-ne v3, v9, :cond_41

    add-int/lit8 v3, v8, 0x9

    .line 93
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 94
    invoke-virtual {v0}, Lt1/G;->M()I

    move-result v3

    .line 95
    new-array v9, v3, [B

    const/4 v13, 0x0

    .line 96
    invoke-virtual {v0, v9, v13, v3}, Lt1/G;->l([BII)V

    .line 97
    invoke-static {v9}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v3

    move-object v13, v3

    goto :goto_18

    :cond_41
    const/16 v18, 0x0

    goto :goto_1a

    .line 98
    :goto_19
    new-instance v3, Landroidx/media3/common/r$b;

    invoke-direct {v3}, Landroidx/media3/common/r$b;-><init>()V

    .line 99
    invoke-virtual {v3, v4}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 100
    invoke-virtual {v3, v1}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 101
    invoke-virtual {v3, v12}, Landroidx/media3/common/r$b;->R(I)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 102
    invoke-virtual {v3, v10}, Landroidx/media3/common/r$b;->v0(I)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 103
    invoke-virtual {v3, v6}, Landroidx/media3/common/r$b;->Y(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 104
    invoke-virtual {v3, v5}, Landroidx/media3/common/r$b;->j0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v3

    .line 105
    invoke-virtual {v3}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v3

    iput-object v3, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    :goto_1a
    const/4 v9, -0x1

    goto/16 :goto_16

    :goto_1b
    if-ne v3, v9, :cond_42

    move v3, v8

    :goto_1c
    const/4 v9, -0x1

    goto :goto_1d

    .line 106
    :cond_42
    invoke-static {v0, v9, v8, v2}, Lh2/b;->c(Lt1/G;III)I

    move-result v3

    goto :goto_1c

    :goto_1d
    if-eq v3, v9, :cond_45

    .line 107
    invoke-static {v0, v3}, Lh2/b;->m(Lt1/G;I)Lh2/b$c;

    move-result-object v16

    .line 108
    invoke-static/range {v16 .. v16}, Lh2/b$c;->a(Lh2/b$c;)Ljava/lang/String;

    move-result-object v1

    .line 109
    invoke-static/range {v16 .. v16}, Lh2/b$c;->b(Lh2/b$c;)[B

    move-result-object v3

    if-eqz v3, :cond_45

    .line 110
    const-string v13, "audio/vorbis"

    invoke-virtual {v13, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v13

    if-eqz v13, :cond_43

    .line 111
    invoke-static {v3}, LN1/W;->e([B)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    goto :goto_1e

    .line 112
    :cond_43
    const-string v13, "audio/mp4a-latm"

    invoke-virtual {v13, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v13

    if-eqz v13, :cond_44

    .line 113
    invoke-static {v3}, LN1/a;->e([B)LN1/a$b;

    move-result-object v10

    .line 114
    iget v11, v10, LN1/a$b;->a:I

    .line 115
    iget v12, v10, LN1/a$b;->b:I

    .line 116
    iget-object v10, v10, LN1/a$b;->c:Ljava/lang/String;

    move/from16 v25, v11

    move-object v11, v10

    move/from16 v10, v25

    .line 117
    :cond_44
    invoke-static {v3}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v13

    :cond_45
    :goto_1e
    add-int/2addr v8, v2

    move/from16 v3, p3

    move-object/from16 v15, p7

    move/from16 v9, v24

    const/16 v22, 0x4

    goto/16 :goto_e

    :cond_46
    move/from16 v24, v9

    .line 118
    iget-object v0, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    if-nez v0, :cond_49

    if-eqz v1, :cond_49

    .line 119
    new-instance v0, Landroidx/media3/common/r$b;

    invoke-direct {v0}, Landroidx/media3/common/r$b;-><init>()V

    .line 120
    invoke-virtual {v0, v4}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 121
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 122
    invoke-virtual {v0, v11}, Landroidx/media3/common/r$b;->S(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 123
    invoke-virtual {v0, v12}, Landroidx/media3/common/r$b;->R(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 124
    invoke-virtual {v0, v10}, Landroidx/media3/common/r$b;->v0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    move/from16 v9, v24

    .line 125
    invoke-virtual {v0, v9}, Landroidx/media3/common/r$b;->o0(I)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 126
    invoke-virtual {v0, v13}, Landroidx/media3/common/r$b;->g0(Ljava/util/List;)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 127
    invoke-virtual {v0, v6}, Landroidx/media3/common/r$b;->Y(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r$b;

    move-result-object v0

    .line 128
    invoke-virtual {v0, v5}, Landroidx/media3/common/r$b;->j0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    move-result-object v0

    if-eqz v16, :cond_47

    .line 129
    invoke-static/range {v16 .. v16}, Lh2/b$c;->d(Lh2/b$c;)J

    move-result-wide v1

    invoke-static {v1, v2}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    move-result-object v1

    .line 130
    invoke-static/range {v16 .. v16}, Lh2/b$c;->c(Lh2/b$c;)J

    move-result-wide v2

    invoke-static {v2, v3}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->p0(I)Landroidx/media3/common/r$b;

    goto :goto_1f

    :cond_47
    if-eqz v23, :cond_48

    .line 131
    invoke-static/range {v23 .. v23}, Lh2/b$a;->b(Lh2/b$a;)J

    move-result-wide v1

    invoke-static {v1, v2}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    move-result-object v1

    .line 132
    invoke-static/range {v23 .. v23}, Lh2/b$a;->a(Lh2/b$a;)J

    move-result-wide v2

    invoke-static {v2, v3}, Lcom/google/common/primitives/Ints;->n(J)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->p0(I)Landroidx/media3/common/r$b;

    .line 133
    :cond_48
    :goto_1f
    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    move-result-object v0

    iput-object v0, v7, Lh2/b$h;->b:Landroidx/media3/common/r;

    :cond_49
    return-void
.end method

.method public static i(Lt1/G;)Landroidx/media3/common/i;
    .locals 15

    .line 1
    new-instance v0, Landroidx/media3/common/i$b;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/media3/common/i$b;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lt1/F;

    .line 7
    .line 8
    invoke-virtual {p0}, Lt1/G;->e()[B

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    invoke-direct {v1, v2}, Lt1/F;-><init>([B)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 16
    .line 17
    .line 18
    move-result p0

    .line 19
    const/16 v2, 0x8

    .line 20
    .line 21
    mul-int/lit8 p0, p0, 0x8

    .line 22
    .line 23
    invoke-virtual {v1, p0}, Lt1/F;->p(I)V

    .line 24
    .line 25
    .line 26
    const/4 p0, 0x1

    .line 27
    invoke-virtual {v1, p0}, Lt1/F;->s(I)V

    .line 28
    .line 29
    .line 30
    const/4 v3, 0x3

    .line 31
    invoke-virtual {v1, v3}, Lt1/F;->h(I)I

    .line 32
    .line 33
    .line 34
    move-result v4

    .line 35
    const/4 v5, 0x6

    .line 36
    invoke-virtual {v1, v5}, Lt1/F;->r(I)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 40
    .line 41
    .line 42
    move-result v5

    .line 43
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 44
    .line 45
    .line 46
    move-result v6

    .line 47
    const/16 v7, 0xc

    .line 48
    .line 49
    const/16 v8, 0xa

    .line 50
    .line 51
    const/4 v9, 0x2

    .line 52
    if-ne v4, v9, :cond_2

    .line 53
    .line 54
    if-eqz v5, :cond_2

    .line 55
    .line 56
    if-eqz v6, :cond_0

    .line 57
    .line 58
    const/16 v4, 0xc

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_0
    const/16 v4, 0xa

    .line 62
    .line 63
    :goto_0
    invoke-virtual {v0, v4}, Landroidx/media3/common/i$b;->g(I)Landroidx/media3/common/i$b;

    .line 64
    .line 65
    .line 66
    if-eqz v6, :cond_1

    .line 67
    .line 68
    const/16 v8, 0xc

    .line 69
    .line 70
    :cond_1
    invoke-virtual {v0, v8}, Landroidx/media3/common/i$b;->b(I)Landroidx/media3/common/i$b;

    .line 71
    .line 72
    .line 73
    goto :goto_3

    .line 74
    :cond_2
    if-gt v4, v9, :cond_5

    .line 75
    .line 76
    if-eqz v5, :cond_3

    .line 77
    .line 78
    const/16 v4, 0xa

    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_3
    const/16 v4, 0x8

    .line 82
    .line 83
    :goto_1
    invoke-virtual {v0, v4}, Landroidx/media3/common/i$b;->g(I)Landroidx/media3/common/i$b;

    .line 84
    .line 85
    .line 86
    if-eqz v5, :cond_4

    .line 87
    .line 88
    goto :goto_2

    .line 89
    :cond_4
    const/16 v8, 0x8

    .line 90
    .line 91
    :goto_2
    invoke-virtual {v0, v8}, Landroidx/media3/common/i$b;->b(I)Landroidx/media3/common/i$b;

    .line 92
    .line 93
    .line 94
    :cond_5
    :goto_3
    const/16 v4, 0xd

    .line 95
    .line 96
    invoke-virtual {v1, v4}, Lt1/F;->r(I)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 100
    .line 101
    .line 102
    const/4 v5, 0x4

    .line 103
    invoke-virtual {v1, v5}, Lt1/F;->h(I)I

    .line 104
    .line 105
    .line 106
    move-result v6

    .line 107
    const-string v8, "BoxParsers"

    .line 108
    .line 109
    if-eq v6, p0, :cond_6

    .line 110
    .line 111
    new-instance p0, Ljava/lang/StringBuilder;

    .line 112
    .line 113
    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    .line 114
    .line 115
    .line 116
    const-string v1, "Unsupported obu_type: "

    .line 117
    .line 118
    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {p0, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object p0

    .line 128
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 132
    .line 133
    .line 134
    move-result-object p0

    .line 135
    return-object p0

    .line 136
    :cond_6
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 137
    .line 138
    .line 139
    move-result v6

    .line 140
    if-eqz v6, :cond_7

    .line 141
    .line 142
    const-string p0, "Unsupported obu_extension_flag"

    .line 143
    .line 144
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 145
    .line 146
    .line 147
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 148
    .line 149
    .line 150
    move-result-object p0

    .line 151
    return-object p0

    .line 152
    :cond_7
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 153
    .line 154
    .line 155
    move-result v6

    .line 156
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 157
    .line 158
    .line 159
    if-eqz v6, :cond_8

    .line 160
    .line 161
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 162
    .line 163
    .line 164
    move-result v6

    .line 165
    const/16 v10, 0x7f

    .line 166
    .line 167
    if-le v6, v10, :cond_8

    .line 168
    .line 169
    const-string p0, "Excessive obu_size"

    .line 170
    .line 171
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 172
    .line 173
    .line 174
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 175
    .line 176
    .line 177
    move-result-object p0

    .line 178
    return-object p0

    .line 179
    :cond_8
    invoke-virtual {v1, v3}, Lt1/F;->h(I)I

    .line 180
    .line 181
    .line 182
    move-result v6

    .line 183
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 184
    .line 185
    .line 186
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 187
    .line 188
    .line 189
    move-result v10

    .line 190
    if-eqz v10, :cond_9

    .line 191
    .line 192
    const-string p0, "Unsupported reduced_still_picture_header"

    .line 193
    .line 194
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 195
    .line 196
    .line 197
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 198
    .line 199
    .line 200
    move-result-object p0

    .line 201
    return-object p0

    .line 202
    :cond_9
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 203
    .line 204
    .line 205
    move-result v10

    .line 206
    if-eqz v10, :cond_a

    .line 207
    .line 208
    const-string p0, "Unsupported timing_info_present_flag"

    .line 209
    .line 210
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 211
    .line 212
    .line 213
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 214
    .line 215
    .line 216
    move-result-object p0

    .line 217
    return-object p0

    .line 218
    :cond_a
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 219
    .line 220
    .line 221
    move-result v10

    .line 222
    if-eqz v10, :cond_b

    .line 223
    .line 224
    const-string p0, "Unsupported initial_display_delay_present_flag"

    .line 225
    .line 226
    invoke-static {v8, p0}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 227
    .line 228
    .line 229
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 230
    .line 231
    .line 232
    move-result-object p0

    .line 233
    return-object p0

    .line 234
    :cond_b
    const/4 v8, 0x5

    .line 235
    invoke-virtual {v1, v8}, Lt1/F;->h(I)I

    .line 236
    .line 237
    .line 238
    move-result v10

    .line 239
    const/4 v11, 0x0

    .line 240
    const/4 v12, 0x0

    .line 241
    :goto_4
    const/4 v13, 0x7

    .line 242
    if-gt v12, v10, :cond_d

    .line 243
    .line 244
    invoke-virtual {v1, v7}, Lt1/F;->r(I)V

    .line 245
    .line 246
    .line 247
    invoke-virtual {v1, v8}, Lt1/F;->h(I)I

    .line 248
    .line 249
    .line 250
    move-result v14

    .line 251
    if-le v14, v13, :cond_c

    .line 252
    .line 253
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 254
    .line 255
    .line 256
    :cond_c
    add-int/lit8 v12, v12, 0x1

    .line 257
    .line 258
    goto :goto_4

    .line 259
    :cond_d
    invoke-virtual {v1, v5}, Lt1/F;->h(I)I

    .line 260
    .line 261
    .line 262
    move-result v7

    .line 263
    invoke-virtual {v1, v5}, Lt1/F;->h(I)I

    .line 264
    .line 265
    .line 266
    move-result v5

    .line 267
    add-int/2addr v7, p0

    .line 268
    invoke-virtual {v1, v7}, Lt1/F;->r(I)V

    .line 269
    .line 270
    .line 271
    add-int/2addr v5, p0

    .line 272
    invoke-virtual {v1, v5}, Lt1/F;->r(I)V

    .line 273
    .line 274
    .line 275
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 276
    .line 277
    .line 278
    move-result v5

    .line 279
    if-eqz v5, :cond_e

    .line 280
    .line 281
    invoke-virtual {v1, v13}, Lt1/F;->r(I)V

    .line 282
    .line 283
    .line 284
    :cond_e
    invoke-virtual {v1, v13}, Lt1/F;->r(I)V

    .line 285
    .line 286
    .line 287
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 288
    .line 289
    .line 290
    move-result v5

    .line 291
    if-eqz v5, :cond_f

    .line 292
    .line 293
    invoke-virtual {v1, v9}, Lt1/F;->r(I)V

    .line 294
    .line 295
    .line 296
    :cond_f
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 297
    .line 298
    .line 299
    move-result v7

    .line 300
    if-eqz v7, :cond_10

    .line 301
    .line 302
    const/4 v7, 0x2

    .line 303
    goto :goto_5

    .line 304
    :cond_10
    invoke-virtual {v1, p0}, Lt1/F;->h(I)I

    .line 305
    .line 306
    .line 307
    move-result v7

    .line 308
    :goto_5
    if-lez v7, :cond_11

    .line 309
    .line 310
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 311
    .line 312
    .line 313
    move-result v7

    .line 314
    if-nez v7, :cond_11

    .line 315
    .line 316
    invoke-virtual {v1, p0}, Lt1/F;->r(I)V

    .line 317
    .line 318
    .line 319
    :cond_11
    if-eqz v5, :cond_12

    .line 320
    .line 321
    invoke-virtual {v1, v3}, Lt1/F;->r(I)V

    .line 322
    .line 323
    .line 324
    :cond_12
    invoke-virtual {v1, v3}, Lt1/F;->r(I)V

    .line 325
    .line 326
    .line 327
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 328
    .line 329
    .line 330
    move-result v3

    .line 331
    if-ne v6, v9, :cond_13

    .line 332
    .line 333
    if-eqz v3, :cond_13

    .line 334
    .line 335
    invoke-virtual {v1}, Lt1/F;->q()V

    .line 336
    .line 337
    .line 338
    :cond_13
    if-eq v6, p0, :cond_14

    .line 339
    .line 340
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 341
    .line 342
    .line 343
    move-result v3

    .line 344
    if-eqz v3, :cond_14

    .line 345
    .line 346
    const/4 v11, 0x1

    .line 347
    :cond_14
    invoke-virtual {v1}, Lt1/F;->g()Z

    .line 348
    .line 349
    .line 350
    move-result v3

    .line 351
    if-eqz v3, :cond_17

    .line 352
    .line 353
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 354
    .line 355
    .line 356
    move-result v3

    .line 357
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 358
    .line 359
    .line 360
    move-result v5

    .line 361
    invoke-virtual {v1, v2}, Lt1/F;->h(I)I

    .line 362
    .line 363
    .line 364
    move-result v2

    .line 365
    if-nez v11, :cond_15

    .line 366
    .line 367
    if-ne v3, p0, :cond_15

    .line 368
    .line 369
    if-ne v5, v4, :cond_15

    .line 370
    .line 371
    if-nez v2, :cond_15

    .line 372
    .line 373
    const/4 v1, 0x1

    .line 374
    goto :goto_6

    .line 375
    :cond_15
    invoke-virtual {v1, p0}, Lt1/F;->h(I)I

    .line 376
    .line 377
    .line 378
    move-result v1

    .line 379
    :goto_6
    invoke-static {v3}, Landroidx/media3/common/i;->j(I)I

    .line 380
    .line 381
    .line 382
    move-result v2

    .line 383
    invoke-virtual {v0, v2}, Landroidx/media3/common/i$b;->d(I)Landroidx/media3/common/i$b;

    .line 384
    .line 385
    .line 386
    move-result-object v2

    .line 387
    if-ne v1, p0, :cond_16

    .line 388
    .line 389
    goto :goto_7

    .line 390
    :cond_16
    const/4 p0, 0x2

    .line 391
    :goto_7
    invoke-virtual {v2, p0}, Landroidx/media3/common/i$b;->c(I)Landroidx/media3/common/i$b;

    .line 392
    .line 393
    .line 394
    move-result-object p0

    .line 395
    invoke-static {v5}, Landroidx/media3/common/i;->k(I)I

    .line 396
    .line 397
    .line 398
    move-result v1

    .line 399
    invoke-virtual {p0, v1}, Landroidx/media3/common/i$b;->e(I)Landroidx/media3/common/i$b;

    .line 400
    .line 401
    .line 402
    :cond_17
    invoke-virtual {v0}, Landroidx/media3/common/i$b;->a()Landroidx/media3/common/i;

    .line 403
    .line 404
    .line 405
    move-result-object p0

    .line 406
    return-object p0
.end method

.method public static j(Lt1/G;I)Lh2/b$a;
    .locals 3

    .line 1
    add-int/lit8 p1, p1, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    const/4 p1, 0x4

    .line 7
    invoke-virtual {p0, p1}, Lt1/G;->X(I)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 11
    .line 12
    .line 13
    move-result-wide v0

    .line 14
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 15
    .line 16
    .line 17
    move-result-wide p0

    .line 18
    new-instance v2, Lh2/b$a;

    .line 19
    .line 20
    invoke-direct {v2, p0, p1, v0, v1}, Lh2/b$a;-><init>(JJ)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static k(Lt1/G;II)Landroid/util/Pair;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt1/G;",
            "II)",
            "Landroid/util/Pair<",
            "Ljava/lang/Integer;",
            "Lh2/u;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    add-int/lit8 v0, p1, 0x8

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    const/4 v3, 0x0

    .line 6
    move-object v4, v3

    .line 7
    move-object v6, v4

    .line 8
    const/4 v5, -0x1

    .line 9
    const/4 v7, 0x0

    .line 10
    :goto_0
    sub-int v8, v0, p1

    .line 11
    .line 12
    if-ge v8, p2, :cond_3

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 18
    .line 19
    .line 20
    move-result v8

    .line 21
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 22
    .line 23
    .line 24
    move-result v9

    .line 25
    const v10, 0x66726d61

    .line 26
    .line 27
    .line 28
    if-ne v9, v10, :cond_0

    .line 29
    .line 30
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 31
    .line 32
    .line 33
    move-result v6

    .line 34
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    goto :goto_1

    .line 39
    :cond_0
    const v10, 0x7363686d

    .line 40
    .line 41
    .line 42
    if-ne v9, v10, :cond_1

    .line 43
    .line 44
    const/4 v4, 0x4

    .line 45
    invoke-virtual {p0, v4}, Lt1/G;->X(I)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p0, v4}, Lt1/G;->E(I)Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    goto :goto_1

    .line 53
    :cond_1
    const v10, 0x73636869

    .line 54
    .line 55
    .line 56
    if-ne v9, v10, :cond_2

    .line 57
    .line 58
    move v5, v0

    .line 59
    move v7, v8

    .line 60
    :cond_2
    :goto_1
    add-int/2addr v0, v8

    .line 61
    goto :goto_0

    .line 62
    :cond_3
    const-string p1, "cenc"

    .line 63
    .line 64
    invoke-virtual {p1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-nez p1, :cond_5

    .line 69
    .line 70
    const-string p1, "cbc1"

    .line 71
    .line 72
    invoke-virtual {p1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 73
    .line 74
    .line 75
    move-result p1

    .line 76
    if-nez p1, :cond_5

    .line 77
    .line 78
    const-string p1, "cens"

    .line 79
    .line 80
    invoke-virtual {p1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-nez p1, :cond_5

    .line 85
    .line 86
    const-string p1, "cbcs"

    .line 87
    .line 88
    invoke-virtual {p1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    move-result p1

    .line 92
    if-eqz p1, :cond_4

    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_4
    return-object v3

    .line 96
    :cond_5
    :goto_2
    const/4 p1, 0x1

    .line 97
    if-eqz v6, :cond_6

    .line 98
    .line 99
    const/4 p2, 0x1

    .line 100
    goto :goto_3

    .line 101
    :cond_6
    const/4 p2, 0x0

    .line 102
    :goto_3
    const-string v0, "frma atom is mandatory"

    .line 103
    .line 104
    invoke-static {p2, v0}, LN1/u;->a(ZLjava/lang/String;)V

    .line 105
    .line 106
    .line 107
    if-eq v5, v1, :cond_7

    .line 108
    .line 109
    const/4 p2, 0x1

    .line 110
    goto :goto_4

    .line 111
    :cond_7
    const/4 p2, 0x0

    .line 112
    :goto_4
    const-string v0, "schi atom is mandatory"

    .line 113
    .line 114
    invoke-static {p2, v0}, LN1/u;->a(ZLjava/lang/String;)V

    .line 115
    .line 116
    .line 117
    invoke-static {p0, v5, v7, v4}, Lh2/b;->z(Lt1/G;IILjava/lang/String;)Lh2/u;

    .line 118
    .line 119
    .line 120
    move-result-object p0

    .line 121
    if-eqz p0, :cond_8

    .line 122
    .line 123
    const/4 v2, 0x1

    .line 124
    :cond_8
    const-string p1, "tenc atom is mandatory"

    .line 125
    .line 126
    invoke-static {v2, p1}, LN1/u;->a(ZLjava/lang/String;)V

    .line 127
    .line 128
    .line 129
    invoke-static {p0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    check-cast p0, Lh2/u;

    .line 134
    .line 135
    invoke-static {v6, p0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    .line 136
    .line 137
    .line 138
    move-result-object p0

    .line 139
    return-object p0
.end method

.method public static l(Landroidx/media3/container/d$b;)Landroid/util/Pair;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/container/d$b;",
            ")",
            "Landroid/util/Pair<",
            "[J[J>;"
        }
    .end annotation

    .line 1
    const v0, 0x656c7374

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, v0}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 5
    .line 6
    .line 7
    move-result-object p0

    .line 8
    if-nez p0, :cond_0

    .line 9
    .line 10
    const/4 p0, 0x0

    .line 11
    return-object p0

    .line 12
    :cond_0
    iget-object p0, p0, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 13
    .line 14
    const/16 v0, 0x8

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    invoke-static {v0}, Lh2/b;->p(I)I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    invoke-virtual {p0}, Lt1/G;->L()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    new-array v2, v1, [J

    .line 32
    .line 33
    new-array v3, v1, [J

    .line 34
    .line 35
    const/4 v4, 0x0

    .line 36
    :goto_0
    if-ge v4, v1, :cond_4

    .line 37
    .line 38
    const/4 v5, 0x1

    .line 39
    if-ne v0, v5, :cond_1

    .line 40
    .line 41
    invoke-virtual {p0}, Lt1/G;->O()J

    .line 42
    .line 43
    .line 44
    move-result-wide v6

    .line 45
    goto :goto_1

    .line 46
    :cond_1
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 47
    .line 48
    .line 49
    move-result-wide v6

    .line 50
    :goto_1
    aput-wide v6, v2, v4

    .line 51
    .line 52
    if-ne v0, v5, :cond_2

    .line 53
    .line 54
    invoke-virtual {p0}, Lt1/G;->A()J

    .line 55
    .line 56
    .line 57
    move-result-wide v6

    .line 58
    goto :goto_2

    .line 59
    :cond_2
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 60
    .line 61
    .line 62
    move-result v6

    .line 63
    int-to-long v6, v6

    .line 64
    :goto_2
    aput-wide v6, v3, v4

    .line 65
    .line 66
    invoke-virtual {p0}, Lt1/G;->D()S

    .line 67
    .line 68
    .line 69
    move-result v6

    .line 70
    if-ne v6, v5, :cond_3

    .line 71
    .line 72
    const/4 v5, 0x2

    .line 73
    invoke-virtual {p0, v5}, Lt1/G;->X(I)V

    .line 74
    .line 75
    .line 76
    add-int/lit8 v4, v4, 0x1

    .line 77
    .line 78
    goto :goto_0

    .line 79
    :cond_3
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 80
    .line 81
    const-string v0, "Unsupported media rate."

    .line 82
    .line 83
    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw p0

    .line 87
    :cond_4
    invoke-static {v2, v3}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    .line 88
    .line 89
    .line 90
    move-result-object p0

    .line 91
    return-object p0
.end method

.method public static m(Lt1/G;I)Lh2/b$c;
    .locals 10

    .line 1
    add-int/lit8 p1, p1, 0xc

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    const/4 p1, 0x1

    .line 7
    invoke-virtual {p0, p1}, Lt1/G;->X(I)V

    .line 8
    .line 9
    .line 10
    invoke-static {p0}, Lh2/b;->n(Lt1/G;)I

    .line 11
    .line 12
    .line 13
    const/4 v0, 0x2

    .line 14
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    and-int/lit16 v2, v1, 0x80

    .line 22
    .line 23
    if-eqz v2, :cond_0

    .line 24
    .line 25
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 26
    .line 27
    .line 28
    :cond_0
    and-int/lit8 v2, v1, 0x40

    .line 29
    .line 30
    if-eqz v2, :cond_1

    .line 31
    .line 32
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 33
    .line 34
    .line 35
    move-result v2

    .line 36
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 37
    .line 38
    .line 39
    :cond_1
    and-int/lit8 v1, v1, 0x20

    .line 40
    .line 41
    if-eqz v1, :cond_2

    .line 42
    .line 43
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 44
    .line 45
    .line 46
    :cond_2
    invoke-virtual {p0, p1}, Lt1/G;->X(I)V

    .line 47
    .line 48
    .line 49
    invoke-static {p0}, Lh2/b;->n(Lt1/G;)I

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    invoke-static {v0}, Landroidx/media3/common/y;->h(I)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    const-string v0, "audio/mpeg"

    .line 61
    .line 62
    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    if-nez v0, :cond_6

    .line 67
    .line 68
    const-string v0, "audio/vnd.dts"

    .line 69
    .line 70
    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 71
    .line 72
    .line 73
    move-result v0

    .line 74
    if-nez v0, :cond_6

    .line 75
    .line 76
    const-string v0, "audio/vnd.dts.hd"

    .line 77
    .line 78
    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    if-eqz v0, :cond_3

    .line 83
    .line 84
    goto :goto_1

    .line 85
    :cond_3
    const/4 v0, 0x4

    .line 86
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 87
    .line 88
    .line 89
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 90
    .line 91
    .line 92
    move-result-wide v0

    .line 93
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 94
    .line 95
    .line 96
    move-result-wide v3

    .line 97
    invoke-virtual {p0, p1}, Lt1/G;->X(I)V

    .line 98
    .line 99
    .line 100
    invoke-static {p0}, Lh2/b;->n(Lt1/G;)I

    .line 101
    .line 102
    .line 103
    move-result p1

    .line 104
    move-wide v4, v3

    .line 105
    new-array v3, p1, [B

    .line 106
    .line 107
    const/4 v6, 0x0

    .line 108
    invoke-virtual {p0, v3, v6, p1}, Lt1/G;->l([BII)V

    .line 109
    .line 110
    .line 111
    move-wide p0, v0

    .line 112
    new-instance v1, Lh2/b$c;

    .line 113
    .line 114
    const-wide/16 v6, -0x1

    .line 115
    .line 116
    const-wide/16 v8, 0x0

    .line 117
    .line 118
    cmp-long v0, v4, v8

    .line 119
    .line 120
    if-lez v0, :cond_4

    .line 121
    .line 122
    goto :goto_0

    .line 123
    :cond_4
    move-wide v4, v6

    .line 124
    :goto_0
    cmp-long v0, p0, v8

    .line 125
    .line 126
    if-lez v0, :cond_5

    .line 127
    .line 128
    move-wide v6, p0

    .line 129
    :cond_5
    invoke-direct/range {v1 .. v7}, Lh2/b$c;-><init>(Ljava/lang/String;[BJJ)V

    .line 130
    .line 131
    .line 132
    return-object v1

    .line 133
    :cond_6
    :goto_1
    new-instance v1, Lh2/b$c;

    .line 134
    .line 135
    const-wide/16 v4, -0x1

    .line 136
    .line 137
    const-wide/16 v6, -0x1

    .line 138
    .line 139
    const/4 v3, 0x0

    .line 140
    invoke-direct/range {v1 .. v7}, Lh2/b$c;-><init>(Ljava/lang/String;[BJJ)V

    .line 141
    .line 142
    .line 143
    return-object v1
.end method

.method public static n(Lt1/G;)I
    .locals 3

    .line 1
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    and-int/lit8 v1, v0, 0x7f

    .line 6
    .line 7
    :goto_0
    const/16 v2, 0x80

    .line 8
    .line 9
    and-int/2addr v0, v2

    .line 10
    if-ne v0, v2, :cond_0

    .line 11
    .line 12
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    shl-int/lit8 v1, v1, 0x7

    .line 17
    .line 18
    and-int/lit8 v2, v0, 0x7f

    .line 19
    .line 20
    or-int/2addr v1, v2

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    return v1
.end method

.method public static o(I)I
    .locals 1

    .line 1
    const v0, 0xffffff

    .line 2
    .line 3
    .line 4
    and-int/2addr p0, v0

    .line 5
    return p0
.end method

.method public static p(I)I
    .locals 0

    .line 1
    shr-int/lit8 p0, p0, 0x18

    .line 2
    .line 3
    and-int/lit16 p0, p0, 0xff

    .line 4
    .line 5
    return p0
.end method

.method public static q(Lt1/G;)I
    .locals 1

    .line 1
    const/16 v0, 0x10

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result p0

    .line 10
    return p0
.end method

.method public static r(Lt1/G;I)Landroidx/media3/common/x;
    .locals 2

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Ljava/util/ArrayList;

    .line 7
    .line 8
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 9
    .line 10
    .line 11
    :cond_0
    :goto_0
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-ge v1, p1, :cond_1

    .line 16
    .line 17
    invoke-static {p0}, Lh2/j;->d(Lt1/G;)Landroidx/media3/common/x$a;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    if-eqz v1, :cond_0

    .line 22
    .line 23
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_1
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    .line 28
    .line 29
    .line 30
    move-result p0

    .line 31
    if-eqz p0, :cond_2

    .line 32
    .line 33
    const/4 p0, 0x0

    .line 34
    return-object p0

    .line 35
    :cond_2
    new-instance p0, Landroidx/media3/common/x;

    .line 36
    .line 37
    invoke-direct {p0, v0}, Landroidx/media3/common/x;-><init>(Ljava/util/List;)V

    .line 38
    .line 39
    .line 40
    return-object p0
.end method

.method public static s(Lt1/G;)Lh2/b$e;
    .locals 10

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    invoke-static {v1}, Lh2/b;->p(I)I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-nez v1, :cond_0

    .line 15
    .line 16
    const/16 v2, 0x8

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/16 v2, 0x10

    .line 20
    .line 21
    :goto_0
    invoke-virtual {p0, v2}, Lt1/G;->X(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 25
    .line 26
    .line 27
    move-result-wide v4

    .line 28
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-nez v1, :cond_1

    .line 33
    .line 34
    const/4 v0, 0x4

    .line 35
    :cond_1
    const/4 v3, 0x0

    .line 36
    :goto_1
    const-wide v6, -0x7fffffffffffffffL    # -4.9E-324

    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    if-ge v3, v0, :cond_5

    .line 42
    .line 43
    invoke-virtual {p0}, Lt1/G;->e()[B

    .line 44
    .line 45
    .line 46
    move-result-object v8

    .line 47
    add-int v9, v2, v3

    .line 48
    .line 49
    aget-byte v8, v8, v9

    .line 50
    .line 51
    const/4 v9, -0x1

    .line 52
    if-eq v8, v9, :cond_4

    .line 53
    .line 54
    if-nez v1, :cond_2

    .line 55
    .line 56
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 57
    .line 58
    .line 59
    move-result-wide v0

    .line 60
    goto :goto_2

    .line 61
    :cond_2
    invoke-virtual {p0}, Lt1/G;->O()J

    .line 62
    .line 63
    .line 64
    move-result-wide v0

    .line 65
    :goto_2
    const-wide/16 v2, 0x0

    .line 66
    .line 67
    cmp-long v8, v0, v2

    .line 68
    .line 69
    if-nez v8, :cond_3

    .line 70
    .line 71
    goto :goto_3

    .line 72
    :cond_3
    move-wide v7, v4

    .line 73
    const-wide/32 v5, 0xf4240

    .line 74
    .line 75
    .line 76
    move-wide v3, v0

    .line 77
    invoke-static/range {v3 .. v8}, Lt1/a0;->c1(JJJ)J

    .line 78
    .line 79
    .line 80
    move-result-wide v0

    .line 81
    move-wide v4, v7

    .line 82
    move-wide v6, v0

    .line 83
    goto :goto_3

    .line 84
    :cond_4
    add-int/lit8 v3, v3, 0x1

    .line 85
    .line 86
    goto :goto_1

    .line 87
    :cond_5
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 88
    .line 89
    .line 90
    :goto_3
    invoke-virtual {p0}, Lt1/G;->P()I

    .line 91
    .line 92
    .line 93
    move-result p0

    .line 94
    invoke-static {p0}, Lh2/b;->d(I)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v8

    .line 98
    new-instance v3, Lh2/b$e;

    .line 99
    .line 100
    invoke-direct/range {v3 .. v8}, Lh2/b$e;-><init>(JJLjava/lang/String;)V

    .line 101
    .line 102
    .line 103
    return-object v3
.end method

.method public static t(Landroidx/media3/container/d$b;)Landroidx/media3/common/x;
    .locals 10

    .line 1
    const v0, 0x68646c72

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, v0}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    const v1, 0x6b657973

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, v1}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    const v2, 0x696c7374

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0, v2}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    const/4 v2, 0x0

    .line 23
    if-eqz v0, :cond_6

    .line 24
    .line 25
    if-eqz v1, :cond_6

    .line 26
    .line 27
    if-eqz p0, :cond_6

    .line 28
    .line 29
    iget-object v0, v0, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 30
    .line 31
    invoke-static {v0}, Lh2/b;->q(Lt1/G;)I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    const v3, 0x6d647461

    .line 36
    .line 37
    .line 38
    if-eq v0, v3, :cond_0

    .line 39
    .line 40
    goto/16 :goto_3

    .line 41
    .line 42
    :cond_0
    iget-object v0, v1, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 43
    .line 44
    const/16 v1, 0xc

    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lt1/G;->W(I)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0}, Lt1/G;->q()I

    .line 50
    .line 51
    .line 52
    move-result v1

    .line 53
    new-array v3, v1, [Ljava/lang/String;

    .line 54
    .line 55
    const/4 v4, 0x0

    .line 56
    :goto_0
    const/16 v5, 0x8

    .line 57
    .line 58
    if-ge v4, v1, :cond_1

    .line 59
    .line 60
    invoke-virtual {v0}, Lt1/G;->q()I

    .line 61
    .line 62
    .line 63
    move-result v6

    .line 64
    const/4 v7, 0x4

    .line 65
    invoke-virtual {v0, v7}, Lt1/G;->X(I)V

    .line 66
    .line 67
    .line 68
    sub-int/2addr v6, v5

    .line 69
    invoke-virtual {v0, v6}, Lt1/G;->E(I)Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v5

    .line 73
    aput-object v5, v3, v4

    .line 74
    .line 75
    add-int/lit8 v4, v4, 0x1

    .line 76
    .line 77
    goto :goto_0

    .line 78
    :cond_1
    iget-object p0, p0, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 79
    .line 80
    invoke-virtual {p0, v5}, Lt1/G;->W(I)V

    .line 81
    .line 82
    .line 83
    new-instance v0, Ljava/util/ArrayList;

    .line 84
    .line 85
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 86
    .line 87
    .line 88
    :goto_1
    invoke-virtual {p0}, Lt1/G;->a()I

    .line 89
    .line 90
    .line 91
    move-result v4

    .line 92
    if-le v4, v5, :cond_4

    .line 93
    .line 94
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 95
    .line 96
    .line 97
    move-result v4

    .line 98
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 99
    .line 100
    .line 101
    move-result v6

    .line 102
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 103
    .line 104
    .line 105
    move-result v7

    .line 106
    add-int/lit8 v7, v7, -0x1

    .line 107
    .line 108
    if-ltz v7, :cond_2

    .line 109
    .line 110
    if-ge v7, v1, :cond_2

    .line 111
    .line 112
    aget-object v7, v3, v7

    .line 113
    .line 114
    add-int v8, v4, v6

    .line 115
    .line 116
    invoke-static {p0, v8, v7}, Lh2/j;->i(Lt1/G;ILjava/lang/String;)Landroidx/media3/container/b;

    .line 117
    .line 118
    .line 119
    move-result-object v7

    .line 120
    if-eqz v7, :cond_3

    .line 121
    .line 122
    invoke-virtual {v0, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    goto :goto_2

    .line 126
    :cond_2
    new-instance v8, Ljava/lang/StringBuilder;

    .line 127
    .line 128
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 129
    .line 130
    .line 131
    const-string v9, "Skipped metadata with unknown key index: "

    .line 132
    .line 133
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 134
    .line 135
    .line 136
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 137
    .line 138
    .line 139
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object v7

    .line 143
    const-string v8, "BoxParsers"

    .line 144
    .line 145
    invoke-static {v8, v7}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    .line 146
    .line 147
    .line 148
    :cond_3
    :goto_2
    add-int/2addr v4, v6

    .line 149
    invoke-virtual {p0, v4}, Lt1/G;->W(I)V

    .line 150
    .line 151
    .line 152
    goto :goto_1

    .line 153
    :cond_4
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    .line 154
    .line 155
    .line 156
    move-result p0

    .line 157
    if-eqz p0, :cond_5

    .line 158
    .line 159
    return-object v2

    .line 160
    :cond_5
    new-instance p0, Landroidx/media3/common/x;

    .line 161
    .line 162
    invoke-direct {p0, v0}, Landroidx/media3/common/x;-><init>(Ljava/util/List;)V

    .line 163
    .line 164
    .line 165
    return-object p0

    .line 166
    :cond_6
    :goto_3
    return-object v2
.end method

.method public static u(Lt1/G;IIILh2/b$h;)V
    .locals 0

    .line 1
    add-int/lit8 p2, p2, 0x10

    .line 2
    .line 3
    invoke-virtual {p0, p2}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    const p2, 0x6d657474

    .line 7
    .line 8
    .line 9
    if-ne p1, p2, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lt1/G;->B()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lt1/G;->B()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    if-eqz p0, :cond_0

    .line 19
    .line 20
    new-instance p1, Landroidx/media3/common/r$b;

    .line 21
    .line 22
    invoke-direct {p1}, Landroidx/media3/common/r$b;-><init>()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p1, p3}, Landroidx/media3/common/r$b;->e0(I)Landroidx/media3/common/r$b;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {p1, p0}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-virtual {p0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    iput-object p0, p4, Lh2/b$h;->b:Landroidx/media3/common/r;

    .line 38
    .line 39
    :cond_0
    return-void
.end method

.method public static v(Lt1/G;)Landroidx/media3/container/f;
    .locals 11

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    invoke-static {v0}, Lh2/b;->p(I)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 17
    .line 18
    .line 19
    move-result-wide v0

    .line 20
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 21
    .line 22
    .line 23
    move-result-wide v2

    .line 24
    :goto_0
    move-wide v5, v0

    .line 25
    move-wide v7, v2

    .line 26
    goto :goto_1

    .line 27
    :cond_0
    invoke-virtual {p0}, Lt1/G;->A()J

    .line 28
    .line 29
    .line 30
    move-result-wide v0

    .line 31
    invoke-virtual {p0}, Lt1/G;->A()J

    .line 32
    .line 33
    .line 34
    move-result-wide v2

    .line 35
    goto :goto_0

    .line 36
    :goto_1
    invoke-virtual {p0}, Lt1/G;->J()J

    .line 37
    .line 38
    .line 39
    move-result-wide v9

    .line 40
    new-instance v4, Landroidx/media3/container/f;

    .line 41
    .line 42
    invoke-direct/range {v4 .. v10}, Landroidx/media3/container/f;-><init>(JJJ)V

    .line 43
    .line 44
    .line 45
    return-object v4
.end method

.method public static w(Lt1/G;I)F
    .locals 0

    .line 1
    add-int/lit8 p1, p1, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->L()I

    .line 7
    .line 8
    .line 9
    move-result p1

    .line 10
    invoke-virtual {p0}, Lt1/G;->L()I

    .line 11
    .line 12
    .line 13
    move-result p0

    .line 14
    int-to-float p1, p1

    .line 15
    int-to-float p0, p0

    .line 16
    div-float/2addr p1, p0

    .line 17
    return p1
.end method

.method public static x(Lt1/G;II)[B
    .locals 4

    .line 1
    add-int/lit8 v0, p1, 0x8

    .line 2
    .line 3
    :goto_0
    sub-int v1, v0, p1

    .line 4
    .line 5
    if-ge v1, p2, :cond_1

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    const v3, 0x70726f6a

    .line 19
    .line 20
    .line 21
    if-ne v2, v3, :cond_0

    .line 22
    .line 23
    invoke-virtual {p0}, Lt1/G;->e()[B

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    add-int/2addr v1, v0

    .line 28
    invoke-static {p0, v0, v1}, Ljava/util/Arrays;->copyOfRange([BII)[B

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    return-object p0

    .line 33
    :cond_0
    add-int/2addr v0, v1

    .line 34
    goto :goto_0

    .line 35
    :cond_1
    const/4 p0, 0x0

    .line 36
    return-object p0
.end method

.method public static y(Lt1/G;II)Landroid/util/Pair;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt1/G;",
            "II)",
            "Landroid/util/Pair<",
            "Ljava/lang/Integer;",
            "Lh2/u;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lt1/G;->f()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    :goto_0
    sub-int v1, v0, p1

    .line 6
    .line 7
    if-ge v1, p2, :cond_2

    .line 8
    .line 9
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-lez v1, :cond_0

    .line 17
    .line 18
    const/4 v2, 0x1

    .line 19
    goto :goto_1

    .line 20
    :cond_0
    const/4 v2, 0x0

    .line 21
    :goto_1
    const-string v3, "childAtomSize must be positive"

    .line 22
    .line 23
    invoke-static {v2, v3}, LN1/u;->a(ZLjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    const v3, 0x73696e66

    .line 31
    .line 32
    .line 33
    if-ne v2, v3, :cond_1

    .line 34
    .line 35
    invoke-static {p0, v0, v1}, Lh2/b;->k(Lt1/G;II)Landroid/util/Pair;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    if-eqz v2, :cond_1

    .line 40
    .line 41
    return-object v2

    .line 42
    :cond_1
    add-int/2addr v0, v1

    .line 43
    goto :goto_0

    .line 44
    :cond_2
    const/4 p0, 0x0

    .line 45
    return-object p0
.end method

.method public static z(Lt1/G;IILjava/lang/String;)Lh2/u;
    .locals 11

    .line 1
    add-int/lit8 v0, p1, 0x8

    .line 2
    .line 3
    :goto_0
    sub-int v1, v0, p1

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-ge v1, p2, :cond_4

    .line 7
    .line 8
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 16
    .line 17
    .line 18
    move-result v3

    .line 19
    const v4, 0x74656e63

    .line 20
    .line 21
    .line 22
    if-ne v3, v4, :cond_3

    .line 23
    .line 24
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    invoke-static {p1}, Lh2/b;->p(I)I

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    const/4 p2, 0x1

    .line 33
    invoke-virtual {p0, p2}, Lt1/G;->X(I)V

    .line 34
    .line 35
    .line 36
    const/4 v0, 0x0

    .line 37
    if-nez p1, :cond_0

    .line 38
    .line 39
    invoke-virtual {p0, p2}, Lt1/G;->X(I)V

    .line 40
    .line 41
    .line 42
    const/4 v8, 0x0

    .line 43
    const/4 v9, 0x0

    .line 44
    goto :goto_1

    .line 45
    :cond_0
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    and-int/lit16 v1, p1, 0xf0

    .line 50
    .line 51
    shr-int/lit8 v1, v1, 0x4

    .line 52
    .line 53
    and-int/lit8 p1, p1, 0xf

    .line 54
    .line 55
    move v9, p1

    .line 56
    move v8, v1

    .line 57
    :goto_1
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    if-ne p1, p2, :cond_1

    .line 62
    .line 63
    const/4 v4, 0x1

    .line 64
    goto :goto_2

    .line 65
    :cond_1
    const/4 v4, 0x0

    .line 66
    :goto_2
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 67
    .line 68
    .line 69
    move-result v6

    .line 70
    const/16 p1, 0x10

    .line 71
    .line 72
    new-array v7, p1, [B

    .line 73
    .line 74
    invoke-virtual {p0, v7, v0, p1}, Lt1/G;->l([BII)V

    .line 75
    .line 76
    .line 77
    if-eqz v4, :cond_2

    .line 78
    .line 79
    if-nez v6, :cond_2

    .line 80
    .line 81
    invoke-virtual {p0}, Lt1/G;->H()I

    .line 82
    .line 83
    .line 84
    move-result p1

    .line 85
    new-array v2, p1, [B

    .line 86
    .line 87
    invoke-virtual {p0, v2, v0, p1}, Lt1/G;->l([BII)V

    .line 88
    .line 89
    .line 90
    :cond_2
    move-object v10, v2

    .line 91
    new-instance v3, Lh2/u;

    .line 92
    .line 93
    move-object v5, p3

    .line 94
    invoke-direct/range {v3 .. v10}, Lh2/u;-><init>(ZLjava/lang/String;I[BII[B)V

    .line 95
    .line 96
    .line 97
    return-object v3

    .line 98
    :cond_3
    move-object v5, p3

    .line 99
    add-int/2addr v0, v1

    .line 100
    goto :goto_0

    .line 101
    :cond_4
    return-object v2
.end method
