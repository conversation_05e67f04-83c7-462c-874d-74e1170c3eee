.class public final synthetic Ln11/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/r1;

.field public final synthetic b:Z

.field public final synthetic c:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/r1;ZLjava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln11/b;->a:Landroidx/compose/runtime/r1;

    iput-boolean p2, p0, Ln11/b;->b:Z

    iput-object p3, p0, Ln11/b;->c:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ln11/b;->a:Landroidx/compose/runtime/r1;

    iget-boolean v1, p0, Ln11/b;->b:Z

    iget-object v2, p0, Ln11/b;->c:Ljava/util/List;

    check-cast p1, Landroidx/compose/ui/graphics/drawscope/f;

    invoke-static {v0, v1, v2, p1}, Ln11/d;->b(Landroidx/compose/runtime/r1;ZLjava/util/List;Landroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
