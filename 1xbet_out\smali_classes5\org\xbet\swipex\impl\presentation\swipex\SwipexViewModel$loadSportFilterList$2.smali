.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$loadSportFilterList$2"
    f = "SwipexViewModel.kt"
    l = {
        0x25b,
        0x25c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->t4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;

    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->L$0:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lorg/xbet/swipex/impl/domain/model/SynchronizedStatus;

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto :goto_2

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->G3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iput v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->label:I

    .line 45
    .line 46
    invoke-virtual {p1, p0}, Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;->i(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    if-ne p1, v0, :cond_3

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_3
    :goto_0
    check-cast p1, Lorg/xbet/swipex/impl/domain/model/SynchronizedStatus;

    .line 54
    .line 55
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 56
    .line 57
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->D3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    iput v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->label:I

    .line 64
    .line 65
    invoke-virtual {v1, p0}, Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    if-ne v1, v0, :cond_4

    .line 70
    .line 71
    :goto_1
    return-object v0

    .line 72
    :cond_4
    move-object v0, p1

    .line 73
    move-object p1, v1

    .line 74
    :goto_2
    check-cast p1, Ljava/util/List;

    .line 75
    .line 76
    sget-object v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2$a;->a:[I

    .line 77
    .line 78
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    aget v0, v1, v0

    .line 83
    .line 84
    if-eq v0, v3, :cond_8

    .line 85
    .line 86
    if-eq v0, v2, :cond_7

    .line 87
    .line 88
    const/4 v1, 0x3

    .line 89
    if-eq v0, v1, :cond_6

    .line 90
    .line 91
    const/4 p1, 0x4

    .line 92
    if-ne v0, p1, :cond_5

    .line 93
    .line 94
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 95
    .line 96
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->s3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)V

    .line 97
    .line 98
    .line 99
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 100
    .line 101
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;

    .line 106
    .line 107
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 108
    .line 109
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->I3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-direct {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 114
    .line 115
    .line 116
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 120
    .line 121
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Q3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 130
    .line 131
    .line 132
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 133
    .line 134
    return-object p1

    .line 135
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 136
    .line 137
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 138
    .line 139
    .line 140
    throw p1

    .line 141
    :cond_6
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 142
    .line 143
    invoke-static {v0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->t3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Ljava/util/List;)V

    .line 144
    .line 145
    .line 146
    goto :goto_3

    .line 147
    :cond_7
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 148
    .line 149
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 150
    .line 151
    .line 152
    move-result-object v0

    .line 153
    sget-object v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$e;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$e;

    .line 154
    .line 155
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 156
    .line 157
    .line 158
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 159
    .line 160
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->K3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)LwX0/c;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 165
    .line 166
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->F3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)LGS0/a;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    invoke-virtual {v1}, LGS0/a;->a()Lq4/q;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    invoke-virtual {v0, v1}, LwX0/c;->t(Lq4/q;)V

    .line 175
    .line 176
    .line 177
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 178
    .line 179
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->N3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/analytics/domain/scope/f1;

    .line 180
    .line 181
    .line 182
    move-result-object v0

    .line 183
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/f1;->k()V

    .line 184
    .line 185
    .line 186
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 187
    .line 188
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->P3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)LkS/a;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 193
    .line 194
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->L3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Ljava/lang/String;

    .line 195
    .line 196
    .line 197
    move-result-object v1

    .line 198
    invoke-interface {v0, v1}, LkS/a;->j(Ljava/lang/String;)V

    .line 199
    .line 200
    .line 201
    goto :goto_3

    .line 202
    :cond_8
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 203
    .line 204
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 205
    .line 206
    .line 207
    move-result-object v0

    .line 208
    sget-object v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;

    .line 209
    .line 210
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 211
    .line 212
    .line 213
    :goto_3
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadSportFilterList$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 214
    .line 215
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Q3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 216
    .line 217
    .line 218
    move-result-object v0

    .line 219
    invoke-static {p1}, LKS0/b;->a(Ljava/util/List;)Ljava/util/List;

    .line 220
    .line 221
    .line 222
    move-result-object p1

    .line 223
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 224
    .line 225
    .line 226
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 227
    .line 228
    return-object p1
.end method
