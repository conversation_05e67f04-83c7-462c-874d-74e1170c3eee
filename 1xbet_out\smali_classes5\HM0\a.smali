.class public final synthetic LHM0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LHM0/b;


# direct methods
.method public synthetic constructor <init>(LHM0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LHM0/a;->a:LHM0/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LHM0/a;->a:LHM0/b;

    invoke-static {v0}, LHM0/b;->b(LHM0/b;)LGM0/a;

    move-result-object v0

    return-object v0
.end method
