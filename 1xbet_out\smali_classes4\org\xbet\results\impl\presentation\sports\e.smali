.class public final synthetic Lorg/xbet/results/impl/presentation/sports/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/results/impl/presentation/sports/e;->a:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/e;->a:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;

    invoke-static {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->y2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/results/impl/presentation/sports/a;

    move-result-object v0

    return-object v0
.end method
