.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;
.super Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u000b\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0001\u0018\u0000 _2\u00020\u0001:\u0001`B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\nJ\u000f\u0010\r\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\r\u0010\nJ\u000f\u0010\u000e\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\nJ\u000f\u0010\u000f\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\nJ\u0017\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0014\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0013J\u000f\u0010\u0015\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\nJ\u000f\u0010\u0016\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\nJ\u000f\u0010\u0017\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\nJ\u000f\u0010\u0018\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\nJ\u000f\u0010\u0019\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\nJ\u000f\u0010\u001a\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001b\u0010\u001e\u001a\u00020\u00082\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001b\u0010 \u001a\u00020\u00082\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001fJ#\u0010$\u001a\u00020\u0008*\u00020!2\u0006\u0010\"\u001a\u00020\u00102\u0006\u0010#\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008$\u0010%J\u001f\u0010(\u001a\u00020\u00082\u0006\u0010&\u001a\u00020\u00102\u0006\u0010\'\u001a\u00020\u0010H\u0014\u00a2\u0006\u0004\u0008(\u0010)J7\u00100\u001a\u00020\u00082\u0006\u0010+\u001a\u00020*2\u0006\u0010,\u001a\u00020\u00102\u0006\u0010-\u001a\u00020\u00102\u0006\u0010.\u001a\u00020\u00102\u0006\u0010/\u001a\u00020\u0010H\u0014\u00a2\u0006\u0004\u00080\u00101J\u0019\u00102\u001a\u00020\u00082\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u00082\u0010\u001fJ\u0019\u00103\u001a\u00020\u00082\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u00083\u0010\u001fJ\u0019\u00104\u001a\u00020\u00082\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u00084\u0010\u001fJ\u0017\u00106\u001a\u00020\u00082\u0006\u00105\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u00086\u0010\u0013J\u0017\u00108\u001a\u00020\u00082\u0006\u00107\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u00088\u0010\u0013J\u0017\u0010;\u001a\u00020\u00082\u0006\u0010:\u001a\u000209H\u0016\u00a2\u0006\u0004\u0008;\u0010<R\u0014\u0010>\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010=R\u0014\u0010?\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010=R\u0014\u0010A\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010=R\u0014\u0010B\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010=R\u0014\u0010D\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010=R\u0014\u0010E\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010=R\u0014\u0010F\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010=R\u0014\u0010G\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010=R\u0014\u0010H\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010=R\u0014\u0010I\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010=R\u0014\u0010J\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010=R\u0016\u0010M\u001a\u0004\u0018\u00010K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010LR\u0016\u0010N\u001a\u0004\u0018\u00010K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010LR\u0014\u0010Q\u001a\u00020O8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010PR\u0014\u0010T\u001a\u00020R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010SR\u0014\u0010W\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010VR\u0014\u0010Y\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010VR\u0014\u0010[\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010VR\u001a\u0010^\u001a\u00020\u00108\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008\\\u0010=\u001a\u0004\u0008]\u0010\u001b\u00a8\u0006a"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attributeSet",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "d",
        "()V",
        "f",
        "e",
        "p",
        "t",
        "r",
        "",
        "parentWidth",
        "s",
        "(I)V",
        "q",
        "k",
        "o",
        "m",
        "n",
        "l",
        "getCommonTextViewMeasuredWidth",
        "()I",
        "",
        "text",
        "i",
        "(Ljava/lang/CharSequence;)V",
        "g",
        "Landroid/widget/TextView;",
        "minSize",
        "maxSize",
        "u",
        "(Landroid/widget/TextView;II)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "setStageNumberText",
        "setTitleText",
        "setCaptionText",
        "progress",
        "setProgress",
        "maxProgress",
        "setMaxProgress",
        "Lc31/a;",
        "state",
        "setState",
        "(Lc31/a;)V",
        "I",
        "size22",
        "size24",
        "h",
        "size40",
        "size60",
        "j",
        "space4",
        "space10",
        "space12",
        "textSize1",
        "textSize12",
        "textSize14",
        "textSize16",
        "Landroid/graphics/drawable/Drawable;",
        "Landroid/graphics/drawable/Drawable;",
        "stageNumberBackgroundCompleted",
        "stageNumberBackgroundAwaiting",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;",
        "circularProgressBar",
        "Landroid/view/View;",
        "Landroid/view/View;",
        "vStageNumberBackground",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStageNumber",
        "v",
        "tvTitle",
        "w",
        "tvCaption",
        "x",
        "getCardHeight",
        "cardHeight",
        "y",
        "a",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final y:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final z:I


# instance fields
.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:Landroid/graphics/drawable/Drawable;

.field public final r:Landroid/graphics/drawable/Drawable;

.field public final s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->y:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->z:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 10
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_22:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->f:I

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->g:I

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_40:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_60:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->i:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->j:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->space_10:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->text_1:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->m:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->text_12:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->n:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->text_14:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->o:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->text_16:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->p:I

    .line 14
    sget v1, LlZ0/h;->rounded_background_full:I

    .line 15
    invoke-static {p1, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v1

    const/4 v2, 0x2

    const/4 v3, 0x0

    if-eqz v1, :cond_0

    .line 16
    sget v4, LlZ0/d;->uikitStaticGreen:I

    invoke-static {p1, v4, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    invoke-virtual {v1, v4}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    goto :goto_0

    :cond_0
    move-object v1, v3

    .line 17
    :goto_0
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->q:Landroid/graphics/drawable/Drawable;

    .line 18
    sget v1, LlZ0/h;->rounded_background_full:I

    .line 19
    invoke-static {p1, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 20
    sget v4, LlZ0/d;->uikitBackground:I

    invoke-static {p1, v4, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    invoke-virtual {v1, v4}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    goto :goto_1

    :cond_1
    move-object v1, v3

    .line 21
    :goto_1
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->r:Landroid/graphics/drawable/Drawable;

    .line 22
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    invoke-direct {v1, p1, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v3, p2, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v3

    if-eqz v3, :cond_2

    sget-object v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;->COUNTER_CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    goto :goto_2

    :cond_2
    sget-object v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;->CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    :goto_2
    invoke-virtual {v1, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->setProgressDirection(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;)V

    .line 25
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 26
    new-instance v3, Landroid/view/View;

    invoke-direct {v3, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 27
    new-instance v4, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v4, p2, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v3, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 28
    iput-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 29
    new-instance p2, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {p2, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 30
    const-string v4, "DSAggregatorTournamentStagesCell.TAG_TV_STAGE_NUMBER"

    invoke-virtual {p2, v4}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 31
    sget v4, LlZ0/n;->TextStyle_Headline_Medium_TextPrimary:I

    invoke-static {p2, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/16 v4, 0x11

    .line 32
    invoke-virtual {p2, v4}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v4, 0x1

    .line 33
    invoke-virtual {p2, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 34
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 35
    new-instance v5, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v5, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 36
    const-string v6, "DSAggregatorTournamentStagesCell.TAG_TV_TITLE"

    invoke-virtual {v5, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 37
    sget v6, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v5, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 38
    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 39
    sget-object v2, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v6, 0x0

    .line 40
    invoke-virtual {v5, v6}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 41
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v7

    const/4 v8, 0x3

    const/4 v9, 0x5

    if-eqz v7, :cond_3

    const/4 v7, 0x5

    goto :goto_3

    :cond_3
    const/4 v7, 0x3

    :goto_3
    invoke-virtual {v5, v7}, Landroid/widget/TextView;->setGravity(I)V

    .line 42
    iput-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    new-instance v7, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v7, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 44
    const-string p1, "DSAggregatorTournamentStagesCell.TAG_TV_CAPTION"

    invoke-virtual {v7, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 45
    sget p1, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v7, p1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 46
    invoke-virtual {v7, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 47
    invoke-virtual {v7, v2}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 48
    invoke-virtual {v7, v6}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 49
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result p1

    if-eqz p1, :cond_4

    const/4 v8, 0x5

    :cond_4
    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setGravity(I)V

    .line 50
    iput-object v7, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->x:I

    .line 52
    sget p1, LlZ0/h;->rounded_background_16_content:I

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 53
    invoke-virtual {p0, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 54
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 55
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 56
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 57
    invoke-virtual {p0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final g(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_10:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_12:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final getCommonTextViewMeasuredWidth()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 9
    .line 10
    mul-int/lit8 v1, v1, 0x3

    .line 11
    .line 12
    sub-int/2addr v0, v1

    .line 13
    return v0
.end method

.method public static synthetic h(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->g(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final i(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_12:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_14:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static synthetic j(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->i(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final l()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->j:I

    .line 25
    .line 26
    add-int v5, v0, v1

    .line 27
    .line 28
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 31
    .line 32
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 33
    .line 34
    mul-int/lit8 v1, v1, 0x2

    .line 35
    .line 36
    add-int v4, v0, v1

    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 43
    .line 44
    sub-int v6, v0, v1

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 47
    .line 48
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    add-int v7, v5, v0

    .line 53
    .line 54
    move-object v2, p0

    .line 55
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 56
    .line 57
    .line 58
    :cond_0
    return-void
.end method

.method private final m()V
    .locals 8

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 4
    .line 5
    div-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    add-int/2addr v0, v1

    .line 8
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    div-int/lit8 v1, v1, 0x2

    .line 15
    .line 16
    sub-int v4, v0, v1

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 21
    .line 22
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    add-int v6, v4, v0

    .line 27
    .line 28
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 29
    .line 30
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 31
    .line 32
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    add-int v7, v0, v1

    .line 37
    .line 38
    move-object v2, p0

    .line 39
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method private final n()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lc31/a$a;

    .line 6
    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    instance-of v1, v0, Lc31/a$b;

    .line 10
    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    :cond_0
    move-object v3, p0

    .line 14
    goto :goto_1

    .line 15
    :cond_1
    instance-of v0, v0, Lc31/a$c;

    .line 16
    .line 17
    if-eqz v0, :cond_3

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    const/16 v1, 0x8

    .line 26
    .line 27
    if-ne v0, v1, :cond_2

    .line 28
    .line 29
    const/4 v0, 0x0

    .line 30
    goto :goto_0

    .line 31
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 32
    .line 33
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->j:I

    .line 38
    .line 39
    add-int/2addr v0, v1

    .line 40
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    div-int/lit8 v1, v1, 0x2

    .line 45
    .line 46
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 47
    .line 48
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    add-int/2addr v2, v0

    .line 53
    div-int/lit8 v2, v2, 0x2

    .line 54
    .line 55
    sub-int v6, v1, v2

    .line 56
    .line 57
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 58
    .line 59
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 60
    .line 61
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 62
    .line 63
    mul-int/lit8 v1, v1, 0x2

    .line 64
    .line 65
    add-int v5, v0, v1

    .line 66
    .line 67
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 72
    .line 73
    sub-int v7, v0, v1

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 76
    .line 77
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    add-int v8, v6, v0

    .line 82
    .line 83
    move-object v3, p0

    .line 84
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 85
    .line 86
    .line 87
    return-void

    .line 88
    :cond_3
    move-object v3, p0

    .line 89
    return-void

    .line 90
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 91
    .line 92
    .line 93
    move-result v0

    .line 94
    div-int/lit8 v0, v0, 0x2

    .line 95
    .line 96
    iget-object v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 97
    .line 98
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    div-int/lit8 v1, v1, 0x2

    .line 103
    .line 104
    sub-int v12, v0, v1

    .line 105
    .line 106
    iget-object v10, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 107
    .line 108
    iget v0, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 109
    .line 110
    iget v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 111
    .line 112
    mul-int/lit8 v1, v1, 0x2

    .line 113
    .line 114
    add-int v11, v0, v1

    .line 115
    .line 116
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 117
    .line 118
    .line 119
    move-result v0

    .line 120
    iget v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 121
    .line 122
    sub-int v13, v0, v1

    .line 123
    .line 124
    iget-object v0, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 125
    .line 126
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    add-int v14, v12, v0

    .line 131
    .line 132
    move-object v9, v3

    .line 133
    invoke-static/range {v9 .. v14}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 134
    .line 135
    .line 136
    return-void
.end method

.method private final o()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 4
    .line 5
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 6
    .line 7
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    add-int v4, v2, v0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 14
    .line 15
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    add-int/2addr v5, v0

    .line 22
    move-object v0, p0

    .line 23
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final q(I)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 12
    .line 13
    mul-int/lit8 v1, v1, 0x3

    .line 14
    .line 15
    sub-int/2addr p1, v1

    .line 16
    const/high16 v1, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    const/4 v1, 0x0

    .line 23
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final r()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->f:I

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->g:I

    .line 13
    .line 14
    :goto_0
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 15
    .line 16
    const/high16 v2, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 23
    .line 24
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    invoke-virtual {v1, v0, v2}, Landroid/view/View;->measure(II)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method private final s(I)V
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->j(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    instance-of v1, v0, Lc31/a$a;

    .line 11
    .line 12
    const/high16 v2, 0x40000000    # 2.0f

    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    if-nez v1, :cond_2

    .line 16
    .line 17
    instance-of v1, v0, Lc31/a$b;

    .line 18
    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    instance-of v0, v0, Lc31/a$c;

    .line 23
    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 29
    .line 30
    sub-int/2addr p1, v1

    .line 31
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 32
    .line 33
    mul-int/lit8 v1, v1, 0x3

    .line 34
    .line 35
    sub-int/2addr p1, v1

    .line 36
    invoke-static {p1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 45
    .line 46
    .line 47
    :cond_1
    return-void

    .line 48
    :cond_2
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 49
    .line 50
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->h:I

    .line 51
    .line 52
    sub-int/2addr p1, v1

    .line 53
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 54
    .line 55
    mul-int/lit8 v1, v1, 0x3

    .line 56
    .line 57
    sub-int/2addr p1, v1

    .line 58
    invoke-static {p1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 63
    .line 64
    .line 65
    move-result v1

    .line 66
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method private final t()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final u(Landroid/widget/TextView;II)V
    .locals 2

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->m:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {p1, p2, p3, v0, v1}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 5
    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final d()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    sget v1, LlZ0/n;->TextStyle_Headline_Medium_TextPrimary:I

    .line 4
    .line 5
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->o:I

    .line 11
    .line 12
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->p:I

    .line 13
    .line 14
    invoke-direct {p0, v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u(Landroid/widget/TextView;II)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final e()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    sget v1, LlZ0/n;->TextStyle_Headline_Medium_StaticWhite:I

    .line 4
    .line 5
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->o:I

    .line 11
    .line 12
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->p:I

    .line 13
    .line 14
    invoke-direct {p0, v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u(Landroid/widget/TextView;II)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final f()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    sget v1, LlZ0/n;->TextStyle_Headline_Bold_Primary:I

    .line 4
    .line 5
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->n:I

    .line 11
    .line 12
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->p:I

    .line 13
    .line 14
    invoke-direct {p0, v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u(Landroid/widget/TextView;II)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public getCardHeight()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->x:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l:I

    .line 4
    .line 5
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 6
    .line 7
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    add-int v4, v2, v0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k:I

    .line 14
    .line 15
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 16
    .line 17
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    add-int/2addr v5, v0

    .line 22
    move-object v0, p0

    .line 23
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->k()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->o()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->m()V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->n()V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->l()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    const/high16 p2, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->getCardHeight()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p2

    .line 22
    invoke-virtual {p0, v0, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->p()V

    .line 26
    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t()V

    .line 29
    .line 30
    .line 31
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->r()V

    .line 32
    .line 33
    .line 34
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s(I)V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->q(I)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final p()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public setCaptionText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->g(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMaxProgress(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->setMaxProgress(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setProgress(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->setProgress(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setStageNumberText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x3

    .line 6
    invoke-static {p1, v1}, Lkotlin/text/A;->X1(Ljava/lang/CharSequence;I)Ljava/lang/CharSequence;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    :goto_0
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public setState(Lc31/a;)V
    .locals 6
    .param p1    # Lc31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setState(Lc31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lc31/a$a;

    .line 5
    .line 6
    const/4 v1, 0x2

    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 12
    .line 13
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->r:Landroid/graphics/drawable/Drawable;

    .line 19
    .line 20
    invoke-virtual {v0, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->d()V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    sget v3, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 29
    .line 30
    invoke-static {v0, v3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 36
    .line 37
    .line 38
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 39
    .line 40
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 41
    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_0
    instance-of v0, p1, Lc31/a$c;

    .line 45
    .line 46
    if-eqz v0, :cond_4

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 49
    .line 50
    const/4 v1, 0x0

    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 55
    .line 56
    const/4 v3, 0x0

    .line 57
    invoke-virtual {v0, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->f()V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 64
    .line 65
    sget v3, LlZ0/n;->TextStyle_Text_Bold_TextPrimary:I

    .line 66
    .line 67
    invoke-static {v0, v3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 68
    .line 69
    .line 70
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 71
    .line 72
    const/4 v3, 0x1

    .line 73
    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 74
    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 77
    .line 78
    move-object v4, p1

    .line 79
    check-cast v4, Lc31/a$c;

    .line 80
    .line 81
    invoke-virtual {v4}, Lc31/a$c;->c()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v5

    .line 85
    if-eqz v5, :cond_2

    .line 86
    .line 87
    invoke-interface {v5}, Ljava/lang/CharSequence;->length()I

    .line 88
    .line 89
    .line 90
    move-result v5

    .line 91
    if-nez v5, :cond_1

    .line 92
    .line 93
    goto :goto_0

    .line 94
    :cond_1
    const/4 v3, 0x0

    .line 95
    :cond_2
    :goto_0
    if-nez v3, :cond_3

    .line 96
    .line 97
    const/4 v2, 0x0

    .line 98
    :cond_3
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 99
    .line 100
    .line 101
    invoke-virtual {v4}, Lc31/a$c;->f()I

    .line 102
    .line 103
    .line 104
    move-result v0

    .line 105
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->setProgress(I)V

    .line 106
    .line 107
    .line 108
    invoke-virtual {v4}, Lc31/a$c;->e()I

    .line 109
    .line 110
    .line 111
    move-result v0

    .line 112
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->setMaxProgress(I)V

    .line 113
    .line 114
    .line 115
    goto :goto_1

    .line 116
    :cond_4
    instance-of v0, p1, Lc31/a$b;

    .line 117
    .line 118
    if-eqz v0, :cond_5

    .line 119
    .line 120
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->s:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;

    .line 121
    .line 122
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 123
    .line 124
    .line 125
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->t:Landroid/view/View;

    .line 126
    .line 127
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->q:Landroid/graphics/drawable/Drawable;

    .line 128
    .line 129
    invoke-virtual {v0, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->e()V

    .line 133
    .line 134
    .line 135
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 136
    .line 137
    sget v3, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 138
    .line 139
    invoke-static {v0, v3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 140
    .line 141
    .line 142
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 143
    .line 144
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 145
    .line 146
    .line 147
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 148
    .line 149
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 150
    .line 151
    .line 152
    :goto_1
    invoke-interface {p1}, Lc31/a;->b()Ljava/lang/String;

    .line 153
    .line 154
    .line 155
    move-result-object v0

    .line 156
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->setStageNumberText(Ljava/lang/CharSequence;)V

    .line 157
    .line 158
    .line 159
    invoke-interface {p1}, Lc31/a;->a()Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v0

    .line 163
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->setTitleText(Ljava/lang/CharSequence;)V

    .line 164
    .line 165
    .line 166
    invoke-interface {p1}, Lc31/a;->c()Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->setCaptionText(Ljava/lang/CharSequence;)V

    .line 171
    .line 172
    .line 173
    return-void

    .line 174
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 175
    .line 176
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 177
    .line 178
    .line 179
    throw p1
.end method

.method public setTitleText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->i(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method
