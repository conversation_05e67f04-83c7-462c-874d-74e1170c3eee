.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a#\u0010\u000c\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a#\u0010\u000e\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\r\u001a#\u0010\u000f\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\r*$\u0008\u0000\u0010\u0010\"\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00072\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007\u00a8\u0006\u0011"
    }
    d2 = {
        "Lox0/a;",
        "tournamentPromotionClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "j",
        "(Lox0/a;)LA4/c;",
        "LB4/a;",
        "Lpx0/a;",
        "LGq0/e1;",
        "Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionsViewHolder;",
        "",
        "i",
        "(LB4/a;)V",
        "h",
        "g",
        "TournamentPromotionsViewHolder",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lox0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->m(Lox0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/e1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/e1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lox0/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->l(Lox0/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic d(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->g(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic e(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->h(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt;->i(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final g(LB4/a;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lpx0/a;",
            "LGq0/e1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lpx0/a;

    .line 6
    .line 7
    invoke-virtual {v0}, Lpx0/a;->e()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lpx0/a;

    .line 22
    .line 23
    invoke-virtual {v0}, Lpx0/a;->o()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    :cond_0
    move-object v3, v0

    .line 28
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, LGq0/e1;

    .line 35
    .line 36
    iget-object v2, p0, LGq0/e1;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 37
    .line 38
    sget v4, Lpb/g;->ic_bonus_promo_sand_clock:I

    .line 39
    .line 40
    const/4 p0, 0x0

    .line 41
    new-array v7, p0, [LYW0/d;

    .line 42
    .line 43
    const/16 v11, 0xec

    .line 44
    .line 45
    const/4 v12, 0x0

    .line 46
    const/4 v5, 0x0

    .line 47
    const/4 v6, 0x0

    .line 48
    const/4 v8, 0x0

    .line 49
    const/4 v9, 0x0

    .line 50
    const/4 v10, 0x0

    .line 51
    invoke-static/range {v1 .. v12}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public static final h(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lpx0/a;",
            "LGq0/e1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/e1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/e1;->c:Lcom/google/android/material/textview/MaterialTextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lpx0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lpx0/a;->f()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final i(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lpx0/a;",
            "LGq0/e1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/e1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/e1;->d:Lcom/google/android/material/textview/MaterialTextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lpx0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lpx0/a;->j()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final j(Lox0/a;)LA4/c;
    .locals 4
    .param p0    # Lox0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lox0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lqx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lqx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lqx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lqx0/b;-><init>(Lox0/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$tournamentPromotionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$tournamentPromotionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$tournamentPromotionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$tournamentPromotionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/e1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/e1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/e1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final l(Lox0/a;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    iget-object v0, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, Lqx0/c;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1}, Lqx0/c;-><init>(Lox0/a;LB4/a;)V

    .line 6
    .line 7
    .line 8
    const/4 p0, 0x1

    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$a;

    .line 14
    .line 15
    invoke-direct {p0, p1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/promotion/viewholder/TournamentPromotionViewHolderKt$a;-><init>(LB4/a;LB4/a;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method

.method public static final m(Lox0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lpx0/a;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-interface {p0, p2, p1}, Lox0/a;->g3(Lpx0/a;I)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
