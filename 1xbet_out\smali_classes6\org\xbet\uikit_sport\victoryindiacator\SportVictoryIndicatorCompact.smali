.class public final Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
.super Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\'\u0010\u0014\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;",
        "Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "index",
        "",
        "win",
        "a",
        "(Landroid/graphics/Canvas;IZ)V",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    sget-object p3, Lm31/g;->SportVictoryIndicatorCompact:[I

    const/4 v0, 0x0

    .line 6
    invoke-virtual {p1, p2, p3, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    .line 7
    sget p2, Lm31/g;->SportVictoryIndicatorCompact_regularSportIndicatorCompact:I

    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setRegularIndicator(Landroid/graphics/drawable/Drawable;)V

    .line 8
    sget p2, Lm31/g;->SportVictoryIndicatorCompact_winSportIndicatorCompact:I

    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setWinIndicator(Landroid/graphics/drawable/Drawable;)V

    .line 9
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_6:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setIndicatorWidth(I)V

    .line 10
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->b()V

    .line 11
    sget p2, Lm31/g;->SportVictoryIndicatorCompact_winColorSportIndicatorCompact:I

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/xbet/uikit/utils/I;->b(Landroid/content/res/TypedArray;Ljava/lang/Integer;)Ljava/lang/Integer;

    move-result-object p2

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    .line 12
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setWinIndicatorColor(I)V

    .line 13
    :cond_0
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public a(Landroid/graphics/Canvas;IZ)V
    .locals 5
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getTotalCount()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    mul-int v0, v0, v1

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getTotalCount()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    const/4 v2, 0x1

    .line 16
    sub-int/2addr v1, v2

    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    mul-int v1, v1, v3

    .line 22
    .line 23
    add-int/2addr v0, v1

    .line 24
    if-nez p2, :cond_0

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    if-ne v3, v2, :cond_1

    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 43
    .line 44
    .line 45
    move-result v4

    .line 46
    sub-int/2addr v4, v0

    .line 47
    div-int/lit8 v4, v4, 0x2

    .line 48
    .line 49
    sub-int/2addr v3, v4

    .line 50
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    sub-int/2addr v3, v0

    .line 55
    goto :goto_1

    .line 56
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    sub-int/2addr v3, v0

    .line 61
    div-int/lit8 v3, v3, 0x2

    .line 62
    .line 63
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    if-ne v0, v2, :cond_2

    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    add-int/2addr v0, v1

    .line 74
    mul-int p2, p2, v0

    .line 75
    .line 76
    sub-int/2addr v3, p2

    .line 77
    goto :goto_2

    .line 78
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    add-int/2addr v0, v1

    .line 83
    mul-int p2, p2, v0

    .line 84
    .line 85
    add-int/2addr v3, p2

    .line 86
    :goto_2
    int-to-float p2, v3

    .line 87
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 88
    .line 89
    .line 90
    move-result v0

    .line 91
    const/4 v1, 0x0

    .line 92
    invoke-virtual {p1, p2, v1}, Landroid/graphics/Canvas;->translate(FF)V

    .line 93
    .line 94
    .line 95
    if-eqz p3, :cond_3

    .line 96
    .line 97
    :try_start_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getWinIndicator()Landroid/graphics/drawable/Drawable;

    .line 98
    .line 99
    .line 100
    move-result-object p2

    .line 101
    if-eqz p2, :cond_4

    .line 102
    .line 103
    invoke-virtual {p2, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 104
    .line 105
    .line 106
    goto :goto_3

    .line 107
    :catchall_0
    move-exception p2

    .line 108
    goto :goto_4

    .line 109
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getRegularIndicator()Landroid/graphics/drawable/Drawable;

    .line 110
    .line 111
    .line 112
    move-result-object p2

    .line 113
    if-eqz p2, :cond_4

    .line 114
    .line 115
    invoke-virtual {p2, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 116
    .line 117
    .line 118
    :cond_4
    :goto_3
    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 119
    .line 120
    .line 121
    return-void

    .line 122
    :goto_4
    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 123
    .line 124
    .line 125
    throw p2
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getTotalCount()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    mul-int p1, p1, p2

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getSpaceCount()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    mul-int p2, p2, v0

    .line 20
    .line 21
    add-int/2addr p1, p2

    .line 22
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorHeight()I

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 27
    .line 28
    .line 29
    return-void
.end method
