.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameViewModel$play$2"
    f = "SpinAndWinGameViewModel.kt"
    l = {
        0x12e,
        0x136,
        0x13a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;

    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x1

    .line 9
    const/4 v4, 0x2

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v3, :cond_2

    .line 13
    .line 14
    if-eq v1, v4, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto/16 :goto_4

    .line 22
    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto/16 :goto_2

    .line 35
    .line 36
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 44
    .line 45
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->G3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/f;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    invoke-virtual {p1}, Lez0/f;->a()Lkotlinx/coroutines/flow/e;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput v3, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->label:I

    .line 59
    .line 60
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    if-ne p1, v0, :cond_4

    .line 65
    .line 66
    goto/16 :goto_3

    .line 67
    .line 68
    :cond_4
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 69
    .line 70
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 71
    .line 72
    .line 73
    move-result v1

    .line 74
    if-eqz v1, :cond_5

    .line 75
    .line 76
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 77
    .line 78
    sget-object v0, Lorg/xbet/spin_and_win/presentation/game/c$a;->a:Lorg/xbet/spin_and_win/presentation/game/c$a;

    .line 79
    .line 80
    invoke-static {p1, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->Y3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/c;)V

    .line 81
    .line 82
    .line 83
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 84
    .line 85
    return-object p1

    .line 86
    :cond_5
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 87
    .line 88
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->P3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/V;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    :cond_6
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    move-object v5, v3

    .line 97
    check-cast v5, Lorg/xbet/spin_and_win/presentation/game/q;

    .line 98
    .line 99
    const/4 v6, 0x0

    .line 100
    const/4 v7, 0x0

    .line 101
    invoke-static {v5, v7, v7, v4, v6}, Lorg/xbet/spin_and_win/presentation/game/q;->b(Lorg/xbet/spin_and_win/presentation/game/q;ZZILjava/lang/Object;)Lorg/xbet/spin_and_win/presentation/game/q;

    .line 102
    .line 103
    .line 104
    move-result-object v5

    .line 105
    invoke-interface {v1, v3, v5}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 106
    .line 107
    .line 108
    move-result v3

    .line 109
    if-eqz v3, :cond_6

    .line 110
    .line 111
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 112
    .line 113
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 114
    .line 115
    invoke-direct {v3, v7}, Lorg/xbet/spin_and_win/presentation/game/a$b;-><init>(Z)V

    .line 116
    .line 117
    .line 118
    invoke-static {v1, v3}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->W3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 119
    .line 120
    .line 121
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 122
    .line 123
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/b$d;

    .line 124
    .line 125
    invoke-direct {v3, v7}, Lorg/xbet/spin_and_win/presentation/game/b$d;-><init>(Z)V

    .line 126
    .line 127
    .line 128
    invoke-static {v1, v3}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 129
    .line 130
    .line 131
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 132
    .line 133
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->K3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/g;

    .line 134
    .line 135
    .line 136
    move-result-object v1

    .line 137
    new-instance v3, Ljava/util/ArrayList;

    .line 138
    .line 139
    const/16 v5, 0xa

    .line 140
    .line 141
    invoke-static {p1, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 142
    .line 143
    .line 144
    move-result v5

    .line 145
    invoke-direct {v3, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 146
    .line 147
    .line 148
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 153
    .line 154
    .line 155
    move-result v5

    .line 156
    if-eqz v5, :cond_7

    .line 157
    .line 158
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object v5

    .line 162
    check-cast v5, Ldz0/a;

    .line 163
    .line 164
    new-instance v6, LZy0/a;

    .line 165
    .line 166
    invoke-virtual {v5}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 167
    .line 168
    .line 169
    move-result-object v7

    .line 170
    invoke-static {v7}, Lgz0/a;->b(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)I

    .line 171
    .line 172
    .line 173
    move-result v7

    .line 174
    invoke-virtual {v5}, Ldz0/a;->c()D

    .line 175
    .line 176
    .line 177
    move-result-wide v8

    .line 178
    invoke-direct {v6, v7, v8, v9}, LZy0/a;-><init>(ID)V

    .line 179
    .line 180
    .line 181
    invoke-interface {v3, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 182
    .line 183
    .line 184
    goto :goto_1

    .line 185
    :cond_7
    iput v4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->label:I

    .line 186
    .line 187
    invoke-virtual {v1, v3, p0}, Lez0/g;->b(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 188
    .line 189
    .line 190
    move-result-object p1

    .line 191
    if-ne p1, v0, :cond_8

    .line 192
    .line 193
    goto :goto_3

    .line 194
    :cond_8
    :goto_2
    check-cast p1, Ldz0/b;

    .line 195
    .line 196
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 197
    .line 198
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->M3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/j;

    .line 199
    .line 200
    .line 201
    move-result-object v1

    .line 202
    invoke-virtual {v1, p1}, Lez0/j;->a(Ldz0/b;)V

    .line 203
    .line 204
    .line 205
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 206
    .line 207
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->w3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 208
    .line 209
    .line 210
    move-result-object p1

    .line 211
    sget-object v1, LTv/a$k;->a:LTv/a$k;

    .line 212
    .line 213
    iput v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->label:I

    .line 214
    .line 215
    invoke-virtual {p1, v1, p0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 216
    .line 217
    .line 218
    move-result-object p1

    .line 219
    if-ne p1, v0, :cond_9

    .line 220
    .line 221
    :goto_3
    return-object v0

    .line 222
    :cond_9
    :goto_4
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 223
    .line 224
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->a4(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)V

    .line 225
    .line 226
    .line 227
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 228
    .line 229
    return-object p1
.end method
