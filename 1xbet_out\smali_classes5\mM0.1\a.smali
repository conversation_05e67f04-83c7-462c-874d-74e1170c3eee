.class public final synthetic LmM0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:I

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(IZ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, LmM0/a;->a:I

    iput-boolean p2, p0, LmM0/a;->b:Z

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget v0, p0, LmM0/a;->a:I

    iget-boolean v1, p0, LmM0/a;->b:Z

    check-cast p1, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketItemView;

    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketItemView;->e(IZLorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketItemView;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
