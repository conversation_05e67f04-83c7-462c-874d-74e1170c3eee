.class public final LqC0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LqC0/f;


# instance fields
.field public final a:Lorg/xbet/sportgame/subgames/impl/presentation/j;


# direct methods
.method public constructor <init>(Lorg/xbet/sportgame/subgames/impl/presentation/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LqC0/g;->a:Lorg/xbet/sportgame/subgames/impl/presentation/j;

    .line 5
    .line 6
    return-void
.end method

.method public static c(Lorg/xbet/sportgame/subgames/impl/presentation/j;)Ldagger/internal/h;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/sportgame/subgames/impl/presentation/j;",
            ")",
            "Ldagger/internal/h<",
            "LqC0/f;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, LqC0/g;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LqC0/g;-><init>(Lorg/xbet/sportgame/subgames/impl/presentation/j;)V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method


# virtual methods
.method public bridge synthetic a(Landroidx/lifecycle/Q;)Landroidx/lifecycle/b0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LqC0/g;->b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, LqC0/g;->a:Lorg/xbet/sportgame/subgames/impl/presentation/j;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/subgames/impl/presentation/j;->b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
