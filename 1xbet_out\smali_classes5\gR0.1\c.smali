.class public final LgR0/c;
.super Ljava/lang/Object;


# static fields
.field public static clHeader:I = 0x7f0a04a7

.field public static clHorsesRace:I = 0x7f0a04ab

.field public static emptyView:I = 0x7f0a0710

.field public static flContentContainer:I = 0x7f0a0878

.field public static groupContent:I = 0x7f0a099c

.field public static guideline:I = 0x7f0a0a05

.field public static guideline1:I = 0x7f0a0a06

.field public static guideline2:I = 0x7f0a0a07

.field public static guideline3:I = 0x7f0a0a08

.field public static guideline4:I = 0x7f0a0a09

.field public static image:I = 0x7f0a0ae2

.field public static ivGameBackground:I = 0x7f0a0c74

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static panelView:I = 0x7f0a1049

.field public static position:I = 0x7f0a10e4

.field public static rvContent:I = 0x7f0a126b

.field public static rvMenu:I = 0x7f0a128c

.field public static separator:I = 0x7f0a13c7

.field public static shimmer:I = 0x7f0a1400

.field public static shimmerHorsesMenu:I = 0x7f0a143c

.field public static title:I = 0x7f0a1808

.field public static toolbar:I = 0x7f0a183e

.field public static tvChampName:I = 0x7f0a19a4

.field public static tvEventTime:I = 0x7f0a1a50

.field public static tvLying:I = 0x7f0a1b0a

.field public static tvSeason:I = 0x7f0a1bf7

.field public static tvSection:I = 0x7f0a1c2a

.field public static tvShooting:I = 0x7f0a1c35

.field public static tvSkiing:I = 0x7f0a1c3e

.field public static tvStanding:I = 0x7f0a1c4f

.field public static view1:I = 0x7f0a1f0b

.field public static view2:I = 0x7f0a1f16

.field public static viewBackground:I = 0x7f0a1f2a

.field public static viewPoint1:I = 0x7f0a1f67

.field public static viewPoint2:I = 0x7f0a1f68

.field public static viewRow1:I = 0x7f0a1f6c

.field public static viewRow2:I = 0x7f0a1f6d

.field public static viewRowTitle1:I = 0x7f0a1f70

.field public static viewRowTitle2:I = 0x7f0a1f72

.field public static viewShadow:I = 0x7f0a1f7c


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
