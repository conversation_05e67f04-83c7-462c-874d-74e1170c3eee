.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "(LSX0/c;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LSX0/c;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt;->f(LSX0/c;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;LSX0/c;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt;->g(LB4/a;LSX0/c;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/r0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/r0;

    move-result-object p0

    return-object p0
.end method

.method public static final d(LSX0/c;)LA4/c;
    .locals 4
    .param p0    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LSX0/c;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lba1/b;-><init>(LSX0/c;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt$aggregatorNoGiftsDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt$aggregatorNoGiftsDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt$aggregatorNoGiftsDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AggregatorNoGiftsDelegateKt$aggregatorNoGiftsDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/r0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/r0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/r0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LSX0/c;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lba1/c;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0}, Lba1/c;-><init>(LB4/a;LSX0/c;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final g(LB4/a;LSX0/c;Ljava/util/List;)Lkotlin/Unit;
    .locals 12

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, LS91/r0;

    .line 6
    .line 7
    iget-object p2, p2, LS91/r0;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 8
    .line 9
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->AGGREGATOR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, Lma1/f;

    .line 16
    .line 17
    invoke-virtual {p0}, Lma1/f;->d()I

    .line 18
    .line 19
    .line 20
    move-result v6

    .line 21
    const/16 v10, 0x1de

    .line 22
    .line 23
    const/4 v11, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    const/4 v9, 0x0

    .line 31
    move-object v0, p1

    .line 32
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p2, p0}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 37
    .line 38
    .line 39
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 40
    .line 41
    return-object p0
.end method
