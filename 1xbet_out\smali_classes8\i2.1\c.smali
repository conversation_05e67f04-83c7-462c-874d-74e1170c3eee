.class public final synthetic Li2/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/x;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Lk2/s$a;)LN1/x;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LN1/w;->d(LN1/x;Lk2/s$a;)LN1/x;

    move-result-object p1

    return-object p1
.end method

.method public synthetic b(I)LN1/x;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LN1/w;->b(LN1/x;I)LN1/x;

    move-result-object p1

    return-object p1
.end method

.method public synthetic c(Z)LN1/x;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LN1/w;->c(LN1/x;Z)LN1/x;

    move-result-object p1

    return-object p1
.end method

.method public synthetic d(Landroid/net/Uri;Ljava/util/Map;)[LN1/r;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LN1/w;->a(LN1/x;Landroid/net/Uri;Ljava/util/Map;)[LN1/r;

    move-result-object p1

    return-object p1
.end method

.method public final e()[LN1/r;
    .locals 1

    .line 1
    invoke-static {}, Li2/d;->c()[LN1/r;

    move-result-object v0

    return-object v0
.end method
