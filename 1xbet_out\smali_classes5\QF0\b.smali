.class public final LQF0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LRF0/b;",
        "LTF0/b;",
        "a",
        "(LRF0/b;)LTF0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LRF0/b;)LTF0/b;
    .locals 9
    .param p0    # LRF0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LRF0/b;->a()Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    :goto_0
    move-wide v3, v0

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    const-wide/16 v0, 0x0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :goto_1
    invoke-virtual {p0}, LRF0/b;->b()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v5

    .line 20
    const/4 v0, 0x0

    .line 21
    if-eqz v5, :cond_6

    .line 22
    .line 23
    invoke-virtual {p0}, LRF0/b;->c()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    if-eqz v1, :cond_1

    .line 28
    .line 29
    new-instance v0, Ljava/util/ArrayList;

    .line 30
    .line 31
    const/16 v2, 0xa

    .line 32
    .line 33
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 38
    .line 39
    .line 40
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-eqz v2, :cond_1

    .line 49
    .line 50
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    check-cast v2, LCN0/f;

    .line 55
    .line 56
    invoke-static {v2}, LBN0/f;->a(LCN0/f;)LND0/e;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 61
    .line 62
    .line 63
    goto :goto_2

    .line 64
    :cond_1
    if-nez v0, :cond_2

    .line 65
    .line 66
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    :cond_2
    move-object v6, v0

    .line 71
    invoke-virtual {p0}, LRF0/b;->d()Ljava/lang/Integer;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    if-eqz v0, :cond_4

    .line 76
    .line 77
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    sget-object v1, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 82
    .line 83
    invoke-virtual {v1, v0}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    if-nez v0, :cond_3

    .line 88
    .line 89
    goto :goto_4

    .line 90
    :cond_3
    :goto_3
    move-object v7, v0

    .line 91
    goto :goto_5

    .line 92
    :cond_4
    :goto_4
    sget-object v0, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->UNKNOWN:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 93
    .line 94
    goto :goto_3

    .line 95
    :goto_5
    invoke-virtual {p0}, LRF0/b;->e()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object p0

    .line 99
    if-nez p0, :cond_5

    .line 100
    .line 101
    const-string p0, ""

    .line 102
    .line 103
    :cond_5
    move-object v8, p0

    .line 104
    new-instance v2, LTF0/b;

    .line 105
    .line 106
    invoke-direct/range {v2 .. v8}, LTF0/b;-><init>(JLjava/lang/String;Ljava/util/List;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;Ljava/lang/String;)V

    .line 107
    .line 108
    .line 109
    return-object v2

    .line 110
    :cond_6
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 111
    .line 112
    const/4 v1, 0x1

    .line 113
    invoke-direct {p0, v0, v1, v0}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 114
    .line 115
    .line 116
    throw p0
.end method
