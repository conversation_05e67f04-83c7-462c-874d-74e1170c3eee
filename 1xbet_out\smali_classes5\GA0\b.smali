.class public final LGA0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001d\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0008\u0010\u0002\u001a\u0004\u0018\u00010\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LJA0/b;",
        "",
        "serve",
        "LWA0/e;",
        "a",
        "(LJA0/b;Ljava/lang/Integer;)LWA0/e;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LJA0/b;Ljava/lang/Integer;)LWA0/e;
    .locals 9
    .param p0    # LJA0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Ljava/util/ArrayList;

    .line 7
    .line 8
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LJA0/b;->b()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    const/4 v3, 0x2

    .line 16
    const/4 v4, 0x1

    .line 17
    if-eqz v2, :cond_4

    .line 18
    .line 19
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v5

    .line 27
    if-eqz v5, :cond_4

    .line 28
    .line 29
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    check-cast v5, LJA0/b$b;

    .line 34
    .line 35
    invoke-virtual {v5}, LJA0/b$b;->a()Ljava/lang/Integer;

    .line 36
    .line 37
    .line 38
    move-result-object v6

    .line 39
    if-nez v6, :cond_1

    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v7

    .line 46
    if-ne v7, v4, :cond_2

    .line 47
    .line 48
    invoke-static {v5}, LGA0/c;->a(LJA0/b$b;)LWA0/d;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    invoke-interface {v0, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_2
    :goto_1
    if-nez v6, :cond_3

    .line 57
    .line 58
    goto :goto_0

    .line 59
    :cond_3
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 60
    .line 61
    .line 62
    move-result v6

    .line 63
    if-ne v6, v3, :cond_0

    .line 64
    .line 65
    invoke-static {v5}, LGA0/c;->a(LJA0/b$b;)LWA0/d;

    .line 66
    .line 67
    .line 68
    move-result-object v5

    .line 69
    invoke-interface {v1, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    goto :goto_0

    .line 73
    :cond_4
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 74
    .line 75
    .line 76
    move-result v2

    .line 77
    const/4 v5, 0x5

    .line 78
    const/4 v6, 0x0

    .line 79
    if-ge v2, v5, :cond_5

    .line 80
    .line 81
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 82
    .line 83
    .line 84
    move-result v2

    .line 85
    rsub-int/lit8 v2, v2, 0x5

    .line 86
    .line 87
    const/4 v7, 0x0

    .line 88
    :goto_2
    if-ge v7, v2, :cond_5

    .line 89
    .line 90
    new-instance v8, LWA0/d$a;

    .line 91
    .line 92
    invoke-direct {v8, v6}, LWA0/d$a;-><init>(Z)V

    .line 93
    .line 94
    .line 95
    invoke-interface {v0, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    add-int/lit8 v7, v7, 0x1

    .line 99
    .line 100
    goto :goto_2

    .line 101
    :cond_5
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 102
    .line 103
    .line 104
    move-result v2

    .line 105
    if-ge v2, v5, :cond_6

    .line 106
    .line 107
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 108
    .line 109
    .line 110
    move-result v2

    .line 111
    sub-int/2addr v5, v2

    .line 112
    const/4 v2, 0x0

    .line 113
    :goto_3
    if-ge v2, v5, :cond_6

    .line 114
    .line 115
    new-instance v7, LWA0/d$a;

    .line 116
    .line 117
    invoke-direct {v7, v6}, LWA0/d$a;-><init>(Z)V

    .line 118
    .line 119
    .line 120
    invoke-interface {v1, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 121
    .line 122
    .line 123
    add-int/lit8 v2, v2, 0x1

    .line 124
    .line 125
    goto :goto_3

    .line 126
    :cond_6
    const/4 v2, -0x1

    .line 127
    if-nez p1, :cond_7

    .line 128
    .line 129
    goto :goto_6

    .line 130
    :cond_7
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 131
    .line 132
    .line 133
    move-result v5

    .line 134
    if-ne v5, v4, :cond_b

    .line 135
    .line 136
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    const/4 v3, 0x0

    .line 141
    :goto_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 142
    .line 143
    .line 144
    move-result v5

    .line 145
    if-eqz v5, :cond_9

    .line 146
    .line 147
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object v5

    .line 151
    check-cast v5, LWA0/d;

    .line 152
    .line 153
    instance-of v5, v5, LWA0/d$a;

    .line 154
    .line 155
    if-eqz v5, :cond_8

    .line 156
    .line 157
    goto :goto_5

    .line 158
    :cond_8
    add-int/lit8 v3, v3, 0x1

    .line 159
    .line 160
    goto :goto_4

    .line 161
    :cond_9
    const/4 v3, -0x1

    .line 162
    :goto_5
    if-ne v3, v2, :cond_a

    .line 163
    .line 164
    new-instance p1, LWA0/d$a;

    .line 165
    .line 166
    invoke-direct {p1, v4}, LWA0/d$a;-><init>(Z)V

    .line 167
    .line 168
    .line 169
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    goto :goto_9

    .line 173
    :cond_a
    invoke-interface {v0, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 174
    .line 175
    .line 176
    new-instance p1, LWA0/d$a;

    .line 177
    .line 178
    invoke-direct {p1, v4}, LWA0/d$a;-><init>(Z)V

    .line 179
    .line 180
    .line 181
    invoke-interface {v0, v3, p1}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 182
    .line 183
    .line 184
    goto :goto_9

    .line 185
    :cond_b
    :goto_6
    if-nez p1, :cond_c

    .line 186
    .line 187
    goto :goto_9

    .line 188
    :cond_c
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 189
    .line 190
    .line 191
    move-result p1

    .line 192
    if-ne p1, v3, :cond_10

    .line 193
    .line 194
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 195
    .line 196
    .line 197
    move-result-object p1

    .line 198
    const/4 v3, 0x0

    .line 199
    :goto_7
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 200
    .line 201
    .line 202
    move-result v5

    .line 203
    if-eqz v5, :cond_e

    .line 204
    .line 205
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v5

    .line 209
    check-cast v5, LWA0/d;

    .line 210
    .line 211
    instance-of v5, v5, LWA0/d$a;

    .line 212
    .line 213
    if-eqz v5, :cond_d

    .line 214
    .line 215
    goto :goto_8

    .line 216
    :cond_d
    add-int/lit8 v3, v3, 0x1

    .line 217
    .line 218
    goto :goto_7

    .line 219
    :cond_e
    const/4 v3, -0x1

    .line 220
    :goto_8
    if-ne v3, v2, :cond_f

    .line 221
    .line 222
    new-instance p1, LWA0/d$a;

    .line 223
    .line 224
    invoke-direct {p1, v4}, LWA0/d$a;-><init>(Z)V

    .line 225
    .line 226
    .line 227
    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 228
    .line 229
    .line 230
    goto :goto_9

    .line 231
    :cond_f
    invoke-interface {v1, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 232
    .line 233
    .line 234
    new-instance p1, LWA0/d$a;

    .line 235
    .line 236
    invoke-direct {p1, v4}, LWA0/d$a;-><init>(Z)V

    .line 237
    .line 238
    .line 239
    invoke-interface {v1, v3, p1}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 240
    .line 241
    .line 242
    :cond_10
    :goto_9
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 243
    .line 244
    .line 245
    move-result p1

    .line 246
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 247
    .line 248
    .line 249
    move-result v2

    .line 250
    if-ge p1, v2, :cond_11

    .line 251
    .line 252
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 253
    .line 254
    .line 255
    move-result p1

    .line 256
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 257
    .line 258
    .line 259
    move-result v2

    .line 260
    sub-int/2addr p1, v2

    .line 261
    const/4 v2, 0x0

    .line 262
    :goto_a
    if-ge v2, p1, :cond_11

    .line 263
    .line 264
    new-instance v3, LWA0/d$a;

    .line 265
    .line 266
    invoke-direct {v3, v6}, LWA0/d$a;-><init>(Z)V

    .line 267
    .line 268
    .line 269
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 270
    .line 271
    .line 272
    add-int/lit8 v2, v2, 0x1

    .line 273
    .line 274
    goto :goto_a

    .line 275
    :cond_11
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 276
    .line 277
    .line 278
    move-result p1

    .line 279
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 280
    .line 281
    .line 282
    move-result v2

    .line 283
    if-ge p1, v2, :cond_12

    .line 284
    .line 285
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 286
    .line 287
    .line 288
    move-result p1

    .line 289
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 290
    .line 291
    .line 292
    move-result v2

    .line 293
    sub-int/2addr p1, v2

    .line 294
    const/4 v2, 0x0

    .line 295
    :goto_b
    if-ge v2, p1, :cond_12

    .line 296
    .line 297
    new-instance v3, LWA0/d$a;

    .line 298
    .line 299
    invoke-direct {v3, v6}, LWA0/d$a;-><init>(Z)V

    .line 300
    .line 301
    .line 302
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 303
    .line 304
    .line 305
    add-int/lit8 v2, v2, 0x1

    .line 306
    .line 307
    goto :goto_b

    .line 308
    :cond_12
    new-instance p1, LWA0/e;

    .line 309
    .line 310
    invoke-virtual {p0}, LJA0/b;->a()LJA0/b$a;

    .line 311
    .line 312
    .line 313
    move-result-object v2

    .line 314
    if-eqz v2, :cond_13

    .line 315
    .line 316
    invoke-virtual {v2}, LJA0/b$a;->a()Ljava/lang/Integer;

    .line 317
    .line 318
    .line 319
    move-result-object v2

    .line 320
    if-eqz v2, :cond_13

    .line 321
    .line 322
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 323
    .line 324
    .line 325
    move-result v2

    .line 326
    goto :goto_c

    .line 327
    :cond_13
    const/4 v2, 0x0

    .line 328
    :goto_c
    invoke-virtual {p0}, LJA0/b;->a()LJA0/b$a;

    .line 329
    .line 330
    .line 331
    move-result-object p0

    .line 332
    if-eqz p0, :cond_14

    .line 333
    .line 334
    invoke-virtual {p0}, LJA0/b$a;->b()Ljava/lang/Integer;

    .line 335
    .line 336
    .line 337
    move-result-object p0

    .line 338
    if-eqz p0, :cond_14

    .line 339
    .line 340
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 341
    .line 342
    .line 343
    move-result v6

    .line 344
    :cond_14
    invoke-direct {p1, v2, v6, v0, v1}, LWA0/e;-><init>(IILjava/util/List;Ljava/util/List;)V

    .line 345
    .line 346
    .line 347
    return-object p1
.end method
