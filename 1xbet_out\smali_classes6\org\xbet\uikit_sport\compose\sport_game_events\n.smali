.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Z

.field public final synthetic c:Z

.field public final synthetic d:Ljava/lang/String;

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(ZZZLjava/lang/String;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->a:Z

    iput-boolean p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->b:Z

    iput-boolean p3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->c:Z

    iput-object p4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->d:Ljava/lang/String;

    iput p5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->a:Z

    iget-boolean v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->b:Z

    iget-boolean v2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->c:Z

    iget-object v3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->d:Ljava/lang/String;

    iget v4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->l(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
