.class public final LFb1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a\u0013\u0010\n\u001a\u00020\t*\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a\u001b\u0010\u000e\u001a\u00020\r*\u00020\u000c2\u0006\u0010\u0003\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a\u001b\u0010\u0011\u001a\u00020\r*\u00020\u00102\u0006\u0010\u0004\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001a\u0013\u0010\u0015\u001a\u00020\u0014*\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016\u001a\u0013\u0010\u0019\u001a\u00020\u0018*\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "LGb1/c;",
        "",
        "brandsApi",
        "hasProvidersAggregator",
        "hasAggregatorBrandsFullInfo",
        "LBb1/a;",
        "d",
        "(LGb1/c;ZZZ)LBb1/a;",
        "LGb1/d;",
        "LBb1/b;",
        "e",
        "(LGb1/d;)LBb1/b;",
        "LGb1/e;",
        "LBb1/c;",
        "f",
        "(LGb1/e;Z)LBb1/c;",
        "LGb1/b;",
        "c",
        "(LGb1/b;Z)LBb1/c;",
        "LGb1/a;",
        "Lorg/xplatform/aggregator/api/model/PartitionBrandModel;",
        "b",
        "(LGb1/a;)Lorg/xplatform/aggregator/api/model/PartitionBrandModel;",
        "",
        "Lorg/xplatform/aggregator/api/model/BrandType;",
        "a",
        "(I)Lorg/xplatform/aggregator/api/model/BrandType;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(I)Lorg/xplatform/aggregator/api/model/BrandType;
    .locals 1

    .line 1
    if-eqz p0, :cond_1

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-eq p0, v0, :cond_0

    .line 5
    .line 6
    sget-object p0, Lorg/xplatform/aggregator/api/model/BrandType;->NOT_CONTRACTED:Lorg/xplatform/aggregator/api/model/BrandType;

    .line 7
    .line 8
    return-object p0

    .line 9
    :cond_0
    sget-object p0, Lorg/xplatform/aggregator/api/model/BrandType;->CONTRACTED:Lorg/xplatform/aggregator/api/model/BrandType;

    .line 10
    .line 11
    return-object p0

    .line 12
    :cond_1
    sget-object p0, Lorg/xplatform/aggregator/api/model/BrandType;->NOT_CONTRACTED:Lorg/xplatform/aggregator/api/model/BrandType;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final b(LGb1/a;)Lorg/xplatform/aggregator/api/model/PartitionBrandModel;
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionBrandModel;

    .line 2
    .line 3
    invoke-virtual {p0}, LGb1/a;->a()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-virtual {p0}, LGb1/a;->b()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, v2, p0}, Lorg/xplatform/aggregator/api/model/PartitionBrandModel;-><init>(JLjava/lang/String;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static final c(LGb1/b;Z)LBb1/c;
    .locals 13
    .param p0    # LGb1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGb1/b;->d()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    sget-object p0, LBb1/c;->k:LBb1/c$a;

    .line 8
    .line 9
    invoke-virtual {p0}, LBb1/c$a;->a()LBb1/c;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    return-object p0

    .line 14
    :cond_0
    invoke-virtual {p0}, LGb1/b;->f()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const-string v1, ""

    .line 19
    .line 20
    if-nez v0, :cond_1

    .line 21
    .line 22
    move-object v3, v1

    .line 23
    goto :goto_0

    .line 24
    :cond_1
    move-object v3, v0

    .line 25
    :goto_0
    invoke-virtual {p0}, LGb1/b;->e()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    if-nez v0, :cond_2

    .line 30
    .line 31
    move-object v4, v1

    .line 32
    goto :goto_1

    .line 33
    :cond_2
    move-object v4, v0

    .line 34
    :goto_1
    invoke-virtual {p0}, LGb1/b;->g()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    if-nez v0, :cond_3

    .line 39
    .line 40
    move-object v5, v1

    .line 41
    goto :goto_2

    .line 42
    :cond_3
    move-object v5, v0

    .line 43
    :goto_2
    invoke-virtual {p0}, LGb1/b;->a()Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    const/4 v2, 0x1

    .line 48
    const/4 v6, 0x0

    .line 49
    if-eqz v0, :cond_d

    .line 50
    .line 51
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    invoke-virtual {p0}, LGb1/b;->b()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object v7

    .line 59
    if-nez v7, :cond_4

    .line 60
    .line 61
    move-object v7, v1

    .line 62
    :cond_4
    invoke-virtual {p0}, LGb1/b;->i()Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v8

    .line 66
    if-eqz v8, :cond_6

    .line 67
    .line 68
    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    .line 69
    .line 70
    .line 71
    move-result v8

    .line 72
    invoke-static {v8}, LFb1/a;->a(I)Lorg/xplatform/aggregator/api/model/BrandType;

    .line 73
    .line 74
    .line 75
    move-result-object v8

    .line 76
    if-nez v8, :cond_5

    .line 77
    .line 78
    goto :goto_4

    .line 79
    :cond_5
    :goto_3
    move-object v11, v8

    .line 80
    goto :goto_5

    .line 81
    :cond_6
    :goto_4
    sget-object v8, Lorg/xplatform/aggregator/api/model/BrandType;->NOT_CONTRACTED:Lorg/xplatform/aggregator/api/model/BrandType;

    .line 82
    .line 83
    goto :goto_3

    .line 84
    :goto_5
    if-nez p1, :cond_9

    .line 85
    .line 86
    invoke-virtual {p0}, LGb1/b;->i()Ljava/lang/Integer;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-nez p1, :cond_7

    .line 91
    .line 92
    goto :goto_6

    .line 93
    :cond_7
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 94
    .line 95
    .line 96
    move-result p1

    .line 97
    if-ne p1, v2, :cond_8

    .line 98
    .line 99
    goto :goto_8

    .line 100
    :cond_8
    :goto_6
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    :goto_7
    move-object v12, p1

    .line 105
    goto :goto_a

    .line 106
    :cond_9
    :goto_8
    invoke-virtual {p0}, LGb1/b;->h()Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    if-eqz p1, :cond_a

    .line 111
    .line 112
    new-instance v6, Ljava/util/ArrayList;

    .line 113
    .line 114
    const/16 v2, 0xa

    .line 115
    .line 116
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 117
    .line 118
    .line 119
    move-result v2

    .line 120
    invoke-direct {v6, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 121
    .line 122
    .line 123
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    :goto_9
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 128
    .line 129
    .line 130
    move-result v2

    .line 131
    if-eqz v2, :cond_a

    .line 132
    .line 133
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 134
    .line 135
    .line 136
    move-result-object v2

    .line 137
    check-cast v2, LGb1/a;

    .line 138
    .line 139
    invoke-static {v2}, LFb1/a;->b(LGb1/a;)Lorg/xplatform/aggregator/api/model/PartitionBrandModel;

    .line 140
    .line 141
    .line 142
    move-result-object v2

    .line 143
    invoke-interface {v6, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 144
    .line 145
    .line 146
    goto :goto_9

    .line 147
    :cond_a
    if-nez v6, :cond_b

    .line 148
    .line 149
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    goto :goto_7

    .line 154
    :cond_b
    move-object v12, v6

    .line 155
    :goto_a
    invoke-virtual {p0}, LGb1/b;->c()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object p0

    .line 159
    if-nez p0, :cond_c

    .line 160
    .line 161
    move-object v10, v1

    .line 162
    goto :goto_b

    .line 163
    :cond_c
    move-object v10, p0

    .line 164
    :goto_b
    new-instance v2, LBb1/c;

    .line 165
    .line 166
    const/4 v8, 0x0

    .line 167
    const/4 v9, 0x1

    .line 168
    move v6, v0

    .line 169
    invoke-direct/range {v2 .. v12}, LBb1/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;IZLjava/lang/String;Lorg/xplatform/aggregator/api/model/BrandType;Ljava/util/List;)V

    .line 170
    .line 171
    .line 172
    return-object v2

    .line 173
    :cond_d
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 174
    .line 175
    invoke-direct {p0, v6, v2, v6}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 176
    .line 177
    .line 178
    throw p0
.end method

.method public static final d(LGb1/c;ZZZ)LBb1/a;
    .locals 3
    .param p0    # LGb1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LBb1/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LGb1/c;->d()LGb1/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-static {v1}, LFb1/a;->e(LGb1/d;)LBb1/b;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    if-nez v1, :cond_1

    .line 14
    .line 15
    :cond_0
    sget-object v1, LBb1/b;->k:LBb1/b$a;

    .line 16
    .line 17
    invoke-virtual {v1}, LBb1/b$a;->a()LBb1/b;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    :cond_1
    invoke-virtual {p0}, LGb1/c;->b()LGb1/d;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    if-eqz v2, :cond_2

    .line 26
    .line 27
    invoke-static {v2}, LFb1/a;->e(LGb1/d;)LBb1/b;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    if-nez v2, :cond_3

    .line 32
    .line 33
    :cond_2
    sget-object v2, LBb1/b;->k:LBb1/b$a;

    .line 34
    .line 35
    invoke-virtual {v2}, LBb1/b$a;->a()LBb1/b;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    :cond_3
    if-eqz p1, :cond_5

    .line 40
    .line 41
    invoke-virtual {p0}, LGb1/c;->a()LGb1/b;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    if-eqz p0, :cond_4

    .line 46
    .line 47
    invoke-static {p0, p3}, LFb1/a;->c(LGb1/b;Z)LBb1/c;

    .line 48
    .line 49
    .line 50
    move-result-object p0

    .line 51
    if-nez p0, :cond_7

    .line 52
    .line 53
    :cond_4
    sget-object p0, LBb1/c;->k:LBb1/c$a;

    .line 54
    .line 55
    invoke-virtual {p0}, LBb1/c$a;->a()LBb1/c;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    goto :goto_0

    .line 60
    :cond_5
    invoke-virtual {p0}, LGb1/c;->c()LGb1/e;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    if-eqz p0, :cond_6

    .line 65
    .line 66
    invoke-static {p0, p2}, LFb1/a;->f(LGb1/e;Z)LBb1/c;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    if-nez p0, :cond_7

    .line 71
    .line 72
    :cond_6
    sget-object p0, LBb1/c;->k:LBb1/c$a;

    .line 73
    .line 74
    invoke-virtual {p0}, LBb1/c$a;->a()LBb1/c;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    :cond_7
    :goto_0
    invoke-direct {v0, v1, v2, p0}, LBb1/a;-><init>(LBb1/b;LBb1/b;LBb1/c;)V

    .line 79
    .line 80
    .line 81
    return-object v0
.end method

.method public static final e(LGb1/d;)LBb1/b;
    .locals 15
    .param p0    # LGb1/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGb1/d;->b()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    sget-object p0, LBb1/b;->k:LBb1/b$a;

    .line 8
    .line 9
    invoke-virtual {p0}, LBb1/b$a;->a()LBb1/b;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    return-object p0

    .line 14
    :cond_0
    new-instance v0, LBb1/b;

    .line 15
    .line 16
    invoke-virtual {p0}, LGb1/d;->f()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    const-string v2, ""

    .line 21
    .line 22
    if-nez v1, :cond_1

    .line 23
    .line 24
    move-object v1, v2

    .line 25
    :cond_1
    invoke-virtual {p0}, LGb1/d;->e()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    if-nez v3, :cond_2

    .line 30
    .line 31
    move-object v3, v2

    .line 32
    :cond_2
    invoke-virtual {p0}, LGb1/d;->g()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    if-nez v4, :cond_3

    .line 37
    .line 38
    move-object v4, v2

    .line 39
    :cond_3
    invoke-virtual {p0}, LGb1/d;->c()Ljava/lang/Long;

    .line 40
    .line 41
    .line 42
    move-result-object v5

    .line 43
    if-eqz v5, :cond_9

    .line 44
    .line 45
    invoke-virtual {v5}, Ljava/lang/Long;->longValue()J

    .line 46
    .line 47
    .line 48
    move-result-wide v5

    .line 49
    invoke-virtual {p0}, LGb1/d;->d()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v7

    .line 53
    if-nez v7, :cond_4

    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_4
    move-object v2, v7

    .line 57
    :goto_0
    invoke-virtual {p0}, LGb1/d;->j()Ljava/lang/Integer;

    .line 58
    .line 59
    .line 60
    move-result-object v7

    .line 61
    const/4 v8, 0x0

    .line 62
    if-eqz v7, :cond_5

    .line 63
    .line 64
    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    .line 65
    .line 66
    .line 67
    move-result v7

    .line 68
    goto :goto_1

    .line 69
    :cond_5
    const/4 v7, 0x0

    .line 70
    :goto_1
    invoke-virtual {p0}, LGb1/d;->i()Ljava/lang/Long;

    .line 71
    .line 72
    .line 73
    move-result-object v9

    .line 74
    if-eqz v9, :cond_6

    .line 75
    .line 76
    invoke-virtual {v9}, Ljava/lang/Long;->longValue()J

    .line 77
    .line 78
    .line 79
    move-result-wide v9

    .line 80
    goto :goto_2

    .line 81
    :cond_6
    const-wide/16 v9, 0x0

    .line 82
    .line 83
    :goto_2
    invoke-virtual {p0}, LGb1/d;->h()Ljava/lang/Boolean;

    .line 84
    .line 85
    .line 86
    move-result-object v11

    .line 87
    if-eqz v11, :cond_7

    .line 88
    .line 89
    invoke-virtual {v11}, Ljava/lang/Boolean;->booleanValue()Z

    .line 90
    .line 91
    .line 92
    move-result v11

    .line 93
    goto :goto_3

    .line 94
    :cond_7
    const/4 v11, 0x0

    .line 95
    :goto_3
    invoke-virtual {p0}, LGb1/d;->a()Ljava/lang/Boolean;

    .line 96
    .line 97
    .line 98
    move-result-object p0

    .line 99
    if-eqz p0, :cond_8

    .line 100
    .line 101
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 102
    .line 103
    .line 104
    move-result v8

    .line 105
    :cond_8
    const/4 v12, 0x1

    .line 106
    move-wide v13, v5

    .line 107
    move-object v6, v2

    .line 108
    move-object v2, v3

    .line 109
    move-object v3, v4

    .line 110
    move-wide v4, v13

    .line 111
    move v13, v11

    .line 112
    move v11, v8

    .line 113
    move-wide v8, v9

    .line 114
    move v10, v13

    .line 115
    invoke-direct/range {v0 .. v12}, LBb1/b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;IJZZZ)V

    .line 116
    .line 117
    .line 118
    return-object v0

    .line 119
    :cond_9
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 120
    .line 121
    const/4 v0, 0x1

    .line 122
    const/4 v1, 0x0

    .line 123
    invoke-direct {p0, v1, v0, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 124
    .line 125
    .line 126
    throw p0
.end method

.method public static final f(LGb1/e;Z)LBb1/c;
    .locals 12
    .param p0    # LGb1/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGb1/e;->a()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez v0, :cond_7

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    goto :goto_5

    .line 10
    :cond_0
    new-instance v1, LBb1/c;

    .line 11
    .line 12
    invoke-virtual {p0}, LGb1/e;->c()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    const-string v0, ""

    .line 17
    .line 18
    if-nez p1, :cond_1

    .line 19
    .line 20
    move-object v2, v0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    move-object v2, p1

    .line 23
    :goto_0
    invoke-virtual {p0}, LGb1/e;->b()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    if-nez p1, :cond_2

    .line 28
    .line 29
    move-object v3, v0

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move-object v3, p1

    .line 32
    :goto_1
    invoke-virtual {p0}, LGb1/e;->d()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    if-nez p1, :cond_3

    .line 37
    .line 38
    move-object v4, v0

    .line 39
    goto :goto_2

    .line 40
    :cond_3
    move-object v4, p1

    .line 41
    :goto_2
    invoke-virtual {p0}, LGb1/e;->e()Ljava/lang/Integer;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    if-eqz p1, :cond_6

    .line 46
    .line 47
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    invoke-virtual {p0}, LGb1/e;->f()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    if-nez p1, :cond_4

    .line 56
    .line 57
    move-object v6, v0

    .line 58
    goto :goto_3

    .line 59
    :cond_4
    move-object v6, p1

    .line 60
    :goto_3
    invoke-virtual {p0}, LGb1/e;->g()Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    if-eqz p0, :cond_5

    .line 65
    .line 66
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 67
    .line 68
    .line 69
    move-result p0

    .line 70
    move v7, p0

    .line 71
    goto :goto_4

    .line 72
    :cond_5
    const/4 p0, 0x0

    .line 73
    const/4 v7, 0x0

    .line 74
    :goto_4
    sget-object v10, Lorg/xplatform/aggregator/api/model/BrandType;->NOT_CONTRACTED:Lorg/xplatform/aggregator/api/model/BrandType;

    .line 75
    .line 76
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 77
    .line 78
    .line 79
    move-result-object v11

    .line 80
    const/4 v8, 0x1

    .line 81
    const-string v9, ""

    .line 82
    .line 83
    invoke-direct/range {v1 .. v11}, LBb1/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;IZLjava/lang/String;Lorg/xplatform/aggregator/api/model/BrandType;Ljava/util/List;)V

    .line 84
    .line 85
    .line 86
    return-object v1

    .line 87
    :cond_6
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 88
    .line 89
    const/4 p1, 0x1

    .line 90
    const/4 v0, 0x0

    .line 91
    invoke-direct {p0, v0, p1, v0}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    throw p0

    .line 95
    :cond_7
    :goto_5
    sget-object p0, LBb1/c;->k:LBb1/c$a;

    .line 96
    .line 97
    invoke-virtual {p0}, LBb1/c$a;->a()LBb1/c;

    .line 98
    .line 99
    .line 100
    move-result-object p0

    .line 101
    return-object p0
.end method
