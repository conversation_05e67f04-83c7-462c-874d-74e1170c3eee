.class public final LtW0/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u001a\u0018\u00002\u00020\u0001Bi\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\r\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105\u00a8\u00066"
    }
    d2 = {
        "LtW0/v;",
        "LQW0/a;",
        "LwW0/g;",
        "getMaxBetSumUseCase",
        "LxW0/a;",
        "getJackpotTiragByBalanceUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;",
        "makeBetScenario",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/c;",
        "getHintStateScenario",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/f;",
        "getMinBetSumScenario",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LTZ0/a;",
        "actionDialogManager",
        "Lak/b;",
        "changeBalanceFeature",
        "Lak/a;",
        "balanceFeature",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "profileInteractor",
        "LzX0/k;",
        "snackbarManager",
        "<init>",
        "(LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;LxX0/a;LTZ0/a;Lak/b;Lak/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V",
        "LtW0/u;",
        "a",
        "()LtW0/u;",
        "LwW0/g;",
        "b",
        "LxW0/a;",
        "c",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;",
        "d",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/c;",
        "e",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/f;",
        "f",
        "LxX0/a;",
        "g",
        "LTZ0/a;",
        "h",
        "Lak/b;",
        "i",
        "Lak/a;",
        "j",
        "Lorg/xbet/ui_common/utils/M;",
        "k",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "l",
        "LzX0/k;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LwW0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LxW0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/toto_jackpot/impl/domain/scenario/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/toto_jackpot/impl/domain/scenario/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lak/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;LxX0/a;LTZ0/a;Lak/b;Lak/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V
    .locals 0
    .param p1    # LwW0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LxW0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/toto_jackpot/impl/domain/scenario/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/toto_jackpot/impl/domain/scenario/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lak/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LtW0/v;->a:LwW0/g;

    .line 5
    .line 6
    iput-object p2, p0, LtW0/v;->b:LxW0/a;

    .line 7
    .line 8
    iput-object p3, p0, LtW0/v;->c:Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;

    .line 9
    .line 10
    iput-object p4, p0, LtW0/v;->d:Lorg/xbet/toto_jackpot/impl/domain/scenario/c;

    .line 11
    .line 12
    iput-object p5, p0, LtW0/v;->e:Lorg/xbet/toto_jackpot/impl/domain/scenario/f;

    .line 13
    .line 14
    iput-object p6, p0, LtW0/v;->f:LxX0/a;

    .line 15
    .line 16
    iput-object p7, p0, LtW0/v;->g:LTZ0/a;

    .line 17
    .line 18
    iput-object p8, p0, LtW0/v;->h:Lak/b;

    .line 19
    .line 20
    iput-object p9, p0, LtW0/v;->i:Lak/a;

    .line 21
    .line 22
    iput-object p10, p0, LtW0/v;->j:Lorg/xbet/ui_common/utils/M;

    .line 23
    .line 24
    iput-object p11, p0, LtW0/v;->k:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 25
    .line 26
    iput-object p12, p0, LtW0/v;->l:LzX0/k;

    .line 27
    .line 28
    return-void
.end method


# virtual methods
.method public final a()LtW0/u;
    .locals 13
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LtW0/g;->a()LtW0/u$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v4, p0, LtW0/v;->a:LwW0/g;

    .line 6
    .line 7
    iget-object v5, p0, LtW0/v;->b:LxW0/a;

    .line 8
    .line 9
    iget-object v6, p0, LtW0/v;->d:Lorg/xbet/toto_jackpot/impl/domain/scenario/c;

    .line 10
    .line 11
    iget-object v7, p0, LtW0/v;->e:Lorg/xbet/toto_jackpot/impl/domain/scenario/f;

    .line 12
    .line 13
    iget-object v8, p0, LtW0/v;->c:Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;

    .line 14
    .line 15
    iget-object v9, p0, LtW0/v;->f:LxX0/a;

    .line 16
    .line 17
    iget-object v3, p0, LtW0/v;->g:LTZ0/a;

    .line 18
    .line 19
    iget-object v1, p0, LtW0/v;->h:Lak/b;

    .line 20
    .line 21
    iget-object v2, p0, LtW0/v;->i:Lak/a;

    .line 22
    .line 23
    iget-object v10, p0, LtW0/v;->j:Lorg/xbet/ui_common/utils/M;

    .line 24
    .line 25
    iget-object v11, p0, LtW0/v;->k:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 26
    .line 27
    iget-object v12, p0, LtW0/v;->l:LzX0/k;

    .line 28
    .line 29
    invoke-interface/range {v0 .. v12}, LtW0/u$a;->a(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)LtW0/u;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    return-object v0
.end method
