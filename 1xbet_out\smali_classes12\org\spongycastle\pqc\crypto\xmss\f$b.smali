.class public Lorg/spongycastle/pqc/crypto/xmss/f$b;
.super Lorg/spongycastle/pqc/crypto/xmss/k$a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/pqc/crypto/xmss/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/spongycastle/pqc/crypto/xmss/k$a<",
        "Lorg/spongycastle/pqc/crypto/xmss/f$b;",
        ">;"
    }
.end annotation


# instance fields
.field public e:I

.field public f:I

.field public g:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/spongycastle/pqc/crypto/xmss/k$a;-><init>(I)V

    .line 3
    .line 4
    .line 5
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->e:I

    .line 6
    .line 7
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->f:I

    .line 8
    .line 9
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->g:I

    .line 10
    .line 11
    return-void
.end method

.method public static synthetic i(Lorg/spongycastle/pqc/crypto/xmss/f$b;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->e:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic j(Lorg/spongycastle/pqc/crypto/xmss/f$b;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->f:I

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic k(Lorg/spongycastle/pqc/crypto/xmss/f$b;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->g:I

    .line 2
    .line 3
    return p0
.end method


# virtual methods
.method public bridge synthetic e()Lorg/spongycastle/pqc/crypto/xmss/k$a;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->m()Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public l()Lorg/spongycastle/pqc/crypto/xmss/k;
    .locals 2

    .line 1
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/f;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Lorg/spongycastle/pqc/crypto/xmss/f;-><init>(Lorg/spongycastle/pqc/crypto/xmss/f$b;Lorg/spongycastle/pqc/crypto/xmss/f$a;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method

.method public m()Lorg/spongycastle/pqc/crypto/xmss/f$b;
    .locals 0

    .line 1
    return-object p0
.end method

.method public n(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;
    .locals 0

    .line 1
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->f:I

    .line 2
    .line 3
    return-object p0
.end method

.method public o(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;
    .locals 0

    .line 1
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->g:I

    .line 2
    .line 3
    return-object p0
.end method

.method public p(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;
    .locals 0

    .line 1
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/f$b;->e:I

    .line 2
    .line 3
    return-object p0
.end method
