.class final Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.games_slider.impl.presentation.delegates.OneXGameCardViewModelDelegateImpl$onWebGameClicked$2"
    f = "OneXGameCardViewModelDelegateImpl.kt"
    l = {
        0x8f,
        0x92,
        0x94,
        0x95
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->S(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x4

    .line 8
    const/4 v3, 0x3

    .line 9
    const/4 v4, 0x2

    .line 10
    const/4 v5, 0x1

    .line 11
    if-eqz v1, :cond_4

    .line 12
    .line 13
    if-eq v1, v5, :cond_3

    .line 14
    .line 15
    if-eq v1, v4, :cond_2

    .line 16
    .line 17
    if-eq v1, v3, :cond_1

    .line 18
    .line 19
    if-ne v1, v2, :cond_0

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 23
    .line 24
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 25
    .line 26
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    goto :goto_2

    .line 34
    :cond_2
    :goto_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    goto :goto_4

    .line 38
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_4
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 46
    .line 47
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->o(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/i;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 52
    .line 53
    invoke-static {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 54
    .line 55
    .line 56
    move-result-wide v6

    .line 57
    iput v5, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->label:I

    .line 58
    .line 59
    invoke-interface {p1, v6, v7, p0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    if-ne p1, v0, :cond_5

    .line 64
    .line 65
    goto :goto_3

    .line 66
    :cond_5
    :goto_1
    check-cast p1, Ljava/lang/Boolean;

    .line 67
    .line 68
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    if-eqz p1, :cond_6

    .line 73
    .line 74
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 75
    .line 76
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->n(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lp9/c;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-nez p1, :cond_6

    .line 85
    .line 86
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 87
    .line 88
    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 89
    .line 90
    invoke-static {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 91
    .line 92
    .line 93
    move-result-wide v1

    .line 94
    iput v4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->label:I

    .line 95
    .line 96
    invoke-static {p1, v1, v2, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->K(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    if-ne p1, v0, :cond_8

    .line 101
    .line 102
    goto :goto_3

    .line 103
    :cond_6
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 104
    .line 105
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->p(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/o;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    iput v3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->label:I

    .line 110
    .line 111
    invoke-interface {p1, p0}, Lw30/o;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    if-ne p1, v0, :cond_7

    .line 116
    .line 117
    goto :goto_3

    .line 118
    :cond_7
    :goto_2
    check-cast p1, Ljava/util/List;

    .line 119
    .line 120
    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 121
    .line 122
    iget-object v3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 123
    .line 124
    iput v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;->label:I

    .line 125
    .line 126
    invoke-static {v1, p1, v3, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->L(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    if-ne p1, v0, :cond_8

    .line 131
    .line 132
    :goto_3
    return-object v0

    .line 133
    :cond_8
    :goto_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 134
    .line 135
    return-object p1
.end method
