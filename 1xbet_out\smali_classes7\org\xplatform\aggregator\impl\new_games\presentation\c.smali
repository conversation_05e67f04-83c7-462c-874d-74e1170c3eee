.class public final synthetic Lorg/xplatform/aggregator/impl/new_games/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

.field public final synthetic b:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/c;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/c;->b:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/c;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/c;->b:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->n3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
