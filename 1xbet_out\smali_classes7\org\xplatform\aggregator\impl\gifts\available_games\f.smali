.class public final synthetic Lorg/xplatform/aggregator/impl/gifts/available_games/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/f;->a:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/f;->a:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->B2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
