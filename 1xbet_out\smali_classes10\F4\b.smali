.class public interface abstract LF4/b;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Landroid/content/Context;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")",
            "Ljava/util/List<",
            "LF4/a;",
            ">;"
        }
    .end annotation
.end method

.method public abstract b(Landroid/content/Context;)V
.end method
