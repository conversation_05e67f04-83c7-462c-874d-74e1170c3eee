.class public final Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;
.super LTZ0/h;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\n\u0018\u0000 $2\u00020\u0001:\u0001%B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0019\u0010\u000b\u001a\u00020\u00062\u0008\u0010\n\u001a\u0004\u0018\u00010\tH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u0003R\"\u0010\u0015\u001a\u00020\u000e8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u000f\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012\"\u0004\u0008\u0013\u0010\u0014R\u001b\u0010\u001b\u001a\u00020\u00168BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0018\u001a\u0004\u0008\u0019\u0010\u001aR\"\u0010#\u001a\u00020\u001c8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 \"\u0004\u0008!\u0010\"\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;",
        "LTZ0/h;",
        "<init>",
        "()V",
        "Landroid/content/DialogInterface;",
        "dialog",
        "",
        "onDismiss",
        "(Landroid/content/DialogInterface;)V",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "q2",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "m0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "R2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;",
        "n0",
        "Lkotlin/j;",
        "Q2",
        "()Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;",
        "viewModel",
        "Lmg/b;",
        "o0",
        "Lmg/b;",
        "P2",
        "()Lmg/b;",
        "setAlertsPipeReceiver",
        "(Lmg/b;)V",
        "alertsPipeReceiver",
        "b1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public m0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o0:Lmg/b;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->b1:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    invoke-direct {p0}, LTZ0/h;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/a;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/a;-><init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)V

    .line 7
    .line 8
    .line 9
    new-instance v1, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$1;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 12
    .line 13
    .line 14
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 15
    .line 16
    new-instance v3, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$2;

    .line 17
    .line 18
    invoke-direct {v3, v1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    const-class v2, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;

    .line 26
    .line 27
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    new-instance v3, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$3;

    .line 32
    .line 33
    invoke-direct {v3, v1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 34
    .line 35
    .line 36
    new-instance v4, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$4;

    .line 37
    .line 38
    const/4 v5, 0x0

    .line 39
    invoke-direct {v4, v5, v1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 40
    .line 41
    .line 42
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->n0:Lkotlin/j;

    .line 47
    .line 48
    return-void
.end method

.method public static synthetic N2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->S2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic O2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;Lorg/xbet/uikit/components/dialog/DialogFields;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTZ0/h;->J2(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final S2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->R2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final P2()Lmg/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->o0:Lmg/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Q2()Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;

    .line 8
    .line 9
    return-object v0
.end method

.method public final R2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->m0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LTZ0/h;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    instance-of v0, p1, LQW0/b;

    .line 13
    .line 14
    const/4 v1, 0x0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    check-cast p1, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object p1, v1

    .line 21
    :goto_0
    const-class v0, Lrg/d;

    .line 22
    .line 23
    if-eqz p1, :cond_3

    .line 24
    .line 25
    invoke-interface {p1}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    check-cast p1, LBc/a;

    .line 34
    .line 35
    if-eqz p1, :cond_1

    .line 36
    .line 37
    invoke-interface {p1}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    check-cast p1, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object p1, v1

    .line 45
    :goto_1
    instance-of v2, p1, Lrg/d;

    .line 46
    .line 47
    if-nez v2, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v1, p1

    .line 51
    :goto_2
    check-cast v1, Lrg/d;

    .line 52
    .line 53
    if-eqz v1, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-virtual {v1, p1}, Lrg/d;->a(LwX0/c;)Lrg/c;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    invoke-interface {p1, p0}, Lrg/c;->a(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->P2()Lmg/b;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    new-instance v1, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;

    .line 75
    .line 76
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 77
    .line 78
    .line 79
    move-result-object v2

    .line 80
    invoke-virtual {v2}, Lorg/xbet/uikit/components/dialog/DialogFields;->k()Ljava/lang/CharSequence;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-static {v2}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    invoke-direct {v1, v2}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    invoke-interface {p1, v0, v1}, Lmg/b;->a(Landroidx/fragment/app/FragmentManager;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V

    .line 92
    .line 93
    .line 94
    return-void

    .line 95
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 96
    .line 97
    new-instance v1, Ljava/lang/StringBuilder;

    .line 98
    .line 99
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 100
    .line 101
    .line 102
    const-string v2, "Cannot create dependency "

    .line 103
    .line 104
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 105
    .line 106
    .line 107
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 108
    .line 109
    .line 110
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 119
    .line 120
    .line 121
    throw p1
.end method

.method public onDismiss(Landroid/content/DialogInterface;)V
    .locals 1
    .param p1    # Landroid/content/DialogInterface;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->P2()Lmg/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Lmg/b;->c()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0, p1}, Landroidx/fragment/app/l;->onDismiss(Landroid/content/DialogInterface;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public q2()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->Q2()Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/b;->p3()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, LTZ0/h;->q2()V

    .line 9
    .line 10
    .line 11
    return-void
.end method
