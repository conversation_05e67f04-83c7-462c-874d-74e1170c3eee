.class public final synthetic Lorg/xbet/themesettings/impl/presentation/timepicker/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/widget/NumberPicker$OnValueChangeListener;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/c;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    return-void
.end method


# virtual methods
.method public final onValueChange(Landroid/widget/NumberPicker;II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/c;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    invoke-static {v0, p1, p2, p3}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;->R2(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;Landroid/widget/NumberPicker;II)V

    return-void
.end method
