.class public final LM01/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008)\u0008\u0087\u0008\u0018\u00002\u00020\u0001B{\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u000b\u0012\u0008\u0008\u0002\u0010\r\u001a\u00020\u000b\u0012\n\u0008\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\n\u0008\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u0012\n\u0008\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u0012\n\u0008\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0013\u0012\u0008\u0008\u0002\u0010\u0015\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0010\u0010\u0018\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0010\u0010\u001a\u001a\u00020\u0006H\u00d6\u0001\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001a\u0010\u001d\u001a\u00020\u000b2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u001d\u0010\u001eR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\u0019R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008&\u0010\'\u001a\u0004\u0008(\u0010\u001bR(\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00088\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008)\u0010*\u001a\u0004\u0008+\u0010,\"\u0004\u0008-\u0010.R\u0017\u0010\u000c\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008!\u0010/\u001a\u0004\u00080\u00101R\u0017\u0010\r\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008(\u0010/\u001a\u0004\u00082\u00101R\u0019\u0010\u000f\u001a\u0004\u0018\u00010\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008+\u00103\u001a\u0004\u0008\"\u00104R\u0019\u0010\u0011\u001a\u0004\u0018\u00010\u00108\u0006\u00a2\u0006\u000c\n\u0004\u0008$\u00105\u001a\u0004\u0008)\u00106R\u0019\u0010\u0012\u001a\u0004\u0018\u00010\u00068\u0006\u00a2\u0006\u000c\n\u0004\u00080\u00107\u001a\u0004\u0008&\u00108R\u0019\u0010\u0014\u001a\u0004\u0018\u00010\u00138\u0006\u00a2\u0006\u000c\n\u0004\u00082\u00109\u001a\u0004\u0008\u001f\u0010:R\u0017\u0010\u0015\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008;\u0010/\u001a\u0004\u0008;\u00101\u00a8\u0006<"
    }
    d2 = {
        "LM01/c;",
        "",
        "",
        "iconId",
        "Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;",
        "type",
        "",
        "iconRes",
        "Lkotlin/Function0;",
        "",
        "onClick",
        "",
        "isClickable",
        "isDisable",
        "Lorg/xbet/uikit/components/badges/BadgeType;",
        "badgeType",
        "Lorg/xbet/uikit/components/counter/CounterType;",
        "counterType",
        "counter",
        "Lorg/xbet/uikit/models/StateStatus;",
        "badgeStatus",
        "isSearchButton",
        "<init>",
        "(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;Z)V",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/lang/String;",
        "e",
        "b",
        "Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;",
        "h",
        "()Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;",
        "c",
        "I",
        "f",
        "d",
        "Lkotlin/jvm/functions/Function0;",
        "g",
        "()Lkotlin/jvm/functions/Function0;",
        "l",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Z",
        "i",
        "()Z",
        "j",
        "Lorg/xbet/uikit/components/badges/BadgeType;",
        "()Lorg/xbet/uikit/components/badges/BadgeType;",
        "Lorg/xbet/uikit/components/counter/CounterType;",
        "()Lorg/xbet/uikit/components/counter/CounterType;",
        "Ljava/lang/Integer;",
        "()Ljava/lang/Integer;",
        "Lorg/xbet/uikit/models/StateStatus;",
        "()Lorg/xbet/uikit/models/StateStatus;",
        "k",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I

.field public d:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Z

.field public final f:Z

.field public final g:Lorg/xbet/uikit/components/badges/BadgeType;

.field public final h:Lorg/xbet/uikit/components/counter/CounterType;

.field public final i:Ljava/lang/Integer;

.field public final j:Lorg/xbet/uikit/models/StateStatus;

.field public final k:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;Z)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;",
            "I",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;ZZ",
            "Lorg/xbet/uikit/components/badges/BadgeType;",
            "Lorg/xbet/uikit/components/counter/CounterType;",
            "Ljava/lang/Integer;",
            "Lorg/xbet/uikit/models/StateStatus;",
            "Z)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, LM01/c;->a:Ljava/lang/String;

    .line 3
    iput-object p2, p0, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 4
    iput p3, p0, LM01/c;->c:I

    .line 5
    iput-object p4, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 6
    iput-boolean p5, p0, LM01/c;->e:Z

    .line 7
    iput-boolean p6, p0, LM01/c;->f:Z

    .line 8
    iput-object p7, p0, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 9
    iput-object p8, p0, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 10
    iput-object p9, p0, LM01/c;->i:Ljava/lang/Integer;

    .line 11
    iput-object p10, p0, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 12
    iput-boolean p11, p0, LM01/c;->k:Z

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 14

    move/from16 v0, p12

    and-int/lit8 v1, v0, 0x10

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    const/4 v7, 0x1

    goto :goto_0

    :cond_0
    move/from16 v7, p5

    :goto_0
    and-int/lit8 v1, v0, 0x20

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    const/4 v8, 0x0

    goto :goto_1

    :cond_1
    move/from16 v8, p6

    :goto_1
    and-int/lit8 v1, v0, 0x40

    const/4 v3, 0x0

    if-eqz v1, :cond_2

    move-object v9, v3

    goto :goto_2

    :cond_2
    move-object/from16 v9, p7

    :goto_2
    and-int/lit16 v1, v0, 0x80

    if-eqz v1, :cond_3

    move-object v10, v3

    goto :goto_3

    :cond_3
    move-object/from16 v10, p8

    :goto_3
    and-int/lit16 v1, v0, 0x100

    if-eqz v1, :cond_4

    move-object v11, v3

    goto :goto_4

    :cond_4
    move-object/from16 v11, p9

    :goto_4
    and-int/lit16 v1, v0, 0x200

    if-eqz v1, :cond_5

    move-object v12, v3

    goto :goto_5

    :cond_5
    move-object/from16 v12, p10

    :goto_5
    and-int/lit16 v0, v0, 0x400

    if-eqz v0, :cond_6

    const/4 v13, 0x0

    :goto_6
    move-object v2, p0

    move-object v3, p1

    move-object/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    goto :goto_7

    :cond_6
    move/from16 v13, p11

    goto :goto_6

    .line 13
    :goto_7
    invoke-direct/range {v2 .. v13}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;Z)V

    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/uikit/models/StateStatus;
    .locals 1

    .line 1
    iget-object v0, p0, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Lorg/xbet/uikit/components/badges/BadgeType;
    .locals 1

    .line 1
    iget-object v0, p0, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, LM01/c;->i:Ljava/lang/Integer;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Lorg/xbet/uikit/components/counter/CounterType;
    .locals 1

    .line 1
    iget-object v0, p0, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LM01/c;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, LM01/c;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, LM01/c;

    .line 12
    .line 13
    iget-object v1, p0, LM01/c;->a:Ljava/lang/String;

    .line 14
    .line 15
    iget-object v3, p1, LM01/c;->a:Ljava/lang/String;

    .line 16
    .line 17
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-nez v1, :cond_2

    .line 22
    .line 23
    return v2

    .line 24
    :cond_2
    iget-object v1, p0, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 25
    .line 26
    iget-object v3, p1, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 27
    .line 28
    if-eq v1, v3, :cond_3

    .line 29
    .line 30
    return v2

    .line 31
    :cond_3
    iget v1, p0, LM01/c;->c:I

    .line 32
    .line 33
    iget v3, p1, LM01/c;->c:I

    .line 34
    .line 35
    if-eq v1, v3, :cond_4

    .line 36
    .line 37
    return v2

    .line 38
    :cond_4
    iget-object v1, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 39
    .line 40
    iget-object v3, p1, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 41
    .line 42
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-nez v1, :cond_5

    .line 47
    .line 48
    return v2

    .line 49
    :cond_5
    iget-boolean v1, p0, LM01/c;->e:Z

    .line 50
    .line 51
    iget-boolean v3, p1, LM01/c;->e:Z

    .line 52
    .line 53
    if-eq v1, v3, :cond_6

    .line 54
    .line 55
    return v2

    .line 56
    :cond_6
    iget-boolean v1, p0, LM01/c;->f:Z

    .line 57
    .line 58
    iget-boolean v3, p1, LM01/c;->f:Z

    .line 59
    .line 60
    if-eq v1, v3, :cond_7

    .line 61
    .line 62
    return v2

    .line 63
    :cond_7
    iget-object v1, p0, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 64
    .line 65
    iget-object v3, p1, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 66
    .line 67
    if-eq v1, v3, :cond_8

    .line 68
    .line 69
    return v2

    .line 70
    :cond_8
    iget-object v1, p0, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 71
    .line 72
    iget-object v3, p1, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 73
    .line 74
    if-eq v1, v3, :cond_9

    .line 75
    .line 76
    return v2

    .line 77
    :cond_9
    iget-object v1, p0, LM01/c;->i:Ljava/lang/Integer;

    .line 78
    .line 79
    iget-object v3, p1, LM01/c;->i:Ljava/lang/Integer;

    .line 80
    .line 81
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    move-result v1

    .line 85
    if-nez v1, :cond_a

    .line 86
    .line 87
    return v2

    .line 88
    :cond_a
    iget-object v1, p0, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 89
    .line 90
    iget-object v3, p1, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 91
    .line 92
    if-eq v1, v3, :cond_b

    .line 93
    .line 94
    return v2

    .line 95
    :cond_b
    iget-boolean v1, p0, LM01/c;->k:Z

    .line 96
    .line 97
    iget-boolean p1, p1, LM01/c;->k:Z

    .line 98
    .line 99
    if-eq v1, p1, :cond_c

    .line 100
    .line 101
    return v2

    .line 102
    :cond_c
    return v0
.end method

.method public final f()I
    .locals 1

    .line 1
    iget v0, p0, LM01/c;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final g()Lkotlin/jvm/functions/Function0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    .line 1
    iget-object v0, p0, LM01/c;->a:Ljava/lang/String;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget-object v1, p0, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    mul-int/lit8 v0, v0, 0x1f

    .line 17
    .line 18
    iget v1, p0, LM01/c;->c:I

    .line 19
    .line 20
    add-int/2addr v0, v1

    .line 21
    mul-int/lit8 v0, v0, 0x1f

    .line 22
    .line 23
    iget-object v1, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 24
    .line 25
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    add-int/2addr v0, v1

    .line 30
    mul-int/lit8 v0, v0, 0x1f

    .line 31
    .line 32
    iget-boolean v1, p0, LM01/c;->e:Z

    .line 33
    .line 34
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    add-int/2addr v0, v1

    .line 39
    mul-int/lit8 v0, v0, 0x1f

    .line 40
    .line 41
    iget-boolean v1, p0, LM01/c;->f:Z

    .line 42
    .line 43
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 44
    .line 45
    .line 46
    move-result v1

    .line 47
    add-int/2addr v0, v1

    .line 48
    mul-int/lit8 v0, v0, 0x1f

    .line 49
    .line 50
    iget-object v1, p0, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 51
    .line 52
    const/4 v2, 0x0

    .line 53
    if-nez v1, :cond_0

    .line 54
    .line 55
    const/4 v1, 0x0

    .line 56
    goto :goto_0

    .line 57
    :cond_0
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    :goto_0
    add-int/2addr v0, v1

    .line 62
    mul-int/lit8 v0, v0, 0x1f

    .line 63
    .line 64
    iget-object v1, p0, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 65
    .line 66
    if-nez v1, :cond_1

    .line 67
    .line 68
    const/4 v1, 0x0

    .line 69
    goto :goto_1

    .line 70
    :cond_1
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 71
    .line 72
    .line 73
    move-result v1

    .line 74
    :goto_1
    add-int/2addr v0, v1

    .line 75
    mul-int/lit8 v0, v0, 0x1f

    .line 76
    .line 77
    iget-object v1, p0, LM01/c;->i:Ljava/lang/Integer;

    .line 78
    .line 79
    if-nez v1, :cond_2

    .line 80
    .line 81
    const/4 v1, 0x0

    .line 82
    goto :goto_2

    .line 83
    :cond_2
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 84
    .line 85
    .line 86
    move-result v1

    .line 87
    :goto_2
    add-int/2addr v0, v1

    .line 88
    mul-int/lit8 v0, v0, 0x1f

    .line 89
    .line 90
    iget-object v1, p0, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 91
    .line 92
    if-nez v1, :cond_3

    .line 93
    .line 94
    goto :goto_3

    .line 95
    :cond_3
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 96
    .line 97
    .line 98
    move-result v2

    .line 99
    :goto_3
    add-int/2addr v0, v2

    .line 100
    mul-int/lit8 v0, v0, 0x1f

    .line 101
    .line 102
    iget-boolean v1, p0, LM01/c;->k:Z

    .line 103
    .line 104
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 105
    .line 106
    .line 107
    move-result v1

    .line 108
    add-int/2addr v0, v1

    .line 109
    return v0
.end method

.method public final i()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LM01/c;->e:Z

    .line 2
    .line 3
    return v0
.end method

.method public final j()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LM01/c;->f:Z

    .line 2
    .line 3
    return v0
.end method

.method public final k()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LM01/c;->k:Z

    .line 2
    .line 3
    return v0
.end method

.method public final l(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 13
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LM01/c;->a:Ljava/lang/String;

    .line 2
    .line 3
    iget-object v1, p0, LM01/c;->b:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 4
    .line 5
    iget v2, p0, LM01/c;->c:I

    .line 6
    .line 7
    iget-object v3, p0, LM01/c;->d:Lkotlin/jvm/functions/Function0;

    .line 8
    .line 9
    iget-boolean v4, p0, LM01/c;->e:Z

    .line 10
    .line 11
    iget-boolean v5, p0, LM01/c;->f:Z

    .line 12
    .line 13
    iget-object v6, p0, LM01/c;->g:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 14
    .line 15
    iget-object v7, p0, LM01/c;->h:Lorg/xbet/uikit/components/counter/CounterType;

    .line 16
    .line 17
    iget-object v8, p0, LM01/c;->i:Ljava/lang/Integer;

    .line 18
    .line 19
    iget-object v9, p0, LM01/c;->j:Lorg/xbet/uikit/models/StateStatus;

    .line 20
    .line 21
    iget-boolean v10, p0, LM01/c;->k:Z

    .line 22
    .line 23
    new-instance v11, Ljava/lang/StringBuilder;

    .line 24
    .line 25
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 26
    .line 27
    .line 28
    const-string v12, "NavigationBarButtonModel(iconId="

    .line 29
    .line 30
    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    const-string v0, ", type="

    .line 37
    .line 38
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    invoke-virtual {v11, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 42
    .line 43
    .line 44
    const-string v0, ", iconRes="

    .line 45
    .line 46
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    invoke-virtual {v11, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 50
    .line 51
    .line 52
    const-string v0, ", onClick="

    .line 53
    .line 54
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 58
    .line 59
    .line 60
    const-string v0, ", isClickable="

    .line 61
    .line 62
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v11, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    const-string v0, ", isDisable="

    .line 69
    .line 70
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v11, v5}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    const-string v0, ", badgeType="

    .line 77
    .line 78
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 79
    .line 80
    .line 81
    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    const-string v0, ", counterType="

    .line 85
    .line 86
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    invoke-virtual {v11, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    const-string v0, ", counter="

    .line 93
    .line 94
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    .line 96
    .line 97
    invoke-virtual {v11, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 98
    .line 99
    .line 100
    const-string v0, ", badgeStatus="

    .line 101
    .line 102
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 106
    .line 107
    .line 108
    const-string v0, ", isSearchButton="

    .line 109
    .line 110
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 111
    .line 112
    .line 113
    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    const-string v0, ")"

    .line 117
    .line 118
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    return-object v0
.end method
