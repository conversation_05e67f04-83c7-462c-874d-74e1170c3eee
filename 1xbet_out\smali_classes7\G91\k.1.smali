.class public final synthetic LG91/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LB4/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/k;->a:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LG91/k;->a:LB4/a;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/FiltersChipsTypeProviderChipsAdapterDelegateKt;->d(LB4/a;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
