.class public final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;,
        Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$g;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008e\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0006\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008.\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0012\u0018\u00002\u00020\u0001:\u000c\u00bb\u0001\u00bc\u0001\u00bd\u0001\u00be\u0001\u00bf\u0001\u00c0\u0001B\u009b\u0001\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008)\u0010*J\u000f\u0010+\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008+\u0010*J\u000f\u0010,\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008,\u0010*J\u000f\u0010-\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008-\u0010*J\u0017\u00100\u001a\u00020(2\u0006\u0010/\u001a\u00020.H\u0002\u00a2\u0006\u0004\u00080\u00101J\u000f\u00102\u001a\u00020(H\u0002\u00a2\u0006\u0004\u00082\u0010*J\u000f\u00103\u001a\u00020(H\u0002\u00a2\u0006\u0004\u00083\u0010*J\u000f\u00104\u001a\u00020(H\u0002\u00a2\u0006\u0004\u00084\u0010*J\u0017\u00107\u001a\u00020(2\u0006\u00106\u001a\u000205H\u0002\u00a2\u0006\u0004\u00087\u00108J\u0017\u0010;\u001a\u00020(2\u0006\u0010:\u001a\u000209H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u000f\u0010=\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008=\u0010*J\u000f\u0010>\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008>\u0010*J\u000f\u0010?\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008?\u0010*J\u001e\u0010C\u001a\u00020(2\u000c\u0010B\u001a\u0008\u0012\u0004\u0012\u00020A0@H\u0082@\u00a2\u0006\u0004\u0008C\u0010DJ\u001d\u0010E\u001a\u00020(2\u000c\u0010B\u001a\u0008\u0012\u0004\u0012\u00020A0@H\u0002\u00a2\u0006\u0004\u0008E\u0010FJ\u0017\u0010I\u001a\u00020(2\u0006\u0010H\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008I\u0010JJ\u0017\u0010M\u001a\u00020(2\u0006\u0010L\u001a\u00020KH\u0002\u00a2\u0006\u0004\u0008M\u0010NJ\u0017\u0010O\u001a\u00020(2\u0006\u00106\u001a\u000205H\u0002\u00a2\u0006\u0004\u0008O\u00108J\u0013\u0010Q\u001a\u00020(*\u00020PH\u0002\u00a2\u0006\u0004\u0008Q\u0010RJ\u0013\u0010T\u001a\u00020(*\u00020SH\u0002\u00a2\u0006\u0004\u0008T\u0010UJ\u0013\u0010W\u001a\u00020(*\u00020VH\u0002\u00a2\u0006\u0004\u0008W\u0010XJ\u0013\u0010Z\u001a\u00020(*\u00020YH\u0002\u00a2\u0006\u0004\u0008Z\u0010[J\u0013\u0010]\u001a\u00020(*\u00020\\H\u0002\u00a2\u0006\u0004\u0008]\u0010^J\u0015\u0010a\u001a\u0008\u0012\u0004\u0012\u00020`0_H\u0000\u00a2\u0006\u0004\u0008a\u0010bJ\u0015\u0010c\u001a\u0008\u0012\u0004\u0012\u00020V0_H\u0000\u00a2\u0006\u0004\u0008c\u0010bJ\u0015\u0010d\u001a\u0008\u0012\u0004\u0012\u00020S0_H\u0000\u00a2\u0006\u0004\u0008d\u0010bJ\u0015\u0010e\u001a\u0008\u0012\u0004\u0012\u00020P0_H\u0000\u00a2\u0006\u0004\u0008e\u0010bJ\u0015\u0010f\u001a\u0008\u0012\u0004\u0012\u00020Y0_H\u0000\u00a2\u0006\u0004\u0008f\u0010bJ\u0015\u0010g\u001a\u0008\u0012\u0004\u0012\u00020\\0_H\u0000\u00a2\u0006\u0004\u0008g\u0010bJ\u0017\u0010i\u001a\u00020(2\u0006\u0010h\u001a\u00020AH\u0000\u00a2\u0006\u0004\u0008i\u0010jJ\u0017\u0010m\u001a\u00020(2\u0006\u0010l\u001a\u00020kH\u0000\u00a2\u0006\u0004\u0008m\u0010nJ\u000f\u0010o\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008o\u0010*J\u0017\u0010r\u001a\u00020(2\u0006\u0010q\u001a\u00020pH\u0000\u00a2\u0006\u0004\u0008r\u0010sJ\u000f\u0010t\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008t\u0010*J\u000f\u0010u\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008u\u0010*J\u000f\u0010v\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008v\u0010*J\u0017\u0010x\u001a\u00020(2\u0006\u0010w\u001a\u00020GH\u0000\u00a2\u0006\u0004\u0008x\u0010JJ\u000f\u0010y\u001a\u00020GH\u0000\u00a2\u0006\u0004\u0008y\u0010zR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0001\u0010\u009a\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0001\u0010\u009e\u0001R\u001c\u0010\u00a2\u0001\u001a\u0005\u0018\u00010\u009f\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00a1\u0001R\u001c\u0010\u00a4\u0001\u001a\u0005\u0018\u00010\u009f\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a1\u0001R\u001c\u0010\u00a6\u0001\u001a\u0005\u0018\u00010\u009f\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u00a1\u0001R\u001c\u0010\u00a8\u0001\u001a\u0005\u0018\u00010\u009f\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a1\u0001R\u0019\u0010\u00ab\u0001\u001a\u00020G8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a9\u0001\u0010\u00aa\u0001R\u001d\u0010\u00ae\u0001\u001a\t\u0012\u0004\u0012\u00020`0\u00ac\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008>\u0010\u00ad\u0001R\u001e\u0010\u00b2\u0001\u001a\t\u0012\u0004\u0012\u00020V0\u00af\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b0\u0001\u0010\u00b1\u0001R\u001e\u0010\u00b4\u0001\u001a\t\u0012\u0004\u0012\u00020S0\u00af\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b1\u0001R\u001e\u0010\u00b6\u0001\u001a\t\u0012\u0004\u0012\u00020P0\u00af\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b5\u0001\u0010\u00b1\u0001R\u001e\u0010\u00b8\u0001\u001a\t\u0012\u0004\u0012\u00020Y0\u00af\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00b1\u0001R\u001e\u0010\u00ba\u0001\u001a\t\u0012\u0004\u0012\u00020\\0\u00af\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00b1\u0001\u00a8\u0006\u00c1\u0001"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwX0/c;",
        "router",
        "Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;",
        "africanRouletteInteractor",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "startGameIfPossibleScenario",
        "Lorg/xbet/core/domain/usecases/u;",
        "observeCommandUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "getGameStateUseCase",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "getBonusUseCase",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "setGameInProgressUseCase",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "onBetSetScenario",
        "Lorg/xbet/core/domain/usecases/bet/h;",
        "getCurrentMinBetUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "updateLastBetForMultiChoiceGameScenario",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "isGameInProgressUseCase",
        "LWv/b;",
        "getConnectionStatusUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "getBetSumUseCase",
        "Lorg/xbet/core/domain/usecases/bet/l;",
        "getInstantBetVisibilityUseCase",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "getCurrencyUseCase",
        "<init>",
        "(LwX0/c;Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/h;LWv/b;Lm8/a;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/l;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;)V",
        "",
        "m4",
        "()V",
        "l4",
        "D4",
        "E4",
        "",
        "betSum",
        "c4",
        "(D)V",
        "r4",
        "q4",
        "p4",
        "LTv/d;",
        "command",
        "Z3",
        "(LTv/d;)V",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "bonus",
        "f4",
        "(Lorg/xbet/games_section/api/models/GameBonus;)V",
        "k4",
        "S3",
        "d4",
        "",
        "Lig/a;",
        "betsList",
        "i4",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "s4",
        "(Ljava/util/List;)V",
        "",
        "instantBetAllowed",
        "G4",
        "(Z)V",
        "",
        "throwable",
        "a4",
        "(Ljava/lang/Throwable;)V",
        "Q3",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
        "w4",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
        "u4",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;",
        "t4",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;",
        "v4",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;)V",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;",
        "x4",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;",
        "X3",
        "()Lkotlinx/coroutines/flow/e;",
        "T3",
        "U3",
        "W3",
        "V3",
        "Y3",
        "bet",
        "o4",
        "(Lig/a;)V",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "africanRouletteBetType",
        "g4",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "F4",
        "",
        "leftMargin",
        "j4",
        "(I)V",
        "e4",
        "n4",
        "h4",
        "initialized",
        "R3",
        "b4",
        "()Z",
        "v1",
        "LwX0/c;",
        "x1",
        "Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;",
        "y1",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "F1",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "H1",
        "Lorg/xbet/core/domain/usecases/u;",
        "I1",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "P1",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "S1",
        "Lorg/xbet/core/domain/usecases/d;",
        "V1",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "b2",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "v2",
        "Lorg/xbet/core/domain/usecases/bet/h;",
        "x2",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "y2",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "F2",
        "LWv/b;",
        "H2",
        "Lm8/a;",
        "I2",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "P2",
        "Lorg/xbet/core/domain/usecases/bet/l;",
        "S2",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "Lkotlinx/coroutines/x0;",
        "V2",
        "Lkotlinx/coroutines/x0;",
        "observeBetsJob",
        "X2",
        "makeBetJob",
        "F3",
        "playBetJob",
        "H3",
        "gameFinishJob",
        "I3",
        "Z",
        "rouletteInitialized",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlinx/coroutines/flow/V;",
        "viewStateFlow",
        "Lkotlinx/coroutines/flow/U;",
        "H4",
        "Lkotlinx/coroutines/flow/U;",
        "betInfoStateFlow",
        "X4",
        "gameFieldStateFlow",
        "v5",
        "rouletteStateFlow",
        "w5",
        "oneExecutionState",
        "x5",
        "wheelParamsStateFlow",
        "d",
        "b",
        "a",
        "e",
        "f",
        "c",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LWv/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F3:Lkotlinx/coroutines/x0;

.field public final H1:Lorg/xbet/core/domain/usecases/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H3:Lkotlinx/coroutines/x0;

.field public final H4:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/core/domain/usecases/game_info/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/core/domain/usecases/bet/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I3:Z

.field public final P1:Lorg/xbet/core/domain/usecases/bonus/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xbet/core/domain/usecases/bet/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/core/domain/usecases/game_state/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V2:Lkotlinx/coroutines/x0;

.field public X2:Lkotlinx/coroutines/x0;

.field public final X4:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lorg/xbet/core/domain/usecases/bet/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/core/domain/usecases/bet/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/core/domain/usecases/game_info/H;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lorg/xbet/core/domain/usecases/game_state/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwX0/c;Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/h;LWv/b;Lm8/a;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/l;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;)V
    .locals 0
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/core/domain/usecases/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/core/domain/usecases/game_info/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/bonus/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/core/domain/usecases/game_state/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/core/domain/usecases/bet/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/core/domain/usecases/bet/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/core/domain/usecases/game_info/H;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/core/domain/usecases/game_state/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LWv/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/core/domain/usecases/bet/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/core/domain/usecases/bet/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v1:LwX0/c;

    .line 3
    iput-object p2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 4
    iput-object p3, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 5
    iput-object p4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 6
    iput-object p5, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H1:Lorg/xbet/core/domain/usecases/u;

    .line 7
    iput-object p6, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 8
    iput-object p7, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 9
    iput-object p8, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S1:Lorg/xbet/core/domain/usecases/d;

    .line 10
    iput-object p9, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->V1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 11
    iput-object p10, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->b2:Lorg/xbet/core/domain/usecases/bet/o;

    .line 12
    iput-object p11, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v2:Lorg/xbet/core/domain/usecases/bet/h;

    .line 13
    iput-object p12, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x2:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 14
    iput-object p13, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 15
    iput-object p14, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F2:LWv/b;

    .line 16
    iput-object p15, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    move-object/from16 p1, p16

    .line 17
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I2:Lorg/xbet/core/domain/usecases/bet/d;

    move-object/from16 p1, p17

    .line 18
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P2:Lorg/xbet/core/domain/usecases/bet/l;

    move-object/from16 p1, p18

    .line 19
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 20
    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    const/4 p2, 0x0

    const/4 p3, 0x3

    const/4 p4, 0x0

    invoke-direct {p1, p2, p2, p3, p4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;-><init>(ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    const/4 p1, 0x6

    .line 21
    invoke-static {p3, p2, p4, p1, p4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    move-result-object p6

    iput-object p6, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H4:Lkotlinx/coroutines/flow/U;

    .line 22
    invoke-static {p3, p2, p4, p1, p4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->X4:Lkotlinx/coroutines/flow/U;

    const/4 p1, 0x7

    .line 23
    invoke-static {p2, p2, p4, p1, p4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v5:Lkotlinx/coroutines/flow/U;

    .line 24
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w5:Lkotlinx/coroutines/flow/U;

    .line 25
    sget-object p1, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    const/4 p3, 0x2

    const/4 p6, 0x1

    .line 26
    invoke-static {p6, p2, p1, p3, p4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 27
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->p4()V

    .line 28
    invoke-virtual {p5}, Lorg/xbet/core/domain/usecases/u;->a()Lkotlinx/coroutines/flow/e;

    move-result-object p1

    new-instance p2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$1;

    invoke-direct {p2, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$1;-><init>(Ljava/lang/Object;)V

    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    move-result-object p1

    .line 29
    new-instance p2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$2;

    invoke-direct {p2, p0, p4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    move-result-object p1

    .line 30
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object p2

    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 31
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->d4()V

    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->X4:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final A4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final B4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final C4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/bet/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->b2:Lorg/xbet/core/domain/usecases/bet/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/game_info/H;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x2:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->a4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->i4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->t4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->E4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final Q3(LTv/d;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$addCommand$1;->INSTANCE:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$addCommand$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$addCommand$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$addCommand$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;LTv/d;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final S3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$1;

    .line 18
    .line 19
    invoke-direct {v3, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$1;-><init>(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 23
    .line 24
    invoke-interface {v0}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    new-instance v7, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method private final Z3(LTv/d;)V
    .locals 2

    .line 1
    instance-of v0, p1, LTv/a$g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_7

    .line 12
    .line 13
    check-cast p1, LTv/a$g;

    .line 14
    .line 15
    invoke-virtual {p1}, LTv/a$g;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->f4(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    instance-of v0, p1, LTv/a$o;

    .line 24
    .line 25
    if-eqz v0, :cond_2

    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->q()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->isEmpty()Z

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    if-eqz p1, :cond_1

    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 40
    .line 41
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->m()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    if-eqz p1, :cond_1

    .line 50
    .line 51
    sget-object p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c$a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c$a;

    .line 52
    .line 53
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;)V

    .line 54
    .line 55
    .line 56
    return-void

    .line 57
    :cond_1
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I2:Lorg/xbet/core/domain/usecases/bet/d;

    .line 58
    .line 59
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 60
    .line 61
    .line 62
    move-result-wide v0

    .line 63
    invoke-virtual {p0, v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->c4(D)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_2
    instance-of v0, p1, LTv/a$w;

    .line 68
    .line 69
    if-eqz v0, :cond_3

    .line 70
    .line 71
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->k4()V

    .line 72
    .line 73
    .line 74
    return-void

    .line 75
    :cond_3
    instance-of v0, p1, LTv/a$p;

    .line 76
    .line 77
    if-eqz v0, :cond_4

    .line 78
    .line 79
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->r4()V

    .line 80
    .line 81
    .line 82
    return-void

    .line 83
    :cond_4
    instance-of v0, p1, LTv/a$r;

    .line 84
    .line 85
    if-eqz v0, :cond_6

    .line 86
    .line 87
    check-cast p1, LTv/a$r;

    .line 88
    .line 89
    invoke-virtual {p1}, LTv/a$r;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 98
    .line 99
    .line 100
    move-result p1

    .line 101
    if-eqz p1, :cond_5

    .line 102
    .line 103
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->q4()V

    .line 104
    .line 105
    .line 106
    return-void

    .line 107
    :cond_5
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->r4()V

    .line 108
    .line 109
    .line 110
    return-void

    .line 111
    :cond_6
    instance-of v0, p1, LTv/b$l;

    .line 112
    .line 113
    if-eqz v0, :cond_7

    .line 114
    .line 115
    check-cast p1, LTv/b$l;

    .line 116
    .line 117
    invoke-virtual {p1}, LTv/b$l;->a()Z

    .line 118
    .line 119
    .line 120
    move-result p1

    .line 121
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->G4(Z)V

    .line 122
    .line 123
    .line 124
    :cond_7
    return-void
.end method

.method private final a4(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$handleGameError$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final k4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$1;

    .line 18
    .line 19
    invoke-direct {v3, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$1;-><init>(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 23
    .line 24
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    new-instance v7, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->A4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4()V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->g()V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;-><init>(Z)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 13
    .line 14
    .line 15
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    invoke-direct {v0, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;-><init>(Z)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 22
    .line 23
    .line 24
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$b;

    .line 25
    .line 26
    invoke-direct {v0, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$b;-><init>(Z)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->t4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 33
    .line 34
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    move-object v4, v3

    .line 39
    check-cast v4, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 40
    .line 41
    const/4 v5, 0x0

    .line 42
    invoke-static {v4, v1, v1, v2, v5}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    invoke-interface {v0, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    if-eqz v3, :cond_0

    .line 51
    .line 52
    return-void
.end method

.method public static synthetic q3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->C4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->B4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->z4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->Z3(LTv/d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H4:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final y4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S1:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final z4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final D4()V
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->l()Lig/b;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Lig/b;->d()Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->getDegree()F

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->p()F

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const/4 v3, 0x0

    .line 24
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;-><init>(ZFF)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 28
    .line 29
    .line 30
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 31
    .line 32
    const/4 v1, 0x1

    .line 33
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;-><init>(Z)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final E4()V
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;-><init>(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 8
    .line 9
    .line 10
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;

    .line 11
    .line 12
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 13
    .line 14
    invoke-virtual {v2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->l()Lig/b;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    invoke-virtual {v2}, Lig/b;->d()Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->getDegree()F

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    iget-object v3, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 27
    .line 28
    invoke-virtual {v3}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->p()F

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;-><init>(ZFF)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final F4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F2:LWv/b;

    .line 10
    .line 11
    invoke-virtual {v0}, LWv/b;->a()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->V1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 19
    .line 20
    const/4 v1, 0x1

    .line 21
    invoke-virtual {v0, v1}, Lorg/xbet/core/domain/usecases/game_state/l;->a(Z)V

    .line 22
    .line 23
    .line 24
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    new-instance v3, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$startGameIfPossible$1;

    .line 29
    .line 30
    invoke-direct {v3, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$startGameIfPossible$1;-><init>(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 34
    .line 35
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    new-instance v7, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$startGameIfPossible$2;

    .line 40
    .line 41
    const/4 v0, 0x0

    .line 42
    invoke-direct {v7, p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$startGameIfPossible$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 43
    .line 44
    .line 45
    const/16 v8, 0xa

    .line 46
    .line 47
    const/4 v9, 0x0

    .line 48
    const/4 v4, 0x0

    .line 49
    const/4 v6, 0x0

    .line 50
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    .line 53
    :cond_1
    :goto_0
    return-void
.end method

.method public final G4(Z)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    :cond_1
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    move-object v2, v1

    .line 25
    check-cast v2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 26
    .line 27
    const/4 v3, 0x1

    .line 28
    const/4 v4, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    invoke-static {v2, v5, p1, v3, v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    if-eqz v1, :cond_1

    .line 39
    .line 40
    :goto_0
    return-void
.end method

.method public final R3(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I3:Z

    .line 2
    .line 3
    return-void
.end method

.method public final T3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H4:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final U3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->X4:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final V3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final W3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final X3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final Y3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b4()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I3:Z

    .line 2
    .line 3
    return v0
.end method

.method public final c4(D)V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->X2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$1;

    .line 18
    .line 19
    invoke-direct {v3, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$1;-><init>(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H2:Lm8/a;

    .line 23
    .line 24
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    new-instance v7, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, p1, p2, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;DLkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->X2:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public final d4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->V2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->o()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$observeBets$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$observeBets$1;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$observeBets$2;

    .line 30
    .line 31
    invoke-direct {v1, p0, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$observeBets$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->V2:Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    return-void
.end method

.method public final e4()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final f4(Lorg/xbet/games_section/api/models/GameBonus;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    sget-object v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$g;->b:[I

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    aget p1, v0, p1

    .line 12
    .line 13
    const/4 v0, 0x1

    .line 14
    if-eq p1, v0, :cond_4

    .line 15
    .line 16
    const/4 v0, 0x2

    .line 17
    if-eq p1, v0, :cond_3

    .line 18
    .line 19
    const/4 v0, 0x3

    .line 20
    if-eq p1, v0, :cond_3

    .line 21
    .line 22
    const/4 v0, 0x4

    .line 23
    if-eq p1, v0, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 27
    .line 28
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->n()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    if-eqz p1, :cond_1

    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 39
    .line 40
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    sget-object v0, Lorg/xbet/core/domain/GameState;->FINISHED:Lorg/xbet/core/domain/GameState;

    .line 45
    .line 46
    if-ne p1, v0, :cond_1

    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->v2:Lorg/xbet/core/domain/usecases/bet/h;

    .line 49
    .line 50
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bet/h;->a()D

    .line 51
    .line 52
    .line 53
    move-result-wide v0

    .line 54
    invoke-virtual {p0, v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->c4(D)V

    .line 55
    .line 56
    .line 57
    :cond_1
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 58
    .line 59
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->i()V

    .line 60
    .line 61
    .line 62
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 63
    .line 64
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    sget-object v0, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 69
    .line 70
    if-ne p1, v0, :cond_2

    .line 71
    .line 72
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->r4()V

    .line 73
    .line 74
    .line 75
    :cond_2
    :goto_0
    return-void

    .line 76
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->r4()V

    .line 77
    .line 78
    .line 79
    return-void

    .line 80
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->q4()V

    .line 81
    .line 82
    .line 83
    return-void
.end method

.method public final g4(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 2
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;-><init>(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 10
    .line 11
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->x(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 15
    .line 16
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    if-eqz p1, :cond_0

    .line 29
    .line 30
    const-wide/16 v0, 0x0

    .line 31
    .line 32
    invoke-virtual {p0, v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->c4(D)V

    .line 33
    .line 34
    .line 35
    :cond_0
    return-void
.end method

.method public final h4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->V2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->d4()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->l4()V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->m4()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final i4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lig/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->D$0:D

    .line 39
    .line 40
    iget-boolean p1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->Z$1:Z

    .line 41
    .line 42
    iget-boolean v4, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->Z$0:Z

    .line 43
    .line 44
    iget-object v5, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->L$1:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 47
    .line 48
    iget-object v0, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v0, Ljava/util/List;

    .line 51
    .line 52
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 57
    .line 58
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 59
    .line 60
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw p1

    .line 64
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->s4(Ljava/util/List;)V

    .line 68
    .line 69
    .line 70
    iget-object p2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    invoke-virtual {p2}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 77
    .line 78
    .line 79
    move-result-object p2

    .line 80
    invoke-virtual {p2}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 81
    .line 82
    .line 83
    move-result p2

    .line 84
    if-nez p2, :cond_3

    .line 85
    .line 86
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 87
    .line 88
    .line 89
    move-result v2

    .line 90
    if-nez v2, :cond_3

    .line 91
    .line 92
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->b2:Lorg/xbet/core/domain/usecases/bet/o;

    .line 93
    .line 94
    iget-object v4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 95
    .line 96
    invoke-virtual {v4, p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->j(Ljava/util/List;)D

    .line 97
    .line 98
    .line 99
    move-result-wide v4

    .line 100
    invoke-virtual {v2, v4, v5}, Lorg/xbet/core/domain/usecases/bet/o;->a(D)V

    .line 101
    .line 102
    .line 103
    :cond_3
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 104
    .line 105
    invoke-virtual {v2, p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->j(Ljava/util/List;)D

    .line 106
    .line 107
    .line 108
    move-result-wide v4

    .line 109
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 110
    .line 111
    iput-object p1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->L$0:Ljava/lang/Object;

    .line 112
    .line 113
    iput-object p0, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->L$1:Ljava/lang/Object;

    .line 114
    .line 115
    iput-boolean p2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->Z$0:Z

    .line 116
    .line 117
    iput-boolean p2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->Z$1:Z

    .line 118
    .line 119
    iput-wide v4, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->D$0:D

    .line 120
    .line 121
    iput v3, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    .line 122
    .line 123
    invoke-virtual {v2, v0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    if-ne v0, v1, :cond_4

    .line 128
    .line 129
    return-object v1

    .line 130
    :cond_4
    move-wide v1, v4

    .line 131
    move-object v5, p0

    .line 132
    move v4, p2

    .line 133
    move-object p2, v0

    .line 134
    move-object v0, p1

    .line 135
    move p1, v4

    .line 136
    :goto_1
    check-cast p2, Ljava/lang/String;

    .line 137
    .line 138
    new-instance v6, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$a;

    .line 139
    .line 140
    invoke-direct {v6, p1, v1, v2, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$a;-><init>(ZDLjava/lang/String;)V

    .line 141
    .line 142
    .line 143
    invoke-virtual {v5, v6}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->t4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V

    .line 144
    .line 145
    .line 146
    sget-object p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;

    .line 147
    .line 148
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 149
    .line 150
    .line 151
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 152
    .line 153
    .line 154
    move-result p1

    .line 155
    const/4 p2, 0x0

    .line 156
    if-nez p1, :cond_5

    .line 157
    .line 158
    if-nez v4, :cond_5

    .line 159
    .line 160
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 161
    .line 162
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 163
    .line 164
    .line 165
    move-result-object p1

    .line 166
    sget-object v1, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 167
    .line 168
    if-ne p1, v1, :cond_5

    .line 169
    .line 170
    goto :goto_2

    .line 171
    :cond_5
    const/4 v3, 0x0

    .line 172
    :goto_2
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 173
    .line 174
    :cond_6
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v1

    .line 178
    move-object v2, v1

    .line 179
    check-cast v2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 180
    .line 181
    const/4 v4, 0x2

    .line 182
    const/4 v5, 0x0

    .line 183
    invoke-static {v2, v3, p2, v4, v5}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 184
    .line 185
    .line 186
    move-result-object v2

    .line 187
    invoke-interface {p1, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move-result v1

    .line 191
    if-eqz v1, :cond_6

    .line 192
    .line 193
    if-eqz v3, :cond_7

    .line 194
    .line 195
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P2:Lorg/xbet/core/domain/usecases/bet/l;

    .line 196
    .line 197
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bet/l;->a()Z

    .line 198
    .line 199
    .line 200
    move-result p1

    .line 201
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->G4(Z)V

    .line 202
    .line 203
    .line 204
    :cond_7
    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$c;

    .line 205
    .line 206
    invoke-direct {p1, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$c;-><init>(Ljava/util/List;)V

    .line 207
    .line 208
    .line 209
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->t4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V

    .line 210
    .line 211
    .line 212
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 213
    .line 214
    return-object p1
.end method

.method public final j4(I)V
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f$a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final l4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 8
    .line 9
    if-ne v0, v1, :cond_0

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;-><init>(Z)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 21
    .line 22
    invoke-virtual {v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->q()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->isNotEmpty()Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 33
    .line 34
    invoke-virtual {v1, v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->h(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    if-nez v1, :cond_0

    .line 39
    .line 40
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->g4(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 41
    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 44
    .line 45
    invoke-virtual {v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->m()Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->s4(Ljava/util/List;)V

    .line 50
    .line 51
    .line 52
    :cond_0
    return-void
.end method

.method public final m4()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 8
    .line 9
    if-ne v0, v1, :cond_2

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 12
    .line 13
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 26
    .line 27
    invoke-virtual {v1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->m()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    const/4 v2, 0x0

    .line 36
    if-nez v1, :cond_0

    .line 37
    .line 38
    if-nez v0, :cond_0

    .line 39
    .line 40
    const/4 v0, 0x1

    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 v0, 0x0

    .line 43
    :goto_0
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    :cond_1
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    move-object v4, v3

    .line 50
    check-cast v4, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 51
    .line 52
    const/4 v5, 0x2

    .line 53
    const/4 v6, 0x0

    .line 54
    invoke-static {v4, v0, v2, v5, v6}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    invoke-interface {v1, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    if-eqz v3, :cond_1

    .line 63
    .line 64
    :cond_2
    return-void
.end method

.method public final n4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$g;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_2

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-eq v0, v1, :cond_1

    .line 20
    .line 21
    const/4 v1, 0x3

    .line 22
    if-ne v0, v1, :cond_0

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->D4()V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 29
    .line 30
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 31
    .line 32
    .line 33
    throw v0

    .line 34
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->D4()V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3()V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 42
    .line 43
    const/4 v1, 0x0

    .line 44
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;-><init>(Z)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public final o4(Lig/a;)V
    .locals 2
    .param p1    # Lig/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_2

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->F2:LWv/b;

    .line 10
    .line 11
    invoke-virtual {v0}, LWv/b;->a()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 19
    .line 20
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget-object v1, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 25
    .line 26
    if-ne v0, v1, :cond_2

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 29
    .line 30
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    if-eqz v0, :cond_1

    .line 43
    .line 44
    new-instance v0, LTv/a$g;

    .line 45
    .line 46
    sget-object v1, Lorg/xbet/games_section/api/models/GameBonus;->Companion:Lorg/xbet/games_section/api/models/GameBonus$a;

    .line 47
    .line 48
    invoke-virtual {v1}, Lorg/xbet/games_section/api/models/GameBonus$a;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    invoke-direct {v0, v1}, LTv/a$g;-><init>(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 53
    .line 54
    .line 55
    invoke-direct {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->Q3(LTv/d;)V

    .line 56
    .line 57
    .line 58
    :cond_1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 59
    .line 60
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->t(Lig/a;)V

    .line 61
    .line 62
    .line 63
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x1:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 64
    .line 65
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 66
    .line 67
    invoke-virtual {p1, v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->x(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 68
    .line 69
    .line 70
    :cond_2
    :goto_0
    return-void
.end method

.method public final q4()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->p4()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->g4(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final r4()V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->p4()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    move-object v2, v1

    .line 16
    check-cast v2, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 17
    .line 18
    const/4 v3, 0x2

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    invoke-static {v2, v5, v5, v3, v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    if-eqz v1, :cond_0

    .line 30
    .line 31
    return-void
.end method

.method public final s4(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lig/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Lig/a;

    .line 27
    .line 28
    invoke-virtual {v1}, Lig/a;->f()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$c;

    .line 37
    .line 38
    invoke-direct {p1, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$c;-><init>(Ljava/util/List;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final t4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/h;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/african_roulette/presentation/game/h;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$6;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$6;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final u4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/i;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/african_roulette/presentation/game/i;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$4;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$4;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final v4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/k;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/african_roulette/presentation/game/k;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$8;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$8;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$c;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final w4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/j;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/african_roulette/presentation/game/j;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final x4(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/l;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/african_roulette/presentation/game/l;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$10;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$send$10;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$f;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method
