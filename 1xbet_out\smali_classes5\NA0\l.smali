.class public final LNA0/l;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u0004\u0018\u00010\u0004*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LYA0/a;",
        "",
        "LdB0/a;",
        "sportModelList",
        "LVA0/a;",
        "a",
        "(LYA0/a;Ljava/util/List;)LVA0/a;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LYA0/a;Ljava/util/List;)LVA0/a;
    .locals 22
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LYA0/a;",
            "Ljava/util/List<",
            "LdB0/a;",
            ">;)",
            "LVA0/a;"
        }
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->SINGLE_TEAM:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eq v0, v1, :cond_11

    .line 9
    .line 10
    invoke-virtual/range {p0 .. p0}, LYA0/a;->v()J

    .line 11
    .line 12
    .line 13
    move-result-wide v0

    .line 14
    const-wide/16 v3, 0x1

    .line 15
    .line 16
    cmp-long v5, v0, v3

    .line 17
    .line 18
    if-eqz v5, :cond_11

    .line 19
    .line 20
    invoke-static/range {p0 .. p0}, LMA0/b;->c(LYA0/a;)Lkotlin/Pair;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {v0}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    move-object v9, v1

    .line 29
    check-cast v9, Ljava/lang/String;

    .line 30
    .line 31
    invoke-virtual {v0}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    move-object v12, v0

    .line 36
    check-cast v12, Ljava/lang/String;

    .line 37
    .line 38
    invoke-interface/range {p1 .. p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_1

    .line 47
    .line 48
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    move-object v3, v1

    .line 53
    check-cast v3, LdB0/a;

    .line 54
    .line 55
    invoke-virtual {v3}, LdB0/a;->a()J

    .line 56
    .line 57
    .line 58
    move-result-wide v3

    .line 59
    invoke-virtual/range {p0 .. p0}, LYA0/a;->v()J

    .line 60
    .line 61
    .line 62
    move-result-wide v5

    .line 63
    cmp-long v7, v3, v5

    .line 64
    .line 65
    if-nez v7, :cond_0

    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    move-object v1, v2

    .line 69
    :goto_0
    check-cast v1, LdB0/a;

    .line 70
    .line 71
    if-eqz v1, :cond_2

    .line 72
    .line 73
    invoke-virtual {v1}, LdB0/a;->c()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v2

    .line 77
    :cond_2
    const-string v0, ""

    .line 78
    .line 79
    if-nez v2, :cond_3

    .line 80
    .line 81
    move-object v4, v0

    .line 82
    goto :goto_1

    .line 83
    :cond_3
    move-object v4, v2

    .line 84
    :goto_1
    new-instance v3, LVA0/a;

    .line 85
    .line 86
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    sget-object v2, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->HOSTS_VS_GUESTS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 91
    .line 92
    if-ne v1, v2, :cond_5

    .line 93
    .line 94
    :cond_4
    const-wide/16 v7, 0x0

    .line 95
    .line 96
    goto :goto_2

    .line 97
    :cond_5
    invoke-virtual/range {p0 .. p0}, LYA0/a;->F()Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object v1

    .line 105
    check-cast v1, Ljava/lang/Long;

    .line 106
    .line 107
    if-eqz v1, :cond_4

    .line 108
    .line 109
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 110
    .line 111
    .line 112
    move-result-wide v7

    .line 113
    :goto_2
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    if-ne v1, v2, :cond_7

    .line 118
    .line 119
    :cond_6
    const-wide/16 v10, 0x0

    .line 120
    .line 121
    goto :goto_3

    .line 122
    :cond_7
    invoke-virtual/range {p0 .. p0}, LYA0/a;->I()Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    check-cast v1, Ljava/lang/Long;

    .line 131
    .line 132
    if-eqz v1, :cond_6

    .line 133
    .line 134
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 135
    .line 136
    .line 137
    move-result-wide v10

    .line 138
    :goto_3
    sget-object v1, LDX0/e;->a:LDX0/e;

    .line 139
    .line 140
    invoke-virtual/range {p0 .. p0}, LYA0/a;->D()Ljava/util/List;

    .line 141
    .line 142
    .line 143
    move-result-object v13

    .line 144
    invoke-static {v13}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object v13

    .line 148
    check-cast v13, Ljava/lang/String;

    .line 149
    .line 150
    if-nez v13, :cond_8

    .line 151
    .line 152
    move-object v13, v0

    .line 153
    :cond_8
    invoke-virtual/range {p0 .. p0}, LYA0/a;->F()Ljava/util/List;

    .line 154
    .line 155
    .line 156
    move-result-object v14

    .line 157
    invoke-static {v14}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 158
    .line 159
    .line 160
    move-result-object v14

    .line 161
    check-cast v14, Ljava/lang/Long;

    .line 162
    .line 163
    if-eqz v14, :cond_9

    .line 164
    .line 165
    invoke-virtual {v14}, Ljava/lang/Long;->longValue()J

    .line 166
    .line 167
    .line 168
    move-result-wide v14

    .line 169
    goto :goto_4

    .line 170
    :cond_9
    const-wide/16 v14, 0x0

    .line 171
    .line 172
    :goto_4
    invoke-virtual {v1, v13, v14, v15}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v13

    .line 176
    invoke-virtual/range {p0 .. p0}, LYA0/a;->D()Ljava/util/List;

    .line 177
    .line 178
    .line 179
    move-result-object v14

    .line 180
    const/4 v15, 0x1

    .line 181
    invoke-static {v14, v15}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 182
    .line 183
    .line 184
    move-result-object v14

    .line 185
    check-cast v14, Ljava/lang/String;

    .line 186
    .line 187
    if-nez v14, :cond_a

    .line 188
    .line 189
    move-object v14, v0

    .line 190
    :cond_a
    invoke-virtual/range {p0 .. p0}, LYA0/a;->F()Ljava/util/List;

    .line 191
    .line 192
    .line 193
    move-result-object v5

    .line 194
    invoke-static {v5, v15}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 195
    .line 196
    .line 197
    move-result-object v5

    .line 198
    check-cast v5, Ljava/lang/Long;

    .line 199
    .line 200
    if-eqz v5, :cond_b

    .line 201
    .line 202
    invoke-virtual {v5}, Ljava/lang/Long;->longValue()J

    .line 203
    .line 204
    .line 205
    move-result-wide v5

    .line 206
    goto :goto_5

    .line 207
    :cond_b
    const-wide/16 v5, 0x0

    .line 208
    .line 209
    :goto_5
    invoke-virtual {v1, v14, v5, v6}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 210
    .line 211
    .line 212
    move-result-object v5

    .line 213
    invoke-virtual/range {p0 .. p0}, LYA0/a;->G()Ljava/util/List;

    .line 214
    .line 215
    .line 216
    move-result-object v6

    .line 217
    invoke-static {v6}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 218
    .line 219
    .line 220
    move-result-object v6

    .line 221
    check-cast v6, Ljava/lang/String;

    .line 222
    .line 223
    if-nez v6, :cond_c

    .line 224
    .line 225
    move-object v6, v0

    .line 226
    :cond_c
    invoke-virtual/range {p0 .. p0}, LYA0/a;->I()Ljava/util/List;

    .line 227
    .line 228
    .line 229
    move-result-object v14

    .line 230
    invoke-static {v14}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 231
    .line 232
    .line 233
    move-result-object v14

    .line 234
    check-cast v14, Ljava/lang/Long;

    .line 235
    .line 236
    if-eqz v14, :cond_d

    .line 237
    .line 238
    invoke-virtual {v14}, Ljava/lang/Long;->longValue()J

    .line 239
    .line 240
    .line 241
    move-result-wide v18

    .line 242
    move-object v14, v3

    .line 243
    move-object/from16 p1, v4

    .line 244
    .line 245
    move-wide/from16 v3, v18

    .line 246
    .line 247
    goto :goto_6

    .line 248
    :cond_d
    move-object v14, v3

    .line 249
    move-object/from16 p1, v4

    .line 250
    .line 251
    const-wide/16 v3, 0x0

    .line 252
    .line 253
    :goto_6
    invoke-virtual {v1, v6, v3, v4}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 254
    .line 255
    .line 256
    move-result-object v3

    .line 257
    invoke-virtual/range {p0 .. p0}, LYA0/a;->G()Ljava/util/List;

    .line 258
    .line 259
    .line 260
    move-result-object v4

    .line 261
    invoke-static {v4, v15}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 262
    .line 263
    .line 264
    move-result-object v4

    .line 265
    check-cast v4, Ljava/lang/String;

    .line 266
    .line 267
    if-nez v4, :cond_e

    .line 268
    .line 269
    goto :goto_7

    .line 270
    :cond_e
    move-object v0, v4

    .line 271
    :goto_7
    invoke-virtual/range {p0 .. p0}, LYA0/a;->I()Ljava/util/List;

    .line 272
    .line 273
    .line 274
    move-result-object v4

    .line 275
    invoke-static {v4, v15}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 276
    .line 277
    .line 278
    move-result-object v4

    .line 279
    check-cast v4, Ljava/lang/Long;

    .line 280
    .line 281
    if-eqz v4, :cond_f

    .line 282
    .line 283
    invoke-virtual {v4}, Ljava/lang/Long;->longValue()J

    .line 284
    .line 285
    .line 286
    move-result-wide v16

    .line 287
    move-object v6, v3

    .line 288
    move-wide/from16 v3, v16

    .line 289
    .line 290
    goto :goto_8

    .line 291
    :cond_f
    move-object v6, v3

    .line 292
    const-wide/16 v3, 0x0

    .line 293
    .line 294
    :goto_8
    invoke-virtual {v1, v0, v3, v4}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 299
    .line 300
    .line 301
    move-result-object v1

    .line 302
    if-ne v1, v2, :cond_10

    .line 303
    .line 304
    :goto_9
    move-object/from16 v4, p1

    .line 305
    .line 306
    move-object v3, v14

    .line 307
    move-object v14, v0

    .line 308
    move-wide/from16 v20, v10

    .line 309
    .line 310
    move-object v11, v5

    .line 311
    move-object v10, v13

    .line 312
    move-object v13, v6

    .line 313
    move-wide v5, v7

    .line 314
    move-wide/from16 v7, v20

    .line 315
    .line 316
    goto :goto_a

    .line 317
    :cond_10
    const/4 v15, 0x0

    .line 318
    goto :goto_9

    .line 319
    :goto_a
    invoke-direct/range {v3 .. v15}, LVA0/a;-><init>(Ljava/lang/String;JJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 320
    .line 321
    .line 322
    move-object v14, v3

    .line 323
    return-object v14

    .line 324
    :cond_11
    return-object v2
.end method
