.class public final LC31/M;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LC31/M;->a:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;

    .line 5
    .line 6
    iput-object p2, p0, LC31/M;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 7
    .line 8
    iput-object p3, p0, LC31/M;->c:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 9
    .line 10
    iput-object p4, p0, LC31/M;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 11
    .line 12
    iput-object p5, p0, LC31/M;->e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;

    .line 13
    .line 14
    return-void
.end method

.method public static a(Landroid/view/View;)LC31/M;
    .locals 8
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, Lm31/d;->cellLeft:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v4, v1

    .line 8
    check-cast v4, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 9
    .line 10
    if-eqz v4, :cond_0

    .line 11
    .line 12
    sget v0, Lm31/d;->cellMiddle:I

    .line 13
    .line 14
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v5, v1

    .line 19
    check-cast v5, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 20
    .line 21
    if-eqz v5, :cond_0

    .line 22
    .line 23
    sget v0, Lm31/d;->cellRight:I

    .line 24
    .line 25
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    move-object v6, v1

    .line 30
    check-cast v6, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 31
    .line 32
    if-eqz v6, :cond_0

    .line 33
    .line 34
    move-object v3, p0

    .line 35
    check-cast v3, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;

    .line 36
    .line 37
    new-instance v2, LC31/M;

    .line 38
    .line 39
    move-object v7, v3

    .line 40
    invoke-direct/range {v2 .. v7}, LC31/M;-><init>(Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;)V

    .line 41
    .line 42
    .line 43
    return-object v2

    .line 44
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    new-instance v0, Ljava/lang/NullPointerException;

    .line 53
    .line 54
    const-string v1, "Missing required view with ID: "

    .line 55
    .line 56
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw v0
.end method

.method public static c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/M;
    .locals 2
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, Lm31/e;->item_sport_feeds_cell_sport_medium_clear:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p0, v0, p1, v1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 5
    .line 6
    .line 7
    move-result-object p0

    .line 8
    if-eqz p2, :cond_0

    .line 9
    .line 10
    invoke-virtual {p1, p0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 11
    .line 12
    .line 13
    :cond_0
    invoke-static {p0}, LC31/M;->a(Landroid/view/View;)LC31/M;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    return-object p0
.end method


# virtual methods
.method public b()Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LC31/M;->a:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LC31/M;->b()Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMediumClear;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
