.class public final Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0003\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0017\u0010\t\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\n\u001a\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;",
        "type",
        "",
        "c",
        "(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)I",
        "SPORT_PLACEHOLDER_RES",
        "I",
        "b",
        "()I",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;-><init>()V

    return-void
.end method

.method public static final synthetic a(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;->c(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method


# virtual methods
.method public final b()I
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->k()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public final c(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p1, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p1, v0, :cond_0

    .line 20
    .line 21
    sget p1, Lm31/f;->Widget_SportCell_SportCellLeft_ActionIcon:I

    .line 22
    .line 23
    return p1

    .line 24
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    sget p1, Lm31/f;->Widget_SportCell_SportCellLeft_ChampionshipIcon:I

    .line 31
    .line 32
    return p1

    .line 33
    :cond_2
    sget p1, Lm31/f;->Widget_SportCell_SportCellLeft_BackgroundIcon:I

    .line 34
    .line 35
    return p1

    .line 36
    :cond_3
    sget p1, Lm31/f;->Widget_SportCell_SportCellLeft_Icon:I

    .line 37
    .line 38
    return p1
.end method
