.class public final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c0\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0006\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u00085\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u00e3\u0001\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u00a2\u0006\u0004\u00088\u00109J\u000f\u0010;\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u000f\u0010=\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008=\u0010<J\u000f\u0010>\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008>\u0010<J\u0017\u0010A\u001a\u00020:2\u0006\u0010@\u001a\u00020?H\u0002\u00a2\u0006\u0004\u0008A\u0010BJ\u000f\u0010C\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008C\u0010<J\u0017\u0010F\u001a\u00020:2\u0006\u0010E\u001a\u00020DH\u0002\u00a2\u0006\u0004\u0008F\u0010GJ\u000f\u0010H\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008H\u0010<J\u000f\u0010I\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008I\u0010<J\u000f\u0010J\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008J\u0010<J\u000f\u0010K\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008K\u0010<J\u000f\u0010L\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008L\u0010<J\u001e\u0010P\u001a\u00020:2\u000c\u0010O\u001a\u0008\u0012\u0004\u0012\u00020N0MH\u0082@\u00a2\u0006\u0004\u0008P\u0010QJ\u0017\u0010T\u001a\u00020:2\u0006\u0010S\u001a\u00020RH\u0002\u00a2\u0006\u0004\u0008T\u0010UJ\u0017\u0010X\u001a\u00020:2\u0006\u0010W\u001a\u00020VH\u0002\u00a2\u0006\u0004\u0008X\u0010YJ\u0017\u0010Z\u001a\u00020:2\u0006\u0010E\u001a\u00020DH\u0002\u00a2\u0006\u0004\u0008Z\u0010GJ\u0013\u0010\\\u001a\u00020:*\u00020[H\u0002\u00a2\u0006\u0004\u0008\\\u0010]J\u0013\u0010_\u001a\u00020:*\u00020^H\u0002\u00a2\u0006\u0004\u0008_\u0010`J\u0013\u0010b\u001a\u00020:*\u00020aH\u0002\u00a2\u0006\u0004\u0008b\u0010cJ\u0013\u0010e\u001a\u00020:*\u00020dH\u0002\u00a2\u0006\u0004\u0008e\u0010fJ\u0013\u0010h\u001a\u00020:*\u00020gH\u0002\u00a2\u0006\u0004\u0008h\u0010iJ\u0015\u0010k\u001a\u0008\u0012\u0004\u0012\u00020d0jH\u0000\u00a2\u0006\u0004\u0008k\u0010lJ\u0015\u0010m\u001a\u0008\u0012\u0004\u0012\u00020a0jH\u0000\u00a2\u0006\u0004\u0008m\u0010lJ\u0015\u0010n\u001a\u0008\u0012\u0004\u0012\u00020^0jH\u0000\u00a2\u0006\u0004\u0008n\u0010lJ\u0015\u0010o\u001a\u0008\u0012\u0004\u0012\u00020[0jH\u0000\u00a2\u0006\u0004\u0008o\u0010lJ\u0015\u0010q\u001a\u0008\u0012\u0004\u0012\u00020p0jH\u0000\u00a2\u0006\u0004\u0008q\u0010lJ\u0015\u0010r\u001a\u0008\u0012\u0004\u0012\u00020g0jH\u0000\u00a2\u0006\u0004\u0008r\u0010lJ\u0015\u0010t\u001a\u00020:2\u0006\u0010s\u001a\u00020N\u00a2\u0006\u0004\u0008t\u0010uJ\r\u0010v\u001a\u00020:\u00a2\u0006\u0004\u0008v\u0010<J\u000f\u0010w\u001a\u00020:H\u0000\u00a2\u0006\u0004\u0008w\u0010<J\u0017\u0010y\u001a\u00020:2\u0006\u0010x\u001a\u00020RH\u0000\u00a2\u0006\u0004\u0008y\u0010UJ\u000f\u0010z\u001a\u00020RH\u0000\u00a2\u0006\u0004\u0008z\u0010{J\u0015\u0010~\u001a\u00020:2\u0006\u0010}\u001a\u00020|\u00a2\u0006\u0004\u0008~\u0010\u007fJ\u000f\u0010\u0080\u0001\u001a\u00020:\u00a2\u0006\u0005\u0008\u0080\u0001\u0010<J\u000f\u0010\u0081\u0001\u001a\u00020:\u00a2\u0006\u0005\u0008\u0081\u0001\u0010<J\u001c\u0010\u0084\u0001\u001a\u00020:2\u0008\u0010\u0083\u0001\u001a\u00030\u0082\u0001H\u0000\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0085\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0086\u0001\u0010\u0087\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u0089\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008a\u0001\u0010\u008b\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008c\u0001\u0010\u008d\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u008f\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u0091\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0092\u0001\u0010\u0093\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0095\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u0097\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u0099\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u009d\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00a1\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a4\u0001\u0010\u00a5\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a9\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001R\u0016\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b0\u0001\u0010\u00b1\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b2\u0001\u0010\u00b3\u0001R\u0016\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0001\u0010\u00b5\u0001R\u0016\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b7\u0001R\u001c\u0010\u00bb\u0001\u001a\u0005\u0018\u00010\u00b8\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001R\u001c\u0010\u00bd\u0001\u001a\u0005\u0018\u00010\u00b8\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00ba\u0001R\u0019\u0010\u00c0\u0001\u001a\u00020R8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00be\u0001\u0010\u00bf\u0001R\u001e\u0010\u00c4\u0001\u001a\t\u0012\u0004\u0012\u00020d0\u00c1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0001\u0010\u00c3\u0001R\u001e\u0010\u00c6\u0001\u001a\t\u0012\u0004\u0012\u00020a0\u00c1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c5\u0001\u0010\u00c3\u0001R\u001e\u0010\u00c8\u0001\u001a\t\u0012\u0004\u0012\u00020^0\u00c1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c7\u0001\u0010\u00c3\u0001R\u001e\u0010\u00ca\u0001\u001a\t\u0012\u0004\u0012\u00020[0\u00c1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00c3\u0001R\u001e\u0010\u00ce\u0001\u001a\t\u0012\u0004\u0012\u00020p0\u00cb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cc\u0001\u0010\u00cd\u0001R\u001e\u0010\u00d0\u0001\u001a\t\u0012\u0004\u0012\u00020g0\u00c1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cf\u0001\u0010\u00c3\u0001\u00a8\u0006\u00d1\u0001"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwX0/c;",
        "router",
        "Lorg/xbet/core/domain/usecases/u;",
        "observeCommandUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "updateLastBetForMultiChoiceGameScenario",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "startGameIfPossibleScenario",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "getGameStateUseCase",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "getBonusUseCase",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "setGameInProgressUseCase",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "onBetSetScenario",
        "Lez0/a;",
        "addNewSpinBetUseCase",
        "Lez0/b;",
        "clearSpinAndWinUseCase",
        "Lez0/c;",
        "getAllSpinBetsSumUseCase",
        "Lez0/d;",
        "getCurrentSpinGameUseCase",
        "Lez0/e;",
        "getSelectedSpinBetUseCase",
        "Lez0/f;",
        "getSpinBetListUseCase",
        "Lez0/g;",
        "playSpinAndWinUseCase",
        "Lez0/h;",
        "removeAllSpinBetsUseCase",
        "Lez0/i;",
        "removeSpinBetScenario",
        "Lez0/j;",
        "setCurrentSpinGameUseCase",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "isGameInProgressUseCase",
        "Lez0/k;",
        "updateSelectedBetUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "LWv/b;",
        "getConnectionStatusUseCase",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "getBetSumUseCase",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "getCurrencyUseCase",
        "Lorg/xbet/core/domain/usecases/bet/l;",
        "getInstantBetVisibilityUseCase",
        "<init>",
        "(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lez0/a;Lez0/b;Lez0/c;Lez0/d;Lez0/e;Lez0/f;Lez0/g;Lez0/h;Lez0/i;Lez0/j;Lorg/xbet/core/domain/usecases/game_state/h;Lez0/k;Lm8/a;LWv/b;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/core/domain/usecases/bet/l;)V",
        "",
        "o4",
        "()V",
        "L4",
        "M4",
        "",
        "betSum",
        "p4",
        "(D)V",
        "y4",
        "LTv/d;",
        "command",
        "l4",
        "(LTv/d;)V",
        "v4",
        "e4",
        "n4",
        "z4",
        "q4",
        "",
        "Ldz0/a;",
        "betsList",
        "t4",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "instantBetAllowed",
        "O4",
        "(Z)V",
        "",
        "throwable",
        "m4",
        "(Ljava/lang/Throwable;)V",
        "b4",
        "Lorg/xbet/spin_and_win/presentation/game/s;",
        "F4",
        "(Lorg/xbet/spin_and_win/presentation/game/s;)V",
        "Lorg/xbet/spin_and_win/presentation/game/b;",
        "C4",
        "(Lorg/xbet/spin_and_win/presentation/game/b;)V",
        "Lorg/xbet/spin_and_win/presentation/game/a;",
        "B4",
        "(Lorg/xbet/spin_and_win/presentation/game/a;)V",
        "Lorg/xbet/spin_and_win/presentation/game/c;",
        "D4",
        "(Lorg/xbet/spin_and_win/presentation/game/c;)V",
        "Lorg/xbet/spin_and_win/presentation/game/r;",
        "E4",
        "(Lorg/xbet/spin_and_win/presentation/game/r;)V",
        "Lkotlinx/coroutines/flow/e;",
        "h4",
        "()Lkotlinx/coroutines/flow/e;",
        "f4",
        "g4",
        "k4",
        "Lorg/xbet/spin_and_win/presentation/game/q;",
        "i4",
        "j4",
        "bet",
        "x4",
        "(Ldz0/a;)V",
        "w4",
        "A4",
        "initialized",
        "c4",
        "d4",
        "()Z",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "betType",
        "s4",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V",
        "N4",
        "r4",
        "",
        "leftMargin",
        "u4",
        "(I)V",
        "v1",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "x1",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "y1",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "F1",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "H1",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "I1",
        "Lorg/xbet/core/domain/usecases/d;",
        "P1",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "S1",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "V1",
        "Lez0/a;",
        "b2",
        "Lez0/b;",
        "v2",
        "Lez0/c;",
        "x2",
        "Lez0/d;",
        "y2",
        "Lez0/e;",
        "F2",
        "Lez0/f;",
        "H2",
        "Lez0/g;",
        "I2",
        "Lez0/h;",
        "P2",
        "Lez0/i;",
        "S2",
        "Lez0/j;",
        "V2",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "X2",
        "Lez0/k;",
        "F3",
        "Lm8/a;",
        "H3",
        "LWv/b;",
        "I3",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "S3",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "H4",
        "Lorg/xbet/core/domain/usecases/bet/l;",
        "Lkotlinx/coroutines/x0;",
        "X4",
        "Lkotlinx/coroutines/x0;",
        "observeBetsJob",
        "v5",
        "makeBetJob",
        "w5",
        "Z",
        "viewsInitialized",
        "Lkotlinx/coroutines/flow/U;",
        "x5",
        "Lkotlinx/coroutines/flow/U;",
        "oneExecutionStateFlow",
        "y5",
        "betInfoStateFlow",
        "z5",
        "gameFieldStateFlow",
        "A5",
        "wheelStateFlow",
        "Lkotlinx/coroutines/flow/V;",
        "B5",
        "Lkotlinx/coroutines/flow/V;",
        "viewStateFlow",
        "C5",
        "wheelParamsStateFlow",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final A5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/spin_and_win/presentation/game/s;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/spin_and_win/presentation/game/q;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/spin_and_win/presentation/game/r;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:Lorg/xbet/core/domain/usecases/bonus/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lez0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lez0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:LWv/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lorg/xbet/core/domain/usecases/bet/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lez0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/core/domain/usecases/bet/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/core/domain/usecases/game_state/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lez0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/bet/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lez0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lez0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lorg/xbet/core/domain/usecases/game_state/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lez0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public X4:Lkotlinx/coroutines/x0;

.field public final b2:Lez0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/core/domain/usecases/game_info/H;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lez0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v5:Lkotlinx/coroutines/x0;

.field public w5:Z

.field public final x1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lez0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/spin_and_win/presentation/game/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/core/domain/usecases/game_info/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lez0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/spin_and_win/presentation/game/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/spin_and_win/presentation/game/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lez0/a;Lez0/b;Lez0/c;Lez0/d;Lez0/e;Lez0/f;Lez0/g;Lez0/h;Lez0/i;Lez0/j;Lorg/xbet/core/domain/usecases/game_state/h;Lez0/k;Lm8/a;LWv/b;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/core/domain/usecases/bet/l;)V
    .locals 0
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/game_info/H;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/core/domain/usecases/game_info/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/core/domain/usecases/bonus/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/core/domain/usecases/game_state/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/core/domain/usecases/bet/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lez0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lez0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lez0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lez0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lez0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lez0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lez0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lez0/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lez0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lez0/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lorg/xbet/core/domain/usecases/game_state/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lez0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LWv/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lorg/xbet/core/domain/usecases/bet/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lorg/xbet/core/domain/usecases/bet/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p3, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v1:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 5
    .line 6
    iput-object p4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 7
    .line 8
    iput-object p5, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 9
    .line 10
    iput-object p6, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 11
    .line 12
    iput-object p7, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 13
    .line 14
    iput-object p8, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I1:Lorg/xbet/core/domain/usecases/d;

    .line 15
    .line 16
    iput-object p9, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->P1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 17
    .line 18
    iput-object p10, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S1:Lorg/xbet/core/domain/usecases/bet/o;

    .line 19
    .line 20
    iput-object p11, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V1:Lez0/a;

    .line 21
    .line 22
    iput-object p12, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->b2:Lez0/b;

    .line 23
    .line 24
    iput-object p13, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v2:Lez0/c;

    .line 25
    .line 26
    iput-object p14, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x2:Lez0/d;

    .line 27
    .line 28
    iput-object p15, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y2:Lez0/e;

    .line 29
    .line 30
    move-object/from16 p1, p16

    .line 31
    .line 32
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F2:Lez0/f;

    .line 33
    .line 34
    move-object/from16 p1, p17

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H2:Lez0/g;

    .line 37
    .line 38
    move-object/from16 p1, p18

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I2:Lez0/h;

    .line 41
    .line 42
    move-object/from16 p1, p19

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->P2:Lez0/i;

    .line 45
    .line 46
    move-object/from16 p1, p20

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S2:Lez0/j;

    .line 49
    .line 50
    move-object/from16 p1, p21

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 53
    .line 54
    move-object/from16 p1, p22

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X2:Lez0/k;

    .line 57
    .line 58
    move-object/from16 p1, p23

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 61
    .line 62
    move-object/from16 p1, p24

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H3:LWv/b;

    .line 65
    .line 66
    move-object/from16 p1, p25

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I3:Lorg/xbet/core/domain/usecases/bet/d;

    .line 69
    .line 70
    move-object/from16 p1, p26

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S3:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 73
    .line 74
    move-object/from16 p1, p27

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H4:Lorg/xbet/core/domain/usecases/bet/l;

    .line 77
    .line 78
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 83
    .line 84
    const/4 p1, 0x0

    .line 85
    const/4 p3, 0x0

    .line 86
    const/4 p4, 0x7

    .line 87
    invoke-static {p1, p1, p3, p4, p3}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 88
    .line 89
    .line 90
    move-result-object p5

    .line 91
    iput-object p5, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y5:Lkotlinx/coroutines/flow/U;

    .line 92
    .line 93
    invoke-static {p1, p1, p3, p4, p3}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 94
    .line 95
    .line 96
    move-result-object p5

    .line 97
    iput-object p5, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->z5:Lkotlinx/coroutines/flow/U;

    .line 98
    .line 99
    invoke-static {p1, p1, p3, p4, p3}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 100
    .line 101
    .line 102
    move-result-object p4

    .line 103
    iput-object p4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->A5:Lkotlinx/coroutines/flow/U;

    .line 104
    .line 105
    new-instance p4, Lorg/xbet/spin_and_win/presentation/game/q;

    .line 106
    .line 107
    const/4 p5, 0x3

    .line 108
    invoke-direct {p4, p1, p1, p5, p3}, Lorg/xbet/spin_and_win/presentation/game/q;-><init>(ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 109
    .line 110
    .line 111
    invoke-static {p4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 112
    .line 113
    .line 114
    move-result-object p4

    .line 115
    iput-object p4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 116
    .line 117
    sget-object p4, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 118
    .line 119
    const/4 p5, 0x2

    .line 120
    const/4 p6, 0x1

    .line 121
    invoke-static {p6, p1, p4, p5, p3}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 126
    .line 127
    invoke-virtual {p2}, Lorg/xbet/core/domain/usecases/u;->a()Lkotlinx/coroutines/flow/e;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    new-instance p2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$1;

    .line 132
    .line 133
    invoke-direct {p2, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$1;-><init>(Ljava/lang/Object;)V

    .line 134
    .line 135
    .line 136
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    new-instance p2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$2;

    .line 141
    .line 142
    invoke-direct {p2, p0, p3}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 143
    .line 144
    .line 145
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 150
    .line 151
    .line 152
    move-result-object p2

    .line 153
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 154
    .line 155
    .line 156
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->q4()V

    .line 157
    .line 158
    .line 159
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->z5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v2:Lez0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S3:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x2:Lez0/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y2:Lez0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F2:Lez0/f;

    .line 2
    .line 3
    return-object p0
.end method

.method private static final G4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method private static final H4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/bet/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S1:Lorg/xbet/core/domain/usecases/bet/o;

    .line 2
    .line 3
    return-object p0
.end method

.method private static final I4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method private static final J4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H2:Lez0/g;

    .line 2
    .line 3
    return-object p0
.end method

.method private static final K4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I2:Lez0/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S2:Lez0/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/game_info/H;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v1:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 2
    .line 3
    return-object p0
.end method

.method private final O4(Z)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    :cond_1
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    move-object v2, v1

    .line 25
    check-cast v2, Lorg/xbet/spin_and_win/presentation/game/q;

    .line 26
    .line 27
    const/4 v3, 0x1

    .line 28
    const/4 v4, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    invoke-static {v2, v5, p1, v3, v4}, Lorg/xbet/spin_and_win/presentation/game/q;->b(Lorg/xbet/spin_and_win/presentation/game/q;ZZILjava/lang/Object;)Lorg/xbet/spin_and_win/presentation/game/q;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    if-eqz v1, :cond_1

    .line 39
    .line 40
    :goto_0
    return-void
.end method

.method public static final synthetic P3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->A5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic S3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->m4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic T3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->q4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic U3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->t4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic V3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->z4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic W3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B4(Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic X3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Y3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->D4(Lorg/xbet/spin_and_win/presentation/game/c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Z3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/s;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F4(Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a4(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->M4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final b4(LTv/d;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$addCommand$1;->INSTANCE:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$addCommand$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$addCommand$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$addCommand$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;LTv/d;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final e4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private final l4(LTv/d;)V
    .locals 9

    .line 1
    instance-of v0, p1, LTv/a$o;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$2;

    .line 10
    .line 11
    invoke-direct {v2, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$2;-><init>(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 15
    .line 16
    invoke-interface {p1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    new-instance v6, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;

    .line 21
    .line 22
    const/4 p1, 0x0

    .line 23
    invoke-direct {v6, p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v7, 0xa

    .line 27
    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I3:Lorg/xbet/core/domain/usecases/bet/d;

    .line 35
    .line 36
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 37
    .line 38
    .line 39
    move-result-wide v0

    .line 40
    invoke-direct {p0, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->p4(D)V

    .line 41
    .line 42
    .line 43
    return-void

    .line 44
    :cond_0
    instance-of v0, p1, LTv/a$w;

    .line 45
    .line 46
    if-eqz v0, :cond_1

    .line 47
    .line 48
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v4()V

    .line 49
    .line 50
    .line 51
    return-void

    .line 52
    :cond_1
    instance-of v0, p1, LTv/a$p;

    .line 53
    .line 54
    if-eqz v0, :cond_2

    .line 55
    .line 56
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y4()V

    .line 57
    .line 58
    .line 59
    return-void

    .line 60
    :cond_2
    instance-of v0, p1, LTv/a$g;

    .line 61
    .line 62
    if-eqz v0, :cond_3

    .line 63
    .line 64
    check-cast p1, LTv/a$g;

    .line 65
    .line 66
    invoke-virtual {p1}, LTv/a$g;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    sget-object v0, Lorg/xbet/games_section/api/models/GameBonusType;->NOTHING:Lorg/xbet/games_section/api/models/GameBonusType;

    .line 75
    .line 76
    if-eq p1, v0, :cond_6

    .line 77
    .line 78
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y4()V

    .line 79
    .line 80
    .line 81
    return-void

    .line 82
    :cond_3
    instance-of v0, p1, LTv/a$r;

    .line 83
    .line 84
    if-eqz v0, :cond_4

    .line 85
    .line 86
    check-cast p1, LTv/a$r;

    .line 87
    .line 88
    invoke-virtual {p1}, LTv/a$r;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 97
    .line 98
    .line 99
    move-result p1

    .line 100
    if-nez p1, :cond_6

    .line 101
    .line 102
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y4()V

    .line 103
    .line 104
    .line 105
    return-void

    .line 106
    :cond_4
    instance-of v0, p1, LTv/a$j;

    .line 107
    .line 108
    if-eqz v0, :cond_5

    .line 109
    .line 110
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->n4()V

    .line 111
    .line 112
    .line 113
    return-void

    .line 114
    :cond_5
    instance-of v0, p1, LTv/b$l;

    .line 115
    .line 116
    if-eqz v0, :cond_6

    .line 117
    .line 118
    check-cast p1, LTv/b$l;

    .line 119
    .line 120
    invoke-virtual {p1}, LTv/b$l;->a()Z

    .line 121
    .line 122
    .line 123
    move-result p1

    .line 124
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->O4(Z)V

    .line 125
    .line 126
    .line 127
    :cond_6
    return-void
.end method

.method private final m4(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleGameError$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final o4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$loadCurrentGame$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$loadCurrentGame$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$loadCurrentGame$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$loadCurrentGame$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4(D)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;DLkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static synthetic q3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->K4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final q4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F2:Lez0/f;

    .line 14
    .line 15
    invoke-virtual {v0}, Lez0/f;->a()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$observeBets$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$observeBets$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$observeBets$2;

    .line 30
    .line 31
    invoke-direct {v1, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$observeBets$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    return-void
.end method

.method public static synthetic r3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->G4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->J4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->l4(LTv/d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->u3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final v4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$1;

    .line 24
    .line 25
    invoke-direct {v3, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$1;-><init>(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    new-instance v7, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$play$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v5:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public static final synthetic w3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V1:Lez0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method private final y4()V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->b2:Lez0/b;

    .line 2
    .line 3
    invoke-virtual {v0}, Lez0/b;->a()V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/s$a;-><init>(Z)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F4(Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 13
    .line 14
    .line 15
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$d;

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    invoke-direct {v0, v2}, Lorg/xbet/spin_and_win/presentation/game/b$d;-><init>(Z)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 22
    .line 23
    .line 24
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 25
    .line 26
    invoke-direct {v0, v2}, Lorg/xbet/spin_and_win/presentation/game/a$b;-><init>(Z)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B4(Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 30
    .line 31
    .line 32
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$b;

    .line 33
    .line 34
    sget-object v3, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 35
    .line 36
    invoke-direct {v0, v3}, Lorg/xbet/spin_and_win/presentation/game/b$b;-><init>(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 43
    .line 44
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    move-object v4, v3

    .line 49
    check-cast v4, Lorg/xbet/spin_and_win/presentation/game/q;

    .line 50
    .line 51
    const/4 v5, 0x0

    .line 52
    invoke-static {v4, v1, v1, v2, v5}, Lorg/xbet/spin_and_win/presentation/game/q;->b(Lorg/xbet/spin_and_win/presentation/game/q;ZZILjava/lang/Object;)Lorg/xbet/spin_and_win/presentation/game/q;

    .line 53
    .line 54
    .line 55
    move-result-object v4

    .line 56
    invoke-interface {v0, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    if-eqz v3, :cond_0

    .line 61
    .line 62
    return-void
.end method

.method public static final synthetic z3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->I1:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final A4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    new-instance v0, LTv/a$r;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 20
    .line 21
    invoke-virtual {v1}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-direct {v0, v1}, LTv/a$r;-><init>(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 26
    .line 27
    .line 28
    invoke-direct {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->b4(LTv/d;)V

    .line 29
    .line 30
    .line 31
    :cond_0
    return-void
.end method

.method public final B4(Lorg/xbet/spin_and_win/presentation/game/a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/n;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/spin_and_win/presentation/game/n;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$6;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$6;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/a;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final C4(Lorg/xbet/spin_and_win/presentation/game/b;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/l;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/spin_and_win/presentation/game/l;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$4;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$4;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/b;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final D4(Lorg/xbet/spin_and_win/presentation/game/c;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/k;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/spin_and_win/presentation/game/k;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$8;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$8;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/c;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final E4(Lorg/xbet/spin_and_win/presentation/game/r;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/o;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/spin_and_win/presentation/game/o;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$10;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$10;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/r;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final F4(Lorg/xbet/spin_and_win/presentation/game/s;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/m;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/spin_and_win/presentation/game/m;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$send$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/s;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final L4()V
    .locals 10

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$d;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/b$d;-><init>(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 8
    .line 9
    .line 10
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 11
    .line 12
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/a$b;-><init>(Z)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B4(Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 16
    .line 17
    .line 18
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 19
    .line 20
    const/4 v1, 0x1

    .line 21
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/s$a;-><init>(Z)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F4(Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 25
    .line 26
    .line 27
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$showEndGameState$1;

    .line 32
    .line 33
    invoke-direct {v3, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$showEndGameState$1;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    new-instance v7, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$showEndGameState$2;

    .line 37
    .line 38
    const/4 v0, 0x0

    .line 39
    invoke-direct {v7, p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$showEndGameState$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 40
    .line 41
    .line 42
    const/16 v8, 0xe

    .line 43
    .line 44
    const/4 v9, 0x0

    .line 45
    const/4 v4, 0x0

    .line 46
    const/4 v5, 0x0

    .line 47
    const/4 v6, 0x0

    .line 48
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X4:Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    if-eqz v2, :cond_0

    .line 54
    .line 55
    invoke-static {v2, v0, v1, v0}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    :cond_0
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->q4()V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final M4()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/s$a;-><init>(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F4(Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 8
    .line 9
    .line 10
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/s$b;

    .line 11
    .line 12
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x2:Lez0/d;

    .line 13
    .line 14
    invoke-virtual {v2}, Lez0/d;->a()Ldz0/b;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    invoke-virtual {v2}, Ldz0/b;->e()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-static {v2}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    check-cast v2, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 27
    .line 28
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/presentation/game/s$b;-><init>(ZLorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F4(Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final N4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H3:LWv/b;

    .line 2
    .line 3
    invoke-virtual {v0}, LWv/b;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 11
    .line 12
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    :goto_0
    return-void

    .line 19
    :cond_1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->P1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 20
    .line 21
    const/4 v1, 0x1

    .line 22
    invoke-virtual {v0, v1}, Lorg/xbet/core/domain/usecases/game_state/l;->a(Z)V

    .line 23
    .line 24
    .line 25
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$startGameIfPossible$1;

    .line 30
    .line 31
    invoke-direct {v3, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$startGameIfPossible$1;-><init>(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    new-instance v7, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$startGameIfPossible$2;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {v7, p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$startGameIfPossible$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    const/16 v8, 0xa

    .line 47
    .line 48
    const/4 v9, 0x0

    .line 49
    const/4 v4, 0x0

    .line 50
    const/4 v6, 0x0

    .line 51
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final c4(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->w5:Z

    .line 2
    .line 3
    return-void
.end method

.method public final d4()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->w5:Z

    .line 2
    .line 3
    return v0
.end method

.method public final f4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->z5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/q;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/r;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/spin_and_win/presentation/game/s;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->A5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final r4()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->e4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final s4(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V
    .locals 2
    .param p1    # Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p1, v1}, Lorg/xbet/spin_and_win/presentation/game/b$a;-><init>(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X2:Lez0/k;

    .line 11
    .line 12
    invoke-virtual {v0, p1}, Lez0/k;->a(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 13
    .line 14
    .line 15
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-virtual {p1}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-eqz p1, :cond_0

    .line 30
    .line 31
    const-wide/16 v0, 0x0

    .line 32
    .line 33
    invoke-direct {p0, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->p4(D)V

    .line 34
    .line 35
    .line 36
    :cond_0
    return-void
.end method

.method public final t4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->D$0:D

    .line 39
    .line 40
    iget-boolean p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->Z$1:Z

    .line 41
    .line 42
    iget-boolean v4, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->Z$0:Z

    .line 43
    .line 44
    iget-object v5, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->L$1:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 47
    .line 48
    iget-object v0, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v0, Ljava/util/List;

    .line 51
    .line 52
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 57
    .line 58
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 59
    .line 60
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw p1

    .line 64
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    iget-object p2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 68
    .line 69
    invoke-virtual {p2}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 70
    .line 71
    .line 72
    move-result-object p2

    .line 73
    invoke-virtual {p2}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 74
    .line 75
    .line 76
    move-result-object p2

    .line 77
    invoke-virtual {p2}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 78
    .line 79
    .line 80
    move-result p2

    .line 81
    if-nez p2, :cond_3

    .line 82
    .line 83
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 84
    .line 85
    .line 86
    move-result v2

    .line 87
    if-nez v2, :cond_3

    .line 88
    .line 89
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S1:Lorg/xbet/core/domain/usecases/bet/o;

    .line 90
    .line 91
    iget-object v4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v2:Lez0/c;

    .line 92
    .line 93
    invoke-virtual {v4, p1}, Lez0/c;->a(Ljava/util/List;)D

    .line 94
    .line 95
    .line 96
    move-result-wide v4

    .line 97
    invoke-virtual {v2, v4, v5}, Lorg/xbet/core/domain/usecases/bet/o;->a(D)V

    .line 98
    .line 99
    .line 100
    :cond_3
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->v2:Lez0/c;

    .line 101
    .line 102
    invoke-virtual {v2, p1}, Lez0/c;->a(Ljava/util/List;)D

    .line 103
    .line 104
    .line 105
    move-result-wide v4

    .line 106
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->S3:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 107
    .line 108
    iput-object p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->L$0:Ljava/lang/Object;

    .line 109
    .line 110
    iput-object p0, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->L$1:Ljava/lang/Object;

    .line 111
    .line 112
    iput-boolean p2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->Z$0:Z

    .line 113
    .line 114
    iput-boolean p2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->Z$1:Z

    .line 115
    .line 116
    iput-wide v4, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->D$0:D

    .line 117
    .line 118
    iput v3, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$onWheelBetsChanged$1;->label:I

    .line 119
    .line 120
    invoke-virtual {v2, v0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    if-ne v0, v1, :cond_4

    .line 125
    .line 126
    return-object v1

    .line 127
    :cond_4
    move-wide v1, v4

    .line 128
    move-object v5, p0

    .line 129
    move v4, p2

    .line 130
    move-object p2, v0

    .line 131
    move-object v0, p1

    .line 132
    move p1, v4

    .line 133
    :goto_1
    check-cast p2, Ljava/lang/String;

    .line 134
    .line 135
    new-instance v6, Lorg/xbet/spin_and_win/presentation/game/a$a;

    .line 136
    .line 137
    invoke-direct {v6, p1, v1, v2, p2}, Lorg/xbet/spin_and_win/presentation/game/a$a;-><init>(ZDLjava/lang/String;)V

    .line 138
    .line 139
    .line 140
    invoke-virtual {v5, v6}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B4(Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 141
    .line 142
    .line 143
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 144
    .line 145
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    sget-object p2, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 150
    .line 151
    const/4 v1, 0x0

    .line 152
    if-ne p1, p2, :cond_5

    .line 153
    .line 154
    const/4 p1, 0x1

    .line 155
    goto :goto_2

    .line 156
    :cond_5
    const/4 p1, 0x0

    .line 157
    :goto_2
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 158
    .line 159
    .line 160
    move-result p2

    .line 161
    if-nez p2, :cond_6

    .line 162
    .line 163
    if-nez v4, :cond_6

    .line 164
    .line 165
    if-eqz p1, :cond_6

    .line 166
    .line 167
    goto :goto_3

    .line 168
    :cond_6
    const/4 v3, 0x0

    .line 169
    :goto_3
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 170
    .line 171
    :cond_7
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 172
    .line 173
    .line 174
    move-result-object p2

    .line 175
    move-object v2, p2

    .line 176
    check-cast v2, Lorg/xbet/spin_and_win/presentation/game/q;

    .line 177
    .line 178
    const/4 v4, 0x2

    .line 179
    const/4 v5, 0x0

    .line 180
    invoke-static {v2, v3, v1, v4, v5}, Lorg/xbet/spin_and_win/presentation/game/q;->b(Lorg/xbet/spin_and_win/presentation/game/q;ZZILjava/lang/Object;)Lorg/xbet/spin_and_win/presentation/game/q;

    .line 181
    .line 182
    .line 183
    move-result-object v2

    .line 184
    invoke-interface {p1, p2, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 185
    .line 186
    .line 187
    move-result p2

    .line 188
    if-eqz p2, :cond_7

    .line 189
    .line 190
    if-eqz v3, :cond_8

    .line 191
    .line 192
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->H4:Lorg/xbet/core/domain/usecases/bet/l;

    .line 193
    .line 194
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bet/l;->a()Z

    .line 195
    .line 196
    .line 197
    move-result p1

    .line 198
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->O4(Z)V

    .line 199
    .line 200
    .line 201
    :cond_8
    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/a$c;

    .line 202
    .line 203
    invoke-direct {p1, v0}, Lorg/xbet/spin_and_win/presentation/game/a$c;-><init>(Ljava/util/List;)V

    .line 204
    .line 205
    .line 206
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->B4(Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 207
    .line 208
    .line 209
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 210
    .line 211
    return-object p1
.end method

.method public final u4(I)V
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/r$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/r$a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->E4(Lorg/xbet/spin_and_win/presentation/game/r;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final w4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_2

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-eq v0, v1, :cond_1

    .line 20
    .line 21
    const/4 v1, 0x3

    .line 22
    if-ne v0, v1, :cond_0

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->L4()V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 29
    .line 30
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 31
    .line 32
    .line 33
    throw v0

    .line 34
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->L4()V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->e4()V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->o4()V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final x4(Ldz0/a;)V
    .locals 2
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->V2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 11
    .line 12
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sget-object v1, Lorg/xbet/core/domain/GameState;->DEFAULT:Lorg/xbet/core/domain/GameState;

    .line 17
    .line 18
    if-ne v0, v1, :cond_2

    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F1:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 21
    .line 22
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-eqz v0, :cond_1

    .line 35
    .line 36
    new-instance v0, LTv/a$g;

    .line 37
    .line 38
    sget-object v1, Lorg/xbet/games_section/api/models/GameBonus;->Companion:Lorg/xbet/games_section/api/models/GameBonus$a;

    .line 39
    .line 40
    invoke-virtual {v1}, Lorg/xbet/games_section/api/models/GameBonus$a;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-direct {v0, v1}, LTv/a$g;-><init>(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 45
    .line 46
    .line 47
    invoke-direct {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->b4(LTv/d;)V

    .line 48
    .line 49
    .line 50
    :cond_1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->P2:Lez0/i;

    .line 51
    .line 52
    invoke-virtual {v0, p1}, Lez0/i;->a(Ldz0/a;)V

    .line 53
    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X2:Lez0/k;

    .line 56
    .line 57
    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 58
    .line 59
    invoke-virtual {v0, v1}, Lez0/k;->a(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 60
    .line 61
    .line 62
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$b;

    .line 63
    .line 64
    invoke-virtual {p1}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-direct {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/b$b;-><init>(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C4(Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 72
    .line 73
    .line 74
    :cond_2
    :goto_0
    return-void
.end method

.method public final z4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$resetHighlightPrevWinSector$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$resetHighlightPrevWinSector$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$resetHighlightPrevWinSector$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$resetHighlightPrevWinSector$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
