.class public final Lq81/c$e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lq81/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq81/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\n\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0008\u0010\t\u001a\u0004\u0008\u0008\u0010\nR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\u000c\u001a\u0004\u0008\u000b\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lq81/c$e;",
        "Lq81/c;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "",
        "subcategoryId",
        "<init>",
        "(Lorg/xplatform/aggregator/api/model/Game;I)V",
        "a",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "()Lorg/xplatform/aggregator/api/model/Game;",
        "b",
        "I",
        "()I",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/api/model/Game;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lq81/c$e;->a:Lorg/xplatform/aggregator/api/model/Game;

    .line 5
    .line 6
    iput p2, p0, Lq81/c$e;->b:I

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Lorg/xplatform/aggregator/api/model/Game;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lq81/c$e;->a:Lorg/xplatform/aggregator/api/model/Game;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lq81/c$e;->b:I

    .line 2
    .line 3
    return v0
.end method
