.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingEndGameViewModel$showRestartOptions$2"
    f = "TileMatchingEndGameViewModel.kt"
    l = {
        0x8f
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->R3(LTv/a$j;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $command:LTv/a$j;

.field D$0:D

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field L$7:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/a$j;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
            "LTv/a$j;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->$command:LTv/a$j;

    .line 4
    .line 5
    const/4 p1, 0x2

    .line 6
    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->$command:LTv/a$j;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/a$j;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 26

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_2

    .line 11
    .line 12
    if-ne v2, v3, :cond_1

    .line 13
    .line 14
    iget-wide v4, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->D$0:D

    .line 15
    .line 16
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$7:Ljava/lang/Object;

    .line 17
    .line 18
    check-cast v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 19
    .line 20
    iget-object v6, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$6:Ljava/lang/Object;

    .line 21
    .line 22
    check-cast v6, Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 23
    .line 24
    iget-object v7, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$5:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v7, Ljava/lang/String;

    .line 27
    .line 28
    iget-object v8, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$4:Ljava/lang/Object;

    .line 29
    .line 30
    iget-object v9, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$3:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v9, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 33
    .line 34
    iget-object v10, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$2:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast v10, LTv/a$j;

    .line 37
    .line 38
    iget-object v11, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$1:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v11, Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    iget-object v12, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v12, LzT0/a;

    .line 45
    .line 46
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    move-object/from16 v13, p1

    .line 50
    .line 51
    :cond_0
    move-object v14, v2

    .line 52
    move-wide/from16 v17, v4

    .line 53
    .line 54
    move-object v15, v6

    .line 55
    move-object/from16 v16, v7

    .line 56
    .line 57
    goto/16 :goto_1

    .line 58
    .line 59
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 60
    .line 61
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 62
    .line 63
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    throw v1

    .line 67
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 71
    .line 72
    invoke-static {v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/d;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    invoke-virtual {v2}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 77
    .line 78
    .line 79
    move-result-wide v4

    .line 80
    const-wide/16 v6, 0x0

    .line 81
    .line 82
    cmpg-double v2, v4, v6

    .line 83
    .line 84
    if-nez v2, :cond_3

    .line 85
    .line 86
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 87
    .line 88
    invoke-static {v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->z3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/o;

    .line 89
    .line 90
    .line 91
    move-result-object v2

    .line 92
    iget-object v4, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 93
    .line 94
    invoke-static {v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->x3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/h;

    .line 95
    .line 96
    .line 97
    move-result-object v4

    .line 98
    invoke-virtual {v4}, Lorg/xbet/core/domain/usecases/bet/h;->a()D

    .line 99
    .line 100
    .line 101
    move-result-wide v4

    .line 102
    invoke-virtual {v2, v4, v5}, Lorg/xbet/core/domain/usecases/bet/o;->a(D)V

    .line 103
    .line 104
    .line 105
    :cond_3
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 106
    .line 107
    invoke-static {v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->y3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/c;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    invoke-virtual {v2}, Lorg/xbet/tile_matching/domain/usecases/c;->a()LzT0/e;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, LzT0/e;->f()LzT0/d;

    .line 116
    .line 117
    .line 118
    move-result-object v2

    .line 119
    invoke-virtual {v2}, LzT0/d;->b()Ljava/util/List;

    .line 120
    .line 121
    .line 122
    move-result-object v4

    .line 123
    invoke-interface {v4}, Ljava/util/Collection;->isEmpty()Z

    .line 124
    .line 125
    .line 126
    move-result v4

    .line 127
    if-nez v4, :cond_4

    .line 128
    .line 129
    invoke-virtual {v2}, LzT0/d;->b()Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    const/4 v4, 0x0

    .line 134
    invoke-interface {v2, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v2

    .line 138
    check-cast v2, LzT0/a;

    .line 139
    .line 140
    goto :goto_0

    .line 141
    :cond_4
    sget-object v2, LzT0/a;->c:LzT0/a$a;

    .line 142
    .line 143
    invoke-virtual {v2}, LzT0/a$a;->a()LzT0/a;

    .line 144
    .line 145
    .line 146
    move-result-object v2

    .line 147
    :goto_0
    iget-object v4, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 148
    .line 149
    invoke-static {v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->D3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lkotlinx/coroutines/flow/V;

    .line 150
    .line 151
    .line 152
    move-result-object v4

    .line 153
    iget-object v5, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->$command:LTv/a$j;

    .line 154
    .line 155
    iget-object v6, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 156
    .line 157
    move-object v12, v2

    .line 158
    move-object v11, v4

    .line 159
    move-object v10, v5

    .line 160
    move-object v9, v6

    .line 161
    :cond_5
    invoke-interface {v11}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v8

    .line 165
    move-object v2, v8

    .line 166
    check-cast v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 167
    .line 168
    invoke-virtual {v12}, LzT0/a;->a()Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 169
    .line 170
    .line 171
    move-result-object v6

    .line 172
    invoke-virtual {v12}, LzT0/a;->b()Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v7

    .line 176
    invoke-virtual {v10}, LTv/a$j;->g()D

    .line 177
    .line 178
    .line 179
    move-result-wide v4

    .line 180
    invoke-static {v9}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->w3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 181
    .line 182
    .line 183
    move-result-object v13

    .line 184
    iput-object v12, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$0:Ljava/lang/Object;

    .line 185
    .line 186
    iput-object v11, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$1:Ljava/lang/Object;

    .line 187
    .line 188
    iput-object v10, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$2:Ljava/lang/Object;

    .line 189
    .line 190
    iput-object v9, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$3:Ljava/lang/Object;

    .line 191
    .line 192
    iput-object v8, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$4:Ljava/lang/Object;

    .line 193
    .line 194
    iput-object v7, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$5:Ljava/lang/Object;

    .line 195
    .line 196
    iput-object v6, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$6:Ljava/lang/Object;

    .line 197
    .line 198
    iput-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->L$7:Ljava/lang/Object;

    .line 199
    .line 200
    iput-wide v4, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->D$0:D

    .line 201
    .line 202
    iput v3, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;->label:I

    .line 203
    .line 204
    invoke-virtual {v13, v0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 205
    .line 206
    .line 207
    move-result-object v13

    .line 208
    if-ne v13, v1, :cond_0

    .line 209
    .line 210
    return-object v1

    .line 211
    :goto_1
    move-object/from16 v19, v13

    .line 212
    .line 213
    check-cast v19, Ljava/lang/String;

    .line 214
    .line 215
    invoke-static {v9, v10}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/a$j;)Z

    .line 216
    .line 217
    .line 218
    move-result v20

    .line 219
    invoke-static {v9}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/d;

    .line 220
    .line 221
    .line 222
    move-result-object v2

    .line 223
    invoke-virtual {v2}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 224
    .line 225
    .line 226
    move-result-wide v21

    .line 227
    const/16 v24, 0x40

    .line 228
    .line 229
    const/16 v25, 0x0

    .line 230
    .line 231
    const/16 v23, 0x0

    .line 232
    .line 233
    invoke-static/range {v14 .. v25}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILjava/lang/Object;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 234
    .line 235
    .line 236
    move-result-object v2

    .line 237
    invoke-interface {v11, v8, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 238
    .line 239
    .line 240
    move-result v2

    .line 241
    if-eqz v2, :cond_5

    .line 242
    .line 243
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 244
    .line 245
    return-object v1
.end method
