.class public final synthetic LKY0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LKY0/g;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKY0/f;->a:LKY0/g;

    iput-object p2, p0, LKY0/f;->b:Lkotlin/jvm/functions/Function0;

    iput-object p3, p0, LKY0/f;->c:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, LKY0/f;->a:LKY0/g;

    iget-object v1, p0, LKY0/f;->b:Lkotlin/jvm/functions/Function0;

    iget-object v2, p0, LKY0/f;->c:Lkotlin/jvm/functions/Function0;

    invoke-static {v0, v1, v2}, LKY0/g;->h(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    return-void
.end method
