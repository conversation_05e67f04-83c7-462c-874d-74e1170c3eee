.class public final LDc1/c$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LDc1/j;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LDc1/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LDc1/c$b$b;,
        LDc1/c$b$c;,
        LDc1/c$b$a;
    }
.end annotation


# instance fields
.field public final a:LDc1/c$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAc1/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBc1/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/logout/impl/domain/usecases/LogoutUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lyc1/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjP/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lyg/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lk8/b;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lyg/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ly20/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqX0/b;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpR/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/logout/impl/presentation/LogoutDialogViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lm8/a;Ldk0/p;LiP/a;Lxc1/a;Lmo/f;Ljava/lang/Boolean;Ljava/lang/Boolean;Lyg/c;LpR/a;Ljava/lang/String;Lf8/g;Lorg/xbet/ui_common/utils/M;LqX0/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lk8/b;Lorg/xplatform/aggregator/api/domain/a;Lw30/e;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LHX0/e;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Leu/a;Ly20/a;Lcom/xbet/onexcore/domain/usecase/a;LQl0/a;LJT/d;LVT/g;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/data/profile/b;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LDc1/c$b;->a:LDc1/c$b;

    .line 4
    invoke-virtual/range {p0 .. p40}, LDc1/c$b;->b(Lm8/a;Ldk0/p;LiP/a;Lxc1/a;Lmo/f;Ljava/lang/Boolean;Ljava/lang/Boolean;Lyg/c;LpR/a;Ljava/lang/String;Lf8/g;Lorg/xbet/ui_common/utils/M;LqX0/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lk8/b;Lorg/xplatform/aggregator/api/domain/a;Lw30/e;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LHX0/e;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Leu/a;Ly20/a;Lcom/xbet/onexcore/domain/usecase/a;LQl0/a;LJT/d;LVT/g;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/data/profile/b;)V

    return-void
.end method

.method public synthetic constructor <init>(Lm8/a;Ldk0/p;LiP/a;Lxc1/a;Lmo/f;Ljava/lang/Boolean;Ljava/lang/Boolean;Lyg/c;LpR/a;Ljava/lang/String;Lf8/g;Lorg/xbet/ui_common/utils/M;LqX0/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lk8/b;Lorg/xplatform/aggregator/api/domain/a;Lw30/e;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LHX0/e;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Leu/a;Ly20/a;Lcom/xbet/onexcore/domain/usecase/a;LQl0/a;LJT/d;LVT/g;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/data/profile/b;LDc1/d;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p40}, LDc1/c$b;-><init>(Lm8/a;Ldk0/p;LiP/a;Lxc1/a;Lmo/f;Ljava/lang/Boolean;Ljava/lang/Boolean;Lyg/c;LpR/a;Ljava/lang/String;Lf8/g;Lorg/xbet/ui_common/utils/M;LqX0/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lk8/b;Lorg/xplatform/aggregator/api/domain/a;Lw30/e;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LHX0/e;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Leu/a;Ly20/a;Lcom/xbet/onexcore/domain/usecase/a;LQl0/a;LJT/d;LVT/g;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/data/profile/b;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/logout/impl/presentation/LogoutDialog;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LDc1/c$b;->c(Lorg/xplatform/logout/impl/presentation/LogoutDialog;)Lorg/xplatform/logout/impl/presentation/LogoutDialog;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(Lm8/a;Ldk0/p;LiP/a;Lxc1/a;Lmo/f;Ljava/lang/Boolean;Ljava/lang/Boolean;Lyg/c;LpR/a;Ljava/lang/String;Lf8/g;Lorg/xbet/ui_common/utils/M;LqX0/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lk8/b;Lorg/xplatform/aggregator/api/domain/a;Lw30/e;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LHX0/e;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Leu/a;Ly20/a;Lcom/xbet/onexcore/domain/usecase/a;LQl0/a;LJT/d;LVT/g;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/data/profile/b;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iput-object v1, v0, LDc1/c$b;->b:Ldagger/internal/h;

    .line 8
    .line 9
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iput-object v1, v0, LDc1/c$b;->c:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iput-object v1, v0, LDc1/c$b;->d:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    iput-object v1, v0, LDc1/c$b;->e:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static {v1}, LAc1/c;->a(LBc/a;)LAc1/c;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iput-object v1, v0, LDc1/c$b;->f:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {v1}, LBc1/b;->a(LBc/a;)LBc1/b;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iput-object v1, v0, LDc1/c$b;->g:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object v2, v0, LDc1/c$b;->d:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static {v2, v1}, Lorg/xplatform/logout/impl/domain/usecases/a;->a(LBc/a;LBc/a;)Lorg/xplatform/logout/impl/domain/usecases/a;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    iput-object v1, v0, LDc1/c$b;->h:Ldagger/internal/h;

    .line 46
    .line 47
    new-instance v1, LDc1/c$b$b;

    .line 48
    .line 49
    move-object/from16 v2, p2

    .line 50
    .line 51
    invoke-direct {v1, v2}, LDc1/c$b$b;-><init>(Ldk0/p;)V

    .line 52
    .line 53
    .line 54
    iput-object v1, v0, LDc1/c$b;->i:Ldagger/internal/h;

    .line 55
    .line 56
    new-instance v1, LDc1/c$b$c;

    .line 57
    .line 58
    move-object/from16 v2, p4

    .line 59
    .line 60
    invoke-direct {v1, v2}, LDc1/c$b$c;-><init>(Lxc1/a;)V

    .line 61
    .line 62
    .line 63
    iput-object v1, v0, LDc1/c$b;->j:Ldagger/internal/h;

    .line 64
    .line 65
    new-instance v1, LDc1/c$b$a;

    .line 66
    .line 67
    move-object/from16 v2, p3

    .line 68
    .line 69
    invoke-direct {v1, v2}, LDc1/c$b$a;-><init>(LiP/a;)V

    .line 70
    .line 71
    .line 72
    iput-object v1, v0, LDc1/c$b;->k:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    iput-object v1, v0, LDc1/c$b;->l:Ldagger/internal/h;

    .line 79
    .line 80
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    iput-object v1, v0, LDc1/c$b;->m:Ldagger/internal/h;

    .line 85
    .line 86
    invoke-static {v1}, Lyg/b;->a(LBc/a;)Lyg/b;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    iput-object v1, v0, LDc1/c$b;->n:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    iput-object v1, v0, LDc1/c$b;->o:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v1, v0, LDc1/c$b;->p:Ldagger/internal/h;

    .line 103
    .line 104
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    iput-object v1, v0, LDc1/c$b;->q:Ldagger/internal/h;

    .line 109
    .line 110
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    iput-object v1, v0, LDc1/c$b;->r:Ldagger/internal/h;

    .line 115
    .line 116
    invoke-static/range {p1 .. p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    iput-object v1, v0, LDc1/c$b;->s:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    iput-object v1, v0, LDc1/c$b;->t:Ldagger/internal/h;

    .line 127
    .line 128
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    iput-object v1, v0, LDc1/c$b;->u:Ldagger/internal/h;

    .line 133
    .line 134
    iget-object v2, v0, LDc1/c$b;->b:Ldagger/internal/h;

    .line 135
    .line 136
    iget-object v3, v0, LDc1/c$b;->c:Ldagger/internal/h;

    .line 137
    .line 138
    iget-object v4, v0, LDc1/c$b;->h:Ldagger/internal/h;

    .line 139
    .line 140
    iget-object v5, v0, LDc1/c$b;->i:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object v6, v0, LDc1/c$b;->j:Ldagger/internal/h;

    .line 143
    .line 144
    iget-object v7, v0, LDc1/c$b;->k:Ldagger/internal/h;

    .line 145
    .line 146
    iget-object v8, v0, LDc1/c$b;->l:Ldagger/internal/h;

    .line 147
    .line 148
    iget-object v9, v0, LDc1/c$b;->n:Ldagger/internal/h;

    .line 149
    .line 150
    iget-object v10, v0, LDc1/c$b;->o:Ldagger/internal/h;

    .line 151
    .line 152
    iget-object v11, v0, LDc1/c$b;->p:Ldagger/internal/h;

    .line 153
    .line 154
    iget-object v12, v0, LDc1/c$b;->q:Ldagger/internal/h;

    .line 155
    .line 156
    iget-object v13, v0, LDc1/c$b;->r:Ldagger/internal/h;

    .line 157
    .line 158
    iget-object v14, v0, LDc1/c$b;->s:Ldagger/internal/h;

    .line 159
    .line 160
    iget-object v15, v0, LDc1/c$b;->t:Ldagger/internal/h;

    .line 161
    .line 162
    move-object/from16 p15, v1

    .line 163
    .line 164
    move-object/from16 p1, v2

    .line 165
    .line 166
    move-object/from16 p2, v3

    .line 167
    .line 168
    move-object/from16 p3, v4

    .line 169
    .line 170
    move-object/from16 p4, v5

    .line 171
    .line 172
    move-object/from16 p5, v6

    .line 173
    .line 174
    move-object/from16 p6, v7

    .line 175
    .line 176
    move-object/from16 p7, v8

    .line 177
    .line 178
    move-object/from16 p8, v9

    .line 179
    .line 180
    move-object/from16 p9, v10

    .line 181
    .line 182
    move-object/from16 p10, v11

    .line 183
    .line 184
    move-object/from16 p11, v12

    .line 185
    .line 186
    move-object/from16 p12, v13

    .line 187
    .line 188
    move-object/from16 p13, v14

    .line 189
    .line 190
    move-object/from16 p14, v15

    .line 191
    .line 192
    invoke-static/range {p1 .. p15}, Lorg/xplatform/logout/impl/presentation/i;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/logout/impl/presentation/i;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    iput-object v1, v0, LDc1/c$b;->v:Ldagger/internal/h;

    .line 197
    .line 198
    return-void
.end method

.method public final c(Lorg/xplatform/logout/impl/presentation/LogoutDialog;)Lorg/xplatform/logout/impl/presentation/LogoutDialog;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LDc1/c$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xplatform/logout/impl/presentation/j;->a(Lorg/xplatform/logout/impl/presentation/LogoutDialog;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/logout/impl/presentation/LogoutDialogViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/c$b;->v:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LDc1/c$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
