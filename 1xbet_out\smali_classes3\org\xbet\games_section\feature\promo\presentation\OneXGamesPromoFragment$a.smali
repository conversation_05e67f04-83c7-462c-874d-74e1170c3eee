.class public final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "promoTypeId",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;",
        "a",
        "(I)Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;",
        "",
        "SELECT_BALANCE_REQUEST_KEY",
        "Ljava/lang/String;",
        "promo_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(I)Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->Companion:Lorg/xbet/games_section/api/models/OneXGamesPromoType$a;

    .line 4
    .line 5
    invoke-virtual {v1, p1}, Lorg/xbet/games_section/api/models/OneXGamesPromoType$a;->a(I)Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-direct {v0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;-><init>(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method
