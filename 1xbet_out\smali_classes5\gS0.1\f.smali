.class public final LgS0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LbS0/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LgS0/d;


# direct methods
.method public constructor <init>(LgS0/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LgS0/f;->a:LgS0/d;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LgS0/d;)LgS0/f;
    .locals 1

    .line 1
    new-instance v0, LgS0/f;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LgS0/f;-><init>(LgS0/d;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LgS0/d;)LbS0/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, LgS0/d;->b()LbS0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-static {p0}, Ldagger/internal/g;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    check-cast p0, LbS0/a;

    .line 10
    .line 11
    return-object p0
.end method


# virtual methods
.method public b()LbS0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LgS0/f;->a:LgS0/d;

    .line 2
    .line 3
    invoke-static {v0}, LgS0/f;->c(LgS0/d;)LbS0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LgS0/f;->b()LbS0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
