.class public final synthetic LHN0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LHN0/d$d;

.field public final synthetic b:LGN0/f;


# direct methods
.method public synthetic constructor <init>(LHN0/d$d;LGN0/f;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LHN0/e;->a:LHN0/d$d;

    iput-object p2, p0, LHN0/e;->b:LGN0/f;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LHN0/e;->a:LHN0/d$d;

    iget-object v1, p0, LHN0/e;->b:LGN0/f;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LHN0/d$d;->d(LHN0/d$d;LGN0/f;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
