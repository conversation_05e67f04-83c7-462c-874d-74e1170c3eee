.class public abstract Ll3/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lm3/a$b;
.implements Ll3/k;
.implements Ll3/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll3/a$b;
    }
.end annotation


# instance fields
.field public final a:Landroid/graphics/PathMeasure;

.field public final b:Landroid/graphics/Path;

.field public final c:Landroid/graphics/Path;

.field public final d:Landroid/graphics/RectF;

.field public final e:Lcom/airbnb/lottie/LottieDrawable;

.field public final f:Lcom/airbnb/lottie/model/layer/a;

.field public final g:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ll3/a$b;",
            ">;"
        }
    .end annotation
.end field

.field public final h:[F

.field public final i:Landroid/graphics/Paint;

.field public final j:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field public final k:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "*",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final l:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lm3/a<",
            "*",
            "Ljava/lang/Float;",
            ">;>;"
        }
    .end annotation
.end field

.field public final m:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field public n:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Landroid/graphics/ColorFilter;",
            "Landroid/graphics/ColorFilter;",
            ">;"
        }
    .end annotation
.end field

.field public o:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field public p:F

.field public q:Lm3/c;


# direct methods
.method public constructor <init>(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/model/layer/a;Landroid/graphics/Paint$Cap;Landroid/graphics/Paint$Join;FLp3/d;Lp3/b;Ljava/util/List;Lp3/b;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/airbnb/lottie/LottieDrawable;",
            "Lcom/airbnb/lottie/model/layer/a;",
            "Landroid/graphics/Paint$Cap;",
            "Landroid/graphics/Paint$Join;",
            "F",
            "Lp3/d;",
            "Lp3/b;",
            "Ljava/util/List<",
            "Lp3/b;",
            ">;",
            "Lp3/b;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Landroid/graphics/PathMeasure;

    .line 5
    .line 6
    invoke-direct {v0}, Landroid/graphics/PathMeasure;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 10
    .line 11
    new-instance v0, Landroid/graphics/Path;

    .line 12
    .line 13
    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 17
    .line 18
    new-instance v0, Landroid/graphics/Path;

    .line 19
    .line 20
    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Ll3/a;->c:Landroid/graphics/Path;

    .line 24
    .line 25
    new-instance v0, Landroid/graphics/RectF;

    .line 26
    .line 27
    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    .line 28
    .line 29
    .line 30
    iput-object v0, p0, Ll3/a;->d:Landroid/graphics/RectF;

    .line 31
    .line 32
    new-instance v0, Ljava/util/ArrayList;

    .line 33
    .line 34
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 35
    .line 36
    .line 37
    iput-object v0, p0, Ll3/a;->g:Ljava/util/List;

    .line 38
    .line 39
    new-instance v0, Lk3/a;

    .line 40
    .line 41
    const/4 v1, 0x1

    .line 42
    invoke-direct {v0, v1}, Lk3/a;-><init>(I)V

    .line 43
    .line 44
    .line 45
    iput-object v0, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 46
    .line 47
    const/4 v1, 0x0

    .line 48
    iput v1, p0, Ll3/a;->p:F

    .line 49
    .line 50
    iput-object p1, p0, Ll3/a;->e:Lcom/airbnb/lottie/LottieDrawable;

    .line 51
    .line 52
    iput-object p2, p0, Ll3/a;->f:Lcom/airbnb/lottie/model/layer/a;

    .line 53
    .line 54
    sget-object p1, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    .line 55
    .line 56
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v0, p3}, Landroid/graphics/Paint;->setStrokeCap(Landroid/graphics/Paint$Cap;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v0, p4}, Landroid/graphics/Paint;->setStrokeJoin(Landroid/graphics/Paint$Join;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {v0, p5}, Landroid/graphics/Paint;->setStrokeMiter(F)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {p6}, Lp3/d;->a()Lm3/a;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Ll3/a;->k:Lm3/a;

    .line 73
    .line 74
    invoke-virtual {p7}, Lp3/b;->c()Lm3/d;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iput-object p1, p0, Ll3/a;->j:Lm3/a;

    .line 79
    .line 80
    if-nez p9, :cond_0

    .line 81
    .line 82
    const/4 p1, 0x0

    .line 83
    iput-object p1, p0, Ll3/a;->m:Lm3/a;

    .line 84
    .line 85
    goto :goto_0

    .line 86
    :cond_0
    invoke-virtual {p9}, Lp3/b;->c()Lm3/d;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    iput-object p1, p0, Ll3/a;->m:Lm3/a;

    .line 91
    .line 92
    :goto_0
    new-instance p1, Ljava/util/ArrayList;

    .line 93
    .line 94
    invoke-interface {p8}, Ljava/util/List;->size()I

    .line 95
    .line 96
    .line 97
    move-result p3

    .line 98
    invoke-direct {p1, p3}, Ljava/util/ArrayList;-><init>(I)V

    .line 99
    .line 100
    .line 101
    iput-object p1, p0, Ll3/a;->l:Ljava/util/List;

    .line 102
    .line 103
    invoke-interface {p8}, Ljava/util/List;->size()I

    .line 104
    .line 105
    .line 106
    move-result p1

    .line 107
    new-array p1, p1, [F

    .line 108
    .line 109
    iput-object p1, p0, Ll3/a;->h:[F

    .line 110
    .line 111
    const/4 p1, 0x0

    .line 112
    const/4 p3, 0x0

    .line 113
    :goto_1
    invoke-interface {p8}, Ljava/util/List;->size()I

    .line 114
    .line 115
    .line 116
    move-result p4

    .line 117
    if-ge p3, p4, :cond_1

    .line 118
    .line 119
    iget-object p4, p0, Ll3/a;->l:Ljava/util/List;

    .line 120
    .line 121
    invoke-interface {p8, p3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 122
    .line 123
    .line 124
    move-result-object p5

    .line 125
    check-cast p5, Lp3/b;

    .line 126
    .line 127
    invoke-virtual {p5}, Lp3/b;->c()Lm3/d;

    .line 128
    .line 129
    .line 130
    move-result-object p5

    .line 131
    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 132
    .line 133
    .line 134
    add-int/lit8 p3, p3, 0x1

    .line 135
    .line 136
    goto :goto_1

    .line 137
    :cond_1
    iget-object p3, p0, Ll3/a;->k:Lm3/a;

    .line 138
    .line 139
    invoke-virtual {p2, p3}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 140
    .line 141
    .line 142
    iget-object p3, p0, Ll3/a;->j:Lm3/a;

    .line 143
    .line 144
    invoke-virtual {p2, p3}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 145
    .line 146
    .line 147
    const/4 p3, 0x0

    .line 148
    :goto_2
    iget-object p4, p0, Ll3/a;->l:Ljava/util/List;

    .line 149
    .line 150
    invoke-interface {p4}, Ljava/util/List;->size()I

    .line 151
    .line 152
    .line 153
    move-result p4

    .line 154
    if-ge p3, p4, :cond_2

    .line 155
    .line 156
    iget-object p4, p0, Ll3/a;->l:Ljava/util/List;

    .line 157
    .line 158
    invoke-interface {p4, p3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object p4

    .line 162
    check-cast p4, Lm3/a;

    .line 163
    .line 164
    invoke-virtual {p2, p4}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 165
    .line 166
    .line 167
    add-int/lit8 p3, p3, 0x1

    .line 168
    .line 169
    goto :goto_2

    .line 170
    :cond_2
    iget-object p3, p0, Ll3/a;->m:Lm3/a;

    .line 171
    .line 172
    if-eqz p3, :cond_3

    .line 173
    .line 174
    invoke-virtual {p2, p3}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 175
    .line 176
    .line 177
    :cond_3
    iget-object p3, p0, Ll3/a;->k:Lm3/a;

    .line 178
    .line 179
    invoke-virtual {p3, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 180
    .line 181
    .line 182
    iget-object p3, p0, Ll3/a;->j:Lm3/a;

    .line 183
    .line 184
    invoke-virtual {p3, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 185
    .line 186
    .line 187
    :goto_3
    invoke-interface {p8}, Ljava/util/List;->size()I

    .line 188
    .line 189
    .line 190
    move-result p3

    .line 191
    if-ge p1, p3, :cond_4

    .line 192
    .line 193
    iget-object p3, p0, Ll3/a;->l:Ljava/util/List;

    .line 194
    .line 195
    invoke-interface {p3, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object p3

    .line 199
    check-cast p3, Lm3/a;

    .line 200
    .line 201
    invoke-virtual {p3, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 202
    .line 203
    .line 204
    add-int/lit8 p1, p1, 0x1

    .line 205
    .line 206
    goto :goto_3

    .line 207
    :cond_4
    iget-object p1, p0, Ll3/a;->m:Lm3/a;

    .line 208
    .line 209
    if-eqz p1, :cond_5

    .line 210
    .line 211
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 212
    .line 213
    .line 214
    :cond_5
    invoke-virtual {p2}, Lcom/airbnb/lottie/model/layer/a;->x()Lq3/a;

    .line 215
    .line 216
    .line 217
    move-result-object p1

    .line 218
    if-eqz p1, :cond_6

    .line 219
    .line 220
    invoke-virtual {p2}, Lcom/airbnb/lottie/model/layer/a;->x()Lq3/a;

    .line 221
    .line 222
    .line 223
    move-result-object p1

    .line 224
    invoke-virtual {p1}, Lq3/a;->a()Lp3/b;

    .line 225
    .line 226
    .line 227
    move-result-object p1

    .line 228
    invoke-virtual {p1}, Lp3/b;->c()Lm3/d;

    .line 229
    .line 230
    .line 231
    move-result-object p1

    .line 232
    iput-object p1, p0, Ll3/a;->o:Lm3/a;

    .line 233
    .line 234
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 235
    .line 236
    .line 237
    iget-object p1, p0, Ll3/a;->o:Lm3/a;

    .line 238
    .line 239
    invoke-virtual {p2, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 240
    .line 241
    .line 242
    :cond_6
    invoke-virtual {p2}, Lcom/airbnb/lottie/model/layer/a;->z()Lt3/j;

    .line 243
    .line 244
    .line 245
    move-result-object p1

    .line 246
    if-eqz p1, :cond_7

    .line 247
    .line 248
    new-instance p1, Lm3/c;

    .line 249
    .line 250
    invoke-virtual {p2}, Lcom/airbnb/lottie/model/layer/a;->z()Lt3/j;

    .line 251
    .line 252
    .line 253
    move-result-object p3

    .line 254
    invoke-direct {p1, p0, p2, p3}, Lm3/c;-><init>(Lm3/a$b;Lcom/airbnb/lottie/model/layer/a;Lt3/j;)V

    .line 255
    .line 256
    .line 257
    iput-object p1, p0, Ll3/a;->q:Lm3/c;

    .line 258
    .line 259
    :cond_7
    return-void
.end method


# virtual methods
.method public a(Lo3/d;ILjava/util/List;Lo3/d;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lo3/d;",
            "I",
            "Ljava/util/List<",
            "Lo3/d;",
            ">;",
            "Lo3/d;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-static {p1, p2, p3, p4, p0}, Lu3/k;->k(Lo3/d;ILjava/util/List;Lo3/d;Ll3/k;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V
    .locals 6

    .line 1
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 2
    .line 3
    .line 4
    move-result p3

    .line 5
    const-string v0, "StrokeContent#getBounds"

    .line 6
    .line 7
    if-eqz p3, :cond_0

    .line 8
    .line 9
    invoke-static {v0}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    iget-object p3, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 13
    .line 14
    invoke-virtual {p3}, Landroid/graphics/Path;->reset()V

    .line 15
    .line 16
    .line 17
    const/4 p3, 0x0

    .line 18
    const/4 v1, 0x0

    .line 19
    :goto_0
    iget-object v2, p0, Ll3/a;->g:Ljava/util/List;

    .line 20
    .line 21
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    if-ge v1, v2, :cond_2

    .line 26
    .line 27
    iget-object v2, p0, Ll3/a;->g:Ljava/util/List;

    .line 28
    .line 29
    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    check-cast v2, Ll3/a$b;

    .line 34
    .line 35
    const/4 v3, 0x0

    .line 36
    :goto_1
    invoke-static {v2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v4

    .line 40
    invoke-interface {v4}, Ljava/util/List;->size()I

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    if-ge v3, v4, :cond_1

    .line 45
    .line 46
    iget-object v4, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 47
    .line 48
    invoke-static {v2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    invoke-interface {v5, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    check-cast v5, Ll3/m;

    .line 57
    .line 58
    invoke-interface {v5}, Ll3/m;->d()Landroid/graphics/Path;

    .line 59
    .line 60
    .line 61
    move-result-object v5

    .line 62
    invoke-virtual {v4, v5, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    .line 63
    .line 64
    .line 65
    add-int/lit8 v3, v3, 0x1

    .line 66
    .line 67
    goto :goto_1

    .line 68
    :cond_1
    add-int/lit8 v1, v1, 0x1

    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_2
    iget-object p2, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 72
    .line 73
    iget-object v1, p0, Ll3/a;->d:Landroid/graphics/RectF;

    .line 74
    .line 75
    invoke-virtual {p2, v1, p3}, Landroid/graphics/Path;->computeBounds(Landroid/graphics/RectF;Z)V

    .line 76
    .line 77
    .line 78
    iget-object p2, p0, Ll3/a;->j:Lm3/a;

    .line 79
    .line 80
    check-cast p2, Lm3/d;

    .line 81
    .line 82
    invoke-virtual {p2}, Lm3/d;->q()F

    .line 83
    .line 84
    .line 85
    move-result p2

    .line 86
    iget-object p3, p0, Ll3/a;->d:Landroid/graphics/RectF;

    .line 87
    .line 88
    iget v1, p3, Landroid/graphics/RectF;->left:F

    .line 89
    .line 90
    const/high16 v2, 0x40000000    # 2.0f

    .line 91
    .line 92
    div-float/2addr p2, v2

    .line 93
    sub-float/2addr v1, p2

    .line 94
    iget v2, p3, Landroid/graphics/RectF;->top:F

    .line 95
    .line 96
    sub-float/2addr v2, p2

    .line 97
    iget v3, p3, Landroid/graphics/RectF;->right:F

    .line 98
    .line 99
    add-float/2addr v3, p2

    .line 100
    iget v4, p3, Landroid/graphics/RectF;->bottom:F

    .line 101
    .line 102
    add-float/2addr v4, p2

    .line 103
    invoke-virtual {p3, v1, v2, v3, v4}, Landroid/graphics/RectF;->set(FFFF)V

    .line 104
    .line 105
    .line 106
    iget-object p2, p0, Ll3/a;->d:Landroid/graphics/RectF;

    .line 107
    .line 108
    invoke-virtual {p1, p2}, Landroid/graphics/RectF;->set(Landroid/graphics/RectF;)V

    .line 109
    .line 110
    .line 111
    iget p2, p1, Landroid/graphics/RectF;->left:F

    .line 112
    .line 113
    const/high16 p3, 0x3f800000    # 1.0f

    .line 114
    .line 115
    sub-float/2addr p2, p3

    .line 116
    iget v1, p1, Landroid/graphics/RectF;->top:F

    .line 117
    .line 118
    sub-float/2addr v1, p3

    .line 119
    iget v2, p1, Landroid/graphics/RectF;->right:F

    .line 120
    .line 121
    add-float/2addr v2, p3

    .line 122
    iget v3, p1, Landroid/graphics/RectF;->bottom:F

    .line 123
    .line 124
    add-float/2addr v3, p3

    .line 125
    invoke-virtual {p1, p2, v1, v2, v3}, Landroid/graphics/RectF;->set(FFFF)V

    .line 126
    .line 127
    .line 128
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 129
    .line 130
    .line 131
    move-result p1

    .line 132
    if-eqz p1, :cond_3

    .line 133
    .line 134
    invoke-static {v0}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 135
    .line 136
    .line 137
    :cond_3
    return-void
.end method

.method public final c(Landroid/graphics/Matrix;)V
    .locals 5

    .line 1
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const-string v1, "StrokeContent#applyDashPattern"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {v1}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    iget-object v0, p0, Ll3/a;->l:Ljava/util/List;

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    if-eqz p1, :cond_6

    .line 25
    .line 26
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 27
    .line 28
    .line 29
    return-void

    .line 30
    :cond_1
    invoke-static {p1}, Lu3/l;->g(Landroid/graphics/Matrix;)F

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    const/4 v0, 0x0

    .line 35
    :goto_0
    iget-object v2, p0, Ll3/a;->l:Ljava/util/List;

    .line 36
    .line 37
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    if-ge v0, v2, :cond_4

    .line 42
    .line 43
    iget-object v2, p0, Ll3/a;->h:[F

    .line 44
    .line 45
    iget-object v3, p0, Ll3/a;->l:Ljava/util/List;

    .line 46
    .line 47
    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    check-cast v3, Lm3/a;

    .line 52
    .line 53
    invoke-virtual {v3}, Lm3/a;->h()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    check-cast v3, Ljava/lang/Float;

    .line 58
    .line 59
    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    aput v3, v2, v0

    .line 64
    .line 65
    rem-int/lit8 v2, v0, 0x2

    .line 66
    .line 67
    if-nez v2, :cond_2

    .line 68
    .line 69
    iget-object v2, p0, Ll3/a;->h:[F

    .line 70
    .line 71
    aget v3, v2, v0

    .line 72
    .line 73
    const/high16 v4, 0x3f800000    # 1.0f

    .line 74
    .line 75
    cmpg-float v3, v3, v4

    .line 76
    .line 77
    if-gez v3, :cond_3

    .line 78
    .line 79
    aput v4, v2, v0

    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_2
    iget-object v2, p0, Ll3/a;->h:[F

    .line 83
    .line 84
    aget v3, v2, v0

    .line 85
    .line 86
    const v4, 0x3dcccccd

    .line 87
    .line 88
    .line 89
    cmpg-float v3, v3, v4

    .line 90
    .line 91
    if-gez v3, :cond_3

    .line 92
    .line 93
    aput v4, v2, v0

    .line 94
    .line 95
    :cond_3
    :goto_1
    iget-object v2, p0, Ll3/a;->h:[F

    .line 96
    .line 97
    aget v3, v2, v0

    .line 98
    .line 99
    mul-float v3, v3, p1

    .line 100
    .line 101
    aput v3, v2, v0

    .line 102
    .line 103
    add-int/lit8 v0, v0, 0x1

    .line 104
    .line 105
    goto :goto_0

    .line 106
    :cond_4
    iget-object v0, p0, Ll3/a;->m:Lm3/a;

    .line 107
    .line 108
    if-nez v0, :cond_5

    .line 109
    .line 110
    const/4 p1, 0x0

    .line 111
    goto :goto_2

    .line 112
    :cond_5
    invoke-virtual {v0}, Lm3/a;->h()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    check-cast v0, Ljava/lang/Float;

    .line 117
    .line 118
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 119
    .line 120
    .line 121
    move-result v0

    .line 122
    mul-float p1, p1, v0

    .line 123
    .line 124
    :goto_2
    iget-object v0, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 125
    .line 126
    new-instance v2, Landroid/graphics/DashPathEffect;

    .line 127
    .line 128
    iget-object v3, p0, Ll3/a;->h:[F

    .line 129
    .line 130
    invoke-direct {v2, v3, p1}, Landroid/graphics/DashPathEffect;-><init>([FF)V

    .line 131
    .line 132
    .line 133
    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setPathEffect(Landroid/graphics/PathEffect;)Landroid/graphics/PathEffect;

    .line 134
    .line 135
    .line 136
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 137
    .line 138
    .line 139
    move-result p1

    .line 140
    if-eqz p1, :cond_6

    .line 141
    .line 142
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 143
    .line 144
    .line 145
    :cond_6
    return-void
.end method

.method public e(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 6

    .line 1
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const-string v1, "StrokeContent#draw"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {v1}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    invoke-static {p2}, Lu3/l;->h(Landroid/graphics/Matrix;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    if-eqz p1, :cond_e

    .line 23
    .line 24
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_1
    int-to-float v0, p3

    .line 29
    const/high16 v2, 0x437f0000    # 255.0f

    .line 30
    .line 31
    div-float/2addr v0, v2

    .line 32
    iget-object v3, p0, Ll3/a;->k:Lm3/a;

    .line 33
    .line 34
    check-cast v3, Lm3/f;

    .line 35
    .line 36
    invoke-virtual {v3}, Lm3/f;->q()I

    .line 37
    .line 38
    .line 39
    move-result v3

    .line 40
    int-to-float v3, v3

    .line 41
    mul-float v0, v0, v3

    .line 42
    .line 43
    const/high16 v3, 0x42c80000    # 100.0f

    .line 44
    .line 45
    div-float/2addr v0, v3

    .line 46
    mul-float v0, v0, v2

    .line 47
    .line 48
    float-to-int v0, v0

    .line 49
    iget-object v2, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 50
    .line 51
    const/16 v3, 0xff

    .line 52
    .line 53
    const/4 v4, 0x0

    .line 54
    invoke-static {v0, v4, v3}, Lu3/k;->c(III)I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 59
    .line 60
    .line 61
    iget-object v2, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 62
    .line 63
    iget-object v3, p0, Ll3/a;->j:Lm3/a;

    .line 64
    .line 65
    check-cast v3, Lm3/d;

    .line 66
    .line 67
    invoke-virtual {v3}, Lm3/d;->q()F

    .line 68
    .line 69
    .line 70
    move-result v3

    .line 71
    invoke-static {p2}, Lu3/l;->g(Landroid/graphics/Matrix;)F

    .line 72
    .line 73
    .line 74
    move-result v5

    .line 75
    mul-float v3, v3, v5

    .line 76
    .line 77
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 78
    .line 79
    .line 80
    iget-object v2, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 81
    .line 82
    invoke-virtual {v2}, Landroid/graphics/Paint;->getStrokeWidth()F

    .line 83
    .line 84
    .line 85
    move-result v2

    .line 86
    const/4 v3, 0x0

    .line 87
    cmpg-float v2, v2, v3

    .line 88
    .line 89
    if-gtz v2, :cond_2

    .line 90
    .line 91
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 92
    .line 93
    .line 94
    move-result p1

    .line 95
    if-eqz p1, :cond_e

    .line 96
    .line 97
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 98
    .line 99
    .line 100
    return-void

    .line 101
    :cond_2
    invoke-virtual {p0, p2}, Ll3/a;->c(Landroid/graphics/Matrix;)V

    .line 102
    .line 103
    .line 104
    iget-object v2, p0, Ll3/a;->n:Lm3/a;

    .line 105
    .line 106
    if-eqz v2, :cond_3

    .line 107
    .line 108
    iget-object v5, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 109
    .line 110
    invoke-virtual {v2}, Lm3/a;->h()Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    check-cast v2, Landroid/graphics/ColorFilter;

    .line 115
    .line 116
    invoke-virtual {v5, v2}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    .line 117
    .line 118
    .line 119
    :cond_3
    iget-object v2, p0, Ll3/a;->o:Lm3/a;

    .line 120
    .line 121
    if-eqz v2, :cond_6

    .line 122
    .line 123
    invoke-virtual {v2}, Lm3/a;->h()Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v2

    .line 127
    check-cast v2, Ljava/lang/Float;

    .line 128
    .line 129
    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    .line 130
    .line 131
    .line 132
    move-result v2

    .line 133
    cmpl-float v3, v2, v3

    .line 134
    .line 135
    if-nez v3, :cond_4

    .line 136
    .line 137
    iget-object v3, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 138
    .line 139
    const/4 v5, 0x0

    .line 140
    invoke-virtual {v3, v5}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    .line 141
    .line 142
    .line 143
    goto :goto_0

    .line 144
    :cond_4
    iget v3, p0, Ll3/a;->p:F

    .line 145
    .line 146
    cmpl-float v3, v2, v3

    .line 147
    .line 148
    if-eqz v3, :cond_5

    .line 149
    .line 150
    iget-object v3, p0, Ll3/a;->f:Lcom/airbnb/lottie/model/layer/a;

    .line 151
    .line 152
    invoke-virtual {v3, v2}, Lcom/airbnb/lottie/model/layer/a;->y(F)Landroid/graphics/BlurMaskFilter;

    .line 153
    .line 154
    .line 155
    move-result-object v3

    .line 156
    iget-object v5, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 157
    .line 158
    invoke-virtual {v5, v3}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    .line 159
    .line 160
    .line 161
    :cond_5
    :goto_0
    iput v2, p0, Ll3/a;->p:F

    .line 162
    .line 163
    :cond_6
    iget-object v2, p0, Ll3/a;->q:Lm3/c;

    .line 164
    .line 165
    if-eqz v2, :cond_7

    .line 166
    .line 167
    iget-object v3, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 168
    .line 169
    invoke-static {p3, v0}, Lu3/l;->l(II)I

    .line 170
    .line 171
    .line 172
    move-result p3

    .line 173
    invoke-virtual {v2, v3, p2, p3}, Lm3/c;->a(Landroid/graphics/Paint;Landroid/graphics/Matrix;I)V

    .line 174
    .line 175
    .line 176
    :cond_7
    :goto_1
    iget-object p3, p0, Ll3/a;->g:Ljava/util/List;

    .line 177
    .line 178
    invoke-interface {p3}, Ljava/util/List;->size()I

    .line 179
    .line 180
    .line 181
    move-result p3

    .line 182
    if-ge v4, p3, :cond_d

    .line 183
    .line 184
    iget-object p3, p0, Ll3/a;->g:Ljava/util/List;

    .line 185
    .line 186
    invoke-interface {p3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 187
    .line 188
    .line 189
    move-result-object p3

    .line 190
    check-cast p3, Ll3/a$b;

    .line 191
    .line 192
    invoke-static {p3}, Ll3/a$b;->b(Ll3/a$b;)Ll3/u;

    .line 193
    .line 194
    .line 195
    move-result-object v0

    .line 196
    if-eqz v0, :cond_8

    .line 197
    .line 198
    invoke-virtual {p0, p1, p3, p2}, Ll3/a;->j(Landroid/graphics/Canvas;Ll3/a$b;Landroid/graphics/Matrix;)V

    .line 199
    .line 200
    .line 201
    goto :goto_3

    .line 202
    :cond_8
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 203
    .line 204
    .line 205
    move-result v0

    .line 206
    const-string v2, "StrokeContent#buildPath"

    .line 207
    .line 208
    if-eqz v0, :cond_9

    .line 209
    .line 210
    invoke-static {v2}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 211
    .line 212
    .line 213
    :cond_9
    iget-object v0, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 214
    .line 215
    invoke-virtual {v0}, Landroid/graphics/Path;->reset()V

    .line 216
    .line 217
    .line 218
    invoke-static {p3}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 219
    .line 220
    .line 221
    move-result-object v0

    .line 222
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 223
    .line 224
    .line 225
    move-result v0

    .line 226
    add-int/lit8 v0, v0, -0x1

    .line 227
    .line 228
    :goto_2
    if-ltz v0, :cond_a

    .line 229
    .line 230
    iget-object v3, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 231
    .line 232
    invoke-static {p3}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 233
    .line 234
    .line 235
    move-result-object v5

    .line 236
    invoke-interface {v5, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 237
    .line 238
    .line 239
    move-result-object v5

    .line 240
    check-cast v5, Ll3/m;

    .line 241
    .line 242
    invoke-interface {v5}, Ll3/m;->d()Landroid/graphics/Path;

    .line 243
    .line 244
    .line 245
    move-result-object v5

    .line 246
    invoke-virtual {v3, v5, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    .line 247
    .line 248
    .line 249
    add-int/lit8 v0, v0, -0x1

    .line 250
    .line 251
    goto :goto_2

    .line 252
    :cond_a
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 253
    .line 254
    .line 255
    move-result p3

    .line 256
    const-string v0, "StrokeContent#drawPath"

    .line 257
    .line 258
    if-eqz p3, :cond_b

    .line 259
    .line 260
    invoke-static {v2}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 261
    .line 262
    .line 263
    invoke-static {v0}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 264
    .line 265
    .line 266
    :cond_b
    iget-object p3, p0, Ll3/a;->b:Landroid/graphics/Path;

    .line 267
    .line 268
    iget-object v2, p0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 269
    .line 270
    invoke-virtual {p1, p3, v2}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 271
    .line 272
    .line 273
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 274
    .line 275
    .line 276
    move-result p3

    .line 277
    if-eqz p3, :cond_c

    .line 278
    .line 279
    invoke-static {v0}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 280
    .line 281
    .line 282
    :cond_c
    :goto_3
    add-int/lit8 v4, v4, 0x1

    .line 283
    .line 284
    goto :goto_1

    .line 285
    :cond_d
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 286
    .line 287
    .line 288
    move-result p1

    .line 289
    if-eqz p1, :cond_e

    .line 290
    .line 291
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 292
    .line 293
    .line 294
    :cond_e
    return-void
.end method

.method public f()V
    .locals 1

    .line 1
    iget-object v0, p0, Ll3/a;->e:Lcom/airbnb/lottie/LottieDrawable;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/airbnb/lottie/LottieDrawable;->invalidateSelf()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public g(Ljava/util/List;Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ll3/c;",
            ">;",
            "Ljava/util/List<",
            "Ll3/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    add-int/lit8 v0, v0, -0x1

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    move-object v2, v1

    .line 9
    :goto_0
    if-ltz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    check-cast v3, Ll3/c;

    .line 16
    .line 17
    instance-of v4, v3, Ll3/u;

    .line 18
    .line 19
    if-eqz v4, :cond_0

    .line 20
    .line 21
    check-cast v3, Ll3/u;

    .line 22
    .line 23
    invoke-virtual {v3}, Ll3/u;->k()Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    sget-object v5, Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;->INDIVIDUALLY:Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;

    .line 28
    .line 29
    if-ne v4, v5, :cond_0

    .line 30
    .line 31
    move-object v2, v3

    .line 32
    :cond_0
    add-int/lit8 v0, v0, -0x1

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    if-eqz v2, :cond_2

    .line 36
    .line 37
    invoke-virtual {v2, p0}, Ll3/u;->a(Lm3/a$b;)V

    .line 38
    .line 39
    .line 40
    :cond_2
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 41
    .line 42
    .line 43
    move-result p1

    .line 44
    add-int/lit8 p1, p1, -0x1

    .line 45
    .line 46
    move-object v0, v1

    .line 47
    :goto_1
    if-ltz p1, :cond_7

    .line 48
    .line 49
    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v3

    .line 53
    check-cast v3, Ll3/c;

    .line 54
    .line 55
    instance-of v4, v3, Ll3/u;

    .line 56
    .line 57
    if-eqz v4, :cond_4

    .line 58
    .line 59
    move-object v4, v3

    .line 60
    check-cast v4, Ll3/u;

    .line 61
    .line 62
    invoke-virtual {v4}, Ll3/u;->k()Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;

    .line 63
    .line 64
    .line 65
    move-result-object v5

    .line 66
    sget-object v6, Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;->INDIVIDUALLY:Lcom/airbnb/lottie/model/content/ShapeTrimPath$Type;

    .line 67
    .line 68
    if-ne v5, v6, :cond_4

    .line 69
    .line 70
    if-eqz v0, :cond_3

    .line 71
    .line 72
    iget-object v3, p0, Ll3/a;->g:Ljava/util/List;

    .line 73
    .line 74
    invoke-interface {v3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 75
    .line 76
    .line 77
    :cond_3
    new-instance v0, Ll3/a$b;

    .line 78
    .line 79
    invoke-direct {v0, v4, v1}, Ll3/a$b;-><init>(Ll3/u;Ll3/a$a;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v4, p0}, Ll3/u;->a(Lm3/a$b;)V

    .line 83
    .line 84
    .line 85
    goto :goto_2

    .line 86
    :cond_4
    instance-of v4, v3, Ll3/m;

    .line 87
    .line 88
    if-eqz v4, :cond_6

    .line 89
    .line 90
    if-nez v0, :cond_5

    .line 91
    .line 92
    new-instance v0, Ll3/a$b;

    .line 93
    .line 94
    invoke-direct {v0, v2, v1}, Ll3/a$b;-><init>(Ll3/u;Ll3/a$a;)V

    .line 95
    .line 96
    .line 97
    :cond_5
    invoke-static {v0}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object v4

    .line 101
    check-cast v3, Ll3/m;

    .line 102
    .line 103
    invoke-interface {v4, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 104
    .line 105
    .line 106
    :cond_6
    :goto_2
    add-int/lit8 p1, p1, -0x1

    .line 107
    .line 108
    goto :goto_1

    .line 109
    :cond_7
    if-eqz v0, :cond_8

    .line 110
    .line 111
    iget-object p1, p0, Ll3/a;->g:Ljava/util/List;

    .line 112
    .line 113
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 114
    .line 115
    .line 116
    :cond_8
    return-void
.end method

.method public h(Ljava/lang/Object;Lv3/c;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;",
            "Lv3/c<",
            "TT;>;)V"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/airbnb/lottie/S;->d:Ljava/lang/Integer;

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Ll3/a;->k:Lm3/a;

    .line 6
    .line 7
    invoke-virtual {p1, p2}, Lm3/a;->o(Lv3/c;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    sget-object v0, Lcom/airbnb/lottie/S;->s:Ljava/lang/Float;

    .line 12
    .line 13
    if-ne p1, v0, :cond_1

    .line 14
    .line 15
    iget-object p1, p0, Ll3/a;->j:Lm3/a;

    .line 16
    .line 17
    invoke-virtual {p1, p2}, Lm3/a;->o(Lv3/c;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_1
    sget-object v0, Lcom/airbnb/lottie/S;->K:Landroid/graphics/ColorFilter;

    .line 22
    .line 23
    if-ne p1, v0, :cond_4

    .line 24
    .line 25
    iget-object p1, p0, Ll3/a;->n:Lm3/a;

    .line 26
    .line 27
    if-eqz p1, :cond_2

    .line 28
    .line 29
    iget-object v0, p0, Ll3/a;->f:Lcom/airbnb/lottie/model/layer/a;

    .line 30
    .line 31
    invoke-virtual {v0, p1}, Lcom/airbnb/lottie/model/layer/a;->H(Lm3/a;)V

    .line 32
    .line 33
    .line 34
    :cond_2
    if-nez p2, :cond_3

    .line 35
    .line 36
    const/4 p1, 0x0

    .line 37
    iput-object p1, p0, Ll3/a;->n:Lm3/a;

    .line 38
    .line 39
    return-void

    .line 40
    :cond_3
    new-instance p1, Lm3/q;

    .line 41
    .line 42
    invoke-direct {p1, p2}, Lm3/q;-><init>(Lv3/c;)V

    .line 43
    .line 44
    .line 45
    iput-object p1, p0, Ll3/a;->n:Lm3/a;

    .line 46
    .line 47
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 48
    .line 49
    .line 50
    iget-object p1, p0, Ll3/a;->f:Lcom/airbnb/lottie/model/layer/a;

    .line 51
    .line 52
    iget-object p2, p0, Ll3/a;->n:Lm3/a;

    .line 53
    .line 54
    invoke-virtual {p1, p2}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 55
    .line 56
    .line 57
    return-void

    .line 58
    :cond_4
    sget-object v0, Lcom/airbnb/lottie/S;->j:Ljava/lang/Float;

    .line 59
    .line 60
    if-ne p1, v0, :cond_6

    .line 61
    .line 62
    iget-object p1, p0, Ll3/a;->o:Lm3/a;

    .line 63
    .line 64
    if-eqz p1, :cond_5

    .line 65
    .line 66
    invoke-virtual {p1, p2}, Lm3/a;->o(Lv3/c;)V

    .line 67
    .line 68
    .line 69
    return-void

    .line 70
    :cond_5
    new-instance p1, Lm3/q;

    .line 71
    .line 72
    invoke-direct {p1, p2}, Lm3/q;-><init>(Lv3/c;)V

    .line 73
    .line 74
    .line 75
    iput-object p1, p0, Ll3/a;->o:Lm3/a;

    .line 76
    .line 77
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 78
    .line 79
    .line 80
    iget-object p1, p0, Ll3/a;->f:Lcom/airbnb/lottie/model/layer/a;

    .line 81
    .line 82
    iget-object p2, p0, Ll3/a;->o:Lm3/a;

    .line 83
    .line 84
    invoke-virtual {p1, p2}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 85
    .line 86
    .line 87
    return-void

    .line 88
    :cond_6
    sget-object v0, Lcom/airbnb/lottie/S;->e:Ljava/lang/Integer;

    .line 89
    .line 90
    if-ne p1, v0, :cond_7

    .line 91
    .line 92
    iget-object v0, p0, Ll3/a;->q:Lm3/c;

    .line 93
    .line 94
    if-eqz v0, :cond_7

    .line 95
    .line 96
    invoke-virtual {v0, p2}, Lm3/c;->b(Lv3/c;)V

    .line 97
    .line 98
    .line 99
    return-void

    .line 100
    :cond_7
    sget-object v0, Lcom/airbnb/lottie/S;->G:Ljava/lang/Float;

    .line 101
    .line 102
    if-ne p1, v0, :cond_8

    .line 103
    .line 104
    iget-object v0, p0, Ll3/a;->q:Lm3/c;

    .line 105
    .line 106
    if-eqz v0, :cond_8

    .line 107
    .line 108
    invoke-virtual {v0, p2}, Lm3/c;->e(Lv3/c;)V

    .line 109
    .line 110
    .line 111
    return-void

    .line 112
    :cond_8
    sget-object v0, Lcom/airbnb/lottie/S;->H:Ljava/lang/Float;

    .line 113
    .line 114
    if-ne p1, v0, :cond_9

    .line 115
    .line 116
    iget-object v0, p0, Ll3/a;->q:Lm3/c;

    .line 117
    .line 118
    if-eqz v0, :cond_9

    .line 119
    .line 120
    invoke-virtual {v0, p2}, Lm3/c;->c(Lv3/c;)V

    .line 121
    .line 122
    .line 123
    return-void

    .line 124
    :cond_9
    sget-object v0, Lcom/airbnb/lottie/S;->I:Ljava/lang/Float;

    .line 125
    .line 126
    if-ne p1, v0, :cond_a

    .line 127
    .line 128
    iget-object v0, p0, Ll3/a;->q:Lm3/c;

    .line 129
    .line 130
    if-eqz v0, :cond_a

    .line 131
    .line 132
    invoke-virtual {v0, p2}, Lm3/c;->d(Lv3/c;)V

    .line 133
    .line 134
    .line 135
    return-void

    .line 136
    :cond_a
    sget-object v0, Lcom/airbnb/lottie/S;->J:Ljava/lang/Float;

    .line 137
    .line 138
    if-ne p1, v0, :cond_b

    .line 139
    .line 140
    iget-object p1, p0, Ll3/a;->q:Lm3/c;

    .line 141
    .line 142
    if-eqz p1, :cond_b

    .line 143
    .line 144
    invoke-virtual {p1, p2}, Lm3/c;->g(Lv3/c;)V

    .line 145
    .line 146
    .line 147
    :cond_b
    return-void
.end method

.method public final j(Landroid/graphics/Canvas;Ll3/a$b;Landroid/graphics/Matrix;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p3

    .line 6
    .line 7
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 8
    .line 9
    .line 10
    move-result v3

    .line 11
    const-string v4, "StrokeContent#applyTrimPath"

    .line 12
    .line 13
    if-eqz v3, :cond_0

    .line 14
    .line 15
    invoke-static {v4}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    invoke-static/range {p2 .. p2}, Ll3/a$b;->b(Ll3/a$b;)Ll3/u;

    .line 19
    .line 20
    .line 21
    move-result-object v3

    .line 22
    if-nez v3, :cond_1

    .line 23
    .line 24
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-eqz v1, :cond_d

    .line 29
    .line 30
    invoke-static {v4}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 31
    .line 32
    .line 33
    return-void

    .line 34
    :cond_1
    iget-object v3, v0, Ll3/a;->b:Landroid/graphics/Path;

    .line 35
    .line 36
    invoke-virtual {v3}, Landroid/graphics/Path;->reset()V

    .line 37
    .line 38
    .line 39
    invoke-static/range {p2 .. p2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 44
    .line 45
    .line 46
    move-result v3

    .line 47
    add-int/lit8 v3, v3, -0x1

    .line 48
    .line 49
    :goto_0
    if-ltz v3, :cond_2

    .line 50
    .line 51
    iget-object v5, v0, Ll3/a;->b:Landroid/graphics/Path;

    .line 52
    .line 53
    invoke-static/range {p2 .. p2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    invoke-interface {v6, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v6

    .line 61
    check-cast v6, Ll3/m;

    .line 62
    .line 63
    invoke-interface {v6}, Ll3/m;->d()Landroid/graphics/Path;

    .line 64
    .line 65
    .line 66
    move-result-object v6

    .line 67
    invoke-virtual {v5, v6, v2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    .line 68
    .line 69
    .line 70
    add-int/lit8 v3, v3, -0x1

    .line 71
    .line 72
    goto :goto_0

    .line 73
    :cond_2
    invoke-static/range {p2 .. p2}, Ll3/a$b;->b(Ll3/a$b;)Ll3/u;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    invoke-virtual {v3}, Ll3/u;->j()Lm3/a;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    invoke-virtual {v3}, Lm3/a;->h()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    check-cast v3, Ljava/lang/Float;

    .line 86
    .line 87
    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    .line 88
    .line 89
    .line 90
    move-result v3

    .line 91
    const/high16 v5, 0x42c80000    # 100.0f

    .line 92
    .line 93
    div-float/2addr v3, v5

    .line 94
    invoke-static/range {p2 .. p2}, Ll3/a$b;->b(Ll3/a$b;)Ll3/u;

    .line 95
    .line 96
    .line 97
    move-result-object v6

    .line 98
    invoke-virtual {v6}, Ll3/u;->c()Lm3/a;

    .line 99
    .line 100
    .line 101
    move-result-object v6

    .line 102
    invoke-virtual {v6}, Lm3/a;->h()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v6

    .line 106
    check-cast v6, Ljava/lang/Float;

    .line 107
    .line 108
    invoke-virtual {v6}, Ljava/lang/Float;->floatValue()F

    .line 109
    .line 110
    .line 111
    move-result v6

    .line 112
    div-float/2addr v6, v5

    .line 113
    invoke-static/range {p2 .. p2}, Ll3/a$b;->b(Ll3/a$b;)Ll3/u;

    .line 114
    .line 115
    .line 116
    move-result-object v5

    .line 117
    invoke-virtual {v5}, Ll3/u;->h()Lm3/a;

    .line 118
    .line 119
    .line 120
    move-result-object v5

    .line 121
    invoke-virtual {v5}, Lm3/a;->h()Ljava/lang/Object;

    .line 122
    .line 123
    .line 124
    move-result-object v5

    .line 125
    check-cast v5, Ljava/lang/Float;

    .line 126
    .line 127
    invoke-virtual {v5}, Ljava/lang/Float;->floatValue()F

    .line 128
    .line 129
    .line 130
    move-result v5

    .line 131
    const/high16 v7, 0x43b40000    # 360.0f

    .line 132
    .line 133
    div-float/2addr v5, v7

    .line 134
    const v7, 0x3c23d70a

    .line 135
    .line 136
    .line 137
    cmpg-float v7, v3, v7

    .line 138
    .line 139
    if-gez v7, :cond_3

    .line 140
    .line 141
    const v7, 0x3f7d70a4

    .line 142
    .line 143
    .line 144
    cmpl-float v7, v6, v7

    .line 145
    .line 146
    if-lez v7, :cond_3

    .line 147
    .line 148
    iget-object v2, v0, Ll3/a;->b:Landroid/graphics/Path;

    .line 149
    .line 150
    iget-object v3, v0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 151
    .line 152
    invoke-virtual {v1, v2, v3}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 153
    .line 154
    .line 155
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 156
    .line 157
    .line 158
    move-result v1

    .line 159
    if-eqz v1, :cond_d

    .line 160
    .line 161
    invoke-static {v4}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 162
    .line 163
    .line 164
    return-void

    .line 165
    :cond_3
    iget-object v7, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 166
    .line 167
    iget-object v8, v0, Ll3/a;->b:Landroid/graphics/Path;

    .line 168
    .line 169
    const/4 v9, 0x0

    .line 170
    invoke-virtual {v7, v8, v9}, Landroid/graphics/PathMeasure;->setPath(Landroid/graphics/Path;Z)V

    .line 171
    .line 172
    .line 173
    iget-object v7, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 174
    .line 175
    invoke-virtual {v7}, Landroid/graphics/PathMeasure;->getLength()F

    .line 176
    .line 177
    .line 178
    move-result v7

    .line 179
    :goto_1
    iget-object v8, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 180
    .line 181
    invoke-virtual {v8}, Landroid/graphics/PathMeasure;->nextContour()Z

    .line 182
    .line 183
    .line 184
    move-result v8

    .line 185
    if-eqz v8, :cond_4

    .line 186
    .line 187
    iget-object v8, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 188
    .line 189
    invoke-virtual {v8}, Landroid/graphics/PathMeasure;->getLength()F

    .line 190
    .line 191
    .line 192
    move-result v8

    .line 193
    add-float/2addr v7, v8

    .line 194
    goto :goto_1

    .line 195
    :cond_4
    mul-float v5, v5, v7

    .line 196
    .line 197
    mul-float v3, v3, v7

    .line 198
    .line 199
    add-float/2addr v3, v5

    .line 200
    mul-float v6, v6, v7

    .line 201
    .line 202
    add-float/2addr v6, v5

    .line 203
    add-float v5, v3, v7

    .line 204
    .line 205
    const/high16 v8, 0x3f800000    # 1.0f

    .line 206
    .line 207
    sub-float/2addr v5, v8

    .line 208
    invoke-static {v6, v5}, Ljava/lang/Math;->min(FF)F

    .line 209
    .line 210
    .line 211
    move-result v5

    .line 212
    invoke-static/range {p2 .. p2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 213
    .line 214
    .line 215
    move-result-object v6

    .line 216
    invoke-interface {v6}, Ljava/util/List;->size()I

    .line 217
    .line 218
    .line 219
    move-result v6

    .line 220
    add-int/lit8 v6, v6, -0x1

    .line 221
    .line 222
    const/4 v10, 0x0

    .line 223
    const/4 v11, 0x0

    .line 224
    :goto_2
    if-ltz v6, :cond_c

    .line 225
    .line 226
    iget-object v12, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 227
    .line 228
    invoke-static/range {p2 .. p2}, Ll3/a$b;->a(Ll3/a$b;)Ljava/util/List;

    .line 229
    .line 230
    .line 231
    move-result-object v13

    .line 232
    invoke-interface {v13, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v13

    .line 236
    check-cast v13, Ll3/m;

    .line 237
    .line 238
    invoke-interface {v13}, Ll3/m;->d()Landroid/graphics/Path;

    .line 239
    .line 240
    .line 241
    move-result-object v13

    .line 242
    invoke-virtual {v12, v13}, Landroid/graphics/Path;->set(Landroid/graphics/Path;)V

    .line 243
    .line 244
    .line 245
    iget-object v12, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 246
    .line 247
    invoke-virtual {v12, v2}, Landroid/graphics/Path;->transform(Landroid/graphics/Matrix;)V

    .line 248
    .line 249
    .line 250
    iget-object v12, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 251
    .line 252
    iget-object v13, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 253
    .line 254
    invoke-virtual {v12, v13, v9}, Landroid/graphics/PathMeasure;->setPath(Landroid/graphics/Path;Z)V

    .line 255
    .line 256
    .line 257
    iget-object v12, v0, Ll3/a;->a:Landroid/graphics/PathMeasure;

    .line 258
    .line 259
    invoke-virtual {v12}, Landroid/graphics/PathMeasure;->getLength()F

    .line 260
    .line 261
    .line 262
    move-result v12

    .line 263
    cmpl-float v13, v5, v7

    .line 264
    .line 265
    if-lez v13, :cond_6

    .line 266
    .line 267
    sub-float v13, v5, v7

    .line 268
    .line 269
    add-float v14, v11, v12

    .line 270
    .line 271
    cmpg-float v14, v13, v14

    .line 272
    .line 273
    if-gez v14, :cond_6

    .line 274
    .line 275
    cmpg-float v14, v11, v13

    .line 276
    .line 277
    if-gez v14, :cond_6

    .line 278
    .line 279
    cmpl-float v14, v3, v7

    .line 280
    .line 281
    if-lez v14, :cond_5

    .line 282
    .line 283
    sub-float v14, v3, v7

    .line 284
    .line 285
    div-float/2addr v14, v12

    .line 286
    goto :goto_3

    .line 287
    :cond_5
    const/4 v14, 0x0

    .line 288
    :goto_3
    div-float/2addr v13, v12

    .line 289
    invoke-static {v13, v8}, Ljava/lang/Math;->min(FF)F

    .line 290
    .line 291
    .line 292
    move-result v13

    .line 293
    iget-object v15, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 294
    .line 295
    invoke-static {v15, v14, v13, v10}, Lu3/l;->a(Landroid/graphics/Path;FFF)V

    .line 296
    .line 297
    .line 298
    iget-object v13, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 299
    .line 300
    iget-object v14, v0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 301
    .line 302
    invoke-virtual {v1, v13, v14}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 303
    .line 304
    .line 305
    goto :goto_6

    .line 306
    :cond_6
    add-float v13, v11, v12

    .line 307
    .line 308
    cmpg-float v14, v13, v3

    .line 309
    .line 310
    if-ltz v14, :cond_b

    .line 311
    .line 312
    cmpl-float v14, v11, v5

    .line 313
    .line 314
    if-lez v14, :cond_7

    .line 315
    .line 316
    goto :goto_6

    .line 317
    :cond_7
    cmpg-float v14, v13, v5

    .line 318
    .line 319
    if-gtz v14, :cond_8

    .line 320
    .line 321
    cmpg-float v14, v3, v11

    .line 322
    .line 323
    if-gez v14, :cond_8

    .line 324
    .line 325
    iget-object v13, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 326
    .line 327
    iget-object v14, v0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 328
    .line 329
    invoke-virtual {v1, v13, v14}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 330
    .line 331
    .line 332
    goto :goto_6

    .line 333
    :cond_8
    cmpg-float v14, v3, v11

    .line 334
    .line 335
    if-gez v14, :cond_9

    .line 336
    .line 337
    const/4 v14, 0x0

    .line 338
    goto :goto_4

    .line 339
    :cond_9
    sub-float v14, v3, v11

    .line 340
    .line 341
    div-float/2addr v14, v12

    .line 342
    :goto_4
    cmpl-float v13, v5, v13

    .line 343
    .line 344
    if-lez v13, :cond_a

    .line 345
    .line 346
    const/high16 v13, 0x3f800000    # 1.0f

    .line 347
    .line 348
    goto :goto_5

    .line 349
    :cond_a
    sub-float v13, v5, v11

    .line 350
    .line 351
    div-float/2addr v13, v12

    .line 352
    :goto_5
    iget-object v15, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 353
    .line 354
    invoke-static {v15, v14, v13, v10}, Lu3/l;->a(Landroid/graphics/Path;FFF)V

    .line 355
    .line 356
    .line 357
    iget-object v13, v0, Ll3/a;->c:Landroid/graphics/Path;

    .line 358
    .line 359
    iget-object v14, v0, Ll3/a;->i:Landroid/graphics/Paint;

    .line 360
    .line 361
    invoke-virtual {v1, v13, v14}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 362
    .line 363
    .line 364
    :cond_b
    :goto_6
    add-float/2addr v11, v12

    .line 365
    add-int/lit8 v6, v6, -0x1

    .line 366
    .line 367
    goto/16 :goto_2

    .line 368
    .line 369
    :cond_c
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 370
    .line 371
    .line 372
    move-result v1

    .line 373
    if-eqz v1, :cond_d

    .line 374
    .line 375
    invoke-static {v4}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 376
    .line 377
    .line 378
    :cond_d
    return-void
.end method
