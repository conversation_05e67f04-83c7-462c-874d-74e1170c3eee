.class public final Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001:\u00010B3\u0012\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004\u0012\u0016\u0008\u0002\u0010\u0008\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000c\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\r\u0010\u000e\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\"\u0010\u0008\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0016\u0010\u0017\u001a\u00020\u00148\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R\u001a\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u001d\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u001c8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 R\u001a\u0010$\u001a\u0008\u0012\u0004\u0012\u00020\"0\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010\u001aR\u001d\u0010\'\u001a\u0008\u0012\u0004\u0012\u00020\"0\u001c8\u0006\u00a2\u0006\u000c\n\u0004\u0008%\u0010\u001e\u001a\u0004\u0008&\u0010 R\u0014\u0010+\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u001c\u0010/\u001a\u0008\u0012\u0004\u0012\u00020\u00070,8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008-\u0010.\u00a8\u00061"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
        "",
        "",
        "countDownInterval",
        "",
        "runAtStart",
        "Lkotlin/Function1;",
        "",
        "onTick",
        "<init>",
        "(JZLkotlin/jvm/functions/Function1;)V",
        "initialValue",
        "l",
        "(J)V",
        "n",
        "()V",
        "a",
        "J",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "Lkotlinx/coroutines/x0;",
        "c",
        "Lkotlinx/coroutines/x0;",
        "timerJob",
        "Lkotlinx/coroutines/flow/V;",
        "d",
        "Lkotlinx/coroutines/flow/V;",
        "tickFlow",
        "Lkotlinx/coroutines/flow/f0;",
        "e",
        "Lkotlinx/coroutines/flow/f0;",
        "j",
        "()Lkotlinx/coroutines/flow/f0;",
        "tickStateFlow",
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;",
        "f",
        "playerModeFlow",
        "g",
        "getPlayerMode",
        "playerMode",
        "Lkotlinx/coroutines/N;",
        "h",
        "Lkotlinx/coroutines/N;",
        "scope",
        "Lkotlin/Function0;",
        "i",
        "Lkotlin/jvm/functions/Function0;",
        "onFinish",
        "PlayerMode",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:J

.field public final b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public c:Lkotlinx/coroutines/x0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 7

    .line 1
    const/4 v5, 0x7

    const/4 v6, 0x0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(JZLkotlin/jvm/functions/Function1;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZ",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->a:J

    .line 4
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->b:Lkotlin/jvm/functions/Function1;

    const/4 p1, 0x0

    const/4 p2, 0x1

    .line 5
    invoke-static {p1, p2, p1}, Lkotlinx/coroutines/z0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c:Lkotlinx/coroutines/x0;

    const-wide/16 v0, 0x0

    .line 6
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p4

    invoke-static {p4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 7
    invoke-static {p4}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->e:Lkotlinx/coroutines/flow/f0;

    .line 8
    sget-object p4, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;->STOPPED:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;

    invoke-static {p4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->f:Lkotlinx/coroutines/flow/V;

    .line 9
    invoke-static {p4}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->g:Lkotlinx/coroutines/flow/f0;

    .line 10
    invoke-static {}, Lkotlinx/coroutines/b0;->b()Lkotlinx/coroutines/J;

    move-result-object p4

    invoke-static {p4}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    move-result-object p4

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->h:Lkotlinx/coroutines/N;

    .line 11
    new-instance p4, Lorg/xplatform/aggregator/impl/gifts/timer/b;

    invoke-direct {p4}, Lorg/xplatform/aggregator/impl/gifts/timer/b;-><init>()V

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->i:Lkotlin/jvm/functions/Function0;

    if-eqz p3, :cond_0

    .line 12
    invoke-static {p0, v0, v1, p2, p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->m(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;JILjava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public synthetic constructor <init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p6, p5, 0x1

    if-eqz p6, :cond_0

    const-wide/16 p1, 0x3e8

    :cond_0
    and-int/lit8 p6, p5, 0x2

    if-eqz p6, :cond_1

    const/4 p3, 0x0

    :cond_1
    and-int/lit8 p5, p5, 0x4

    if-eqz p5, :cond_2

    .line 13
    new-instance p4, Lorg/xplatform/aggregator/impl/gifts/timer/a;

    invoke-direct {p4}, Lorg/xplatform/aggregator/impl/gifts/timer/a;-><init>()V

    .line 14
    :cond_2
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public static synthetic a()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->k()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic b(J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c(J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(J)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlin/jvm/functions/Function0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->i:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlin/jvm/functions/Function1;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->b:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->f:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final k()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static synthetic m(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;JILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const-wide/16 p1, 0x0

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->l(J)V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final j()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->e:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l(J)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 11
    .line 12
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    check-cast p1, Ljava/lang/Number;

    .line 17
    .line 18
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 19
    .line 20
    .line 21
    move-result-wide p1

    .line 22
    const-wide/16 v0, 0x0

    .line 23
    .line 24
    cmp-long v2, p1, v0

    .line 25
    .line 26
    if-nez v2, :cond_0

    .line 27
    .line 28
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    const-wide v0, 0x7fffffffffffffffL

    .line 31
    .line 32
    .line 33
    .line 34
    .line 35
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 36
    .line 37
    .line 38
    move-result-object p2

    .line 39
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c:Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    const/4 p2, 0x1

    .line 45
    const/4 v0, 0x0

    .line 46
    invoke-static {p1, v0, p2, v0}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->h:Lkotlinx/coroutines/N;

    .line 50
    .line 51
    invoke-static {}, Lkotlinx/coroutines/b0;->b()Lkotlinx/coroutines/J;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;

    .line 56
    .line 57
    invoke-direct {v4, p0, v0}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;Lkotlin/coroutines/e;)V

    .line 58
    .line 59
    .line 60
    const/4 v5, 0x2

    .line 61
    const/4 v6, 0x0

    .line 62
    const/4 v3, 0x0

    .line 63
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c:Lkotlinx/coroutines/x0;

    .line 68
    .line 69
    return-void
.end method

.method public final n()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->c:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    const-wide/16 v1, 0x0

    .line 11
    .line 12
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->f:Lkotlinx/coroutines/flow/V;

    .line 20
    .line 21
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;->STOPPED:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;

    .line 22
    .line 23
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method
