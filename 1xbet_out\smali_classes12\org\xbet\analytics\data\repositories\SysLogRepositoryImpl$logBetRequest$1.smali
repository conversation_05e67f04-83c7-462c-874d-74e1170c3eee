.class final Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.analytics.data.repositories.SysLogRepositoryImpl$logBetRequest$1"
    f = "SysLogRepositoryImpl.kt"
    l = {
        0xa4
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->k(Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0006\n\u0000\n\u0002\u0010\u0002\u0010\u0000\u001a\u00020\u0001H\n"
    }
    d2 = {
        "<anonymous>",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $betGuid:Ljava/lang/String;

.field final synthetic $generated:Ljava/lang/String;

.field final synthetic $isQuickBet:Z

.field final synthetic $vid:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;",
            "Ljava/lang/String;",
            "Z",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$generated:Ljava/lang/String;

    iput-boolean p3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$isQuickBet:Z

    iput-object p4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$betGuid:Ljava/lang/String;

    iput-object p5, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$vid:Ljava/lang/String;

    const/4 p1, 0x1

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;

    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$generated:Ljava/lang/String;

    iget-boolean v3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$isQuickBet:Z

    iget-object v4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$betGuid:Ljava/lang/String;

    iget-object v5, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$vid:Ljava/lang/String;

    move-object v6, p1

    invoke-direct/range {v0 .. v6}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    new-instance p1, Lcom/google/gson/JsonObject;

    .line 28
    .line 29
    invoke-direct {p1}, Lcom/google/gson/JsonObject;-><init>()V

    .line 30
    .line 31
    .line 32
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$generated:Ljava/lang/String;

    .line 33
    .line 34
    iget-boolean v3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$isQuickBet:Z

    .line 35
    .line 36
    iget-object v4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$betGuid:Ljava/lang/String;

    .line 37
    .line 38
    iget-object v5, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->$vid:Ljava/lang/String;

    .line 39
    .line 40
    const-string v6, "betUniqueToken"

    .line 41
    .line 42
    invoke-virtual {p1, v6, v1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    invoke-static {v3}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    const-string v3, "betQuickBet"

    .line 50
    .line 51
    invoke-virtual {p1, v3, v1}, Lcom/google/gson/JsonObject;->C(Ljava/lang/String;Ljava/lang/Boolean;)V

    .line 52
    .line 53
    .line 54
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 55
    .line 56
    .line 57
    move-result-wide v6

    .line 58
    invoke-static {v6, v7}, LHc/a;->f(J)Ljava/lang/Long;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    const-string v3, "betClickTime"

    .line 63
    .line 64
    invoke-virtual {p1, v3, v1}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 65
    .line 66
    .line 67
    const-string v1, "betGUID"

    .line 68
    .line 69
    invoke-virtual {p1, v1, v4}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 70
    .line 71
    .line 72
    const-string v1, "betVid"

    .line 73
    .line 74
    invoke-virtual {p1, v1, v5}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 78
    .line 79
    const-string v3, "logBetRequest"

    .line 80
    .line 81
    invoke-static {v1, v3}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->t(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)Lcom/google/gson/JsonObject;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    iput v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;->label:I

    .line 86
    .line 87
    const-string v2, "BetEvRequest"

    .line 88
    .line 89
    invoke-static {v1, v3, v2, p1, p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->v(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lcom/google/gson/JsonObject;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    if-ne p1, v0, :cond_2

    .line 94
    .line 95
    return-object v0

    .line 96
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 97
    .line 98
    return-object p1
.end method
