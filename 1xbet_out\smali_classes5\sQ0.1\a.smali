.class public final LsQ0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LGN0/a;",
        "LtQ0/a;",
        "a",
        "(LGN0/a;)LtQ0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LGN0/a;)LtQ0/a;
    .locals 3
    .param p0    # LGN0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LtQ0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LGN0/a;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, LtQ0/f;->d:LtQ0/f$c;

    .line 8
    .line 9
    invoke-virtual {p0}, LGN0/a;->c()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-virtual {v2, p0}, LtQ0/f$c;->a(I)LtQ0/f;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-direct {v0, v1, p0}, LtQ0/a;-><init>(Ljava/lang/String;LtQ0/f;)V

    .line 18
    .line 19
    .line 20
    return-object v0
.end method
