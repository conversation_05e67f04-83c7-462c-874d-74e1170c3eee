.class public final enum Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\n\n\u0002\u0010\u000b\n\u0000\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u000b\u001a\u00020\u000cj\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\n\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "X2",
        "X4",
        "X5",
        "X7",
        "X10",
        "X20",
        "EMPTY",
        "isEmpty",
        "",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X10:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X2:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X20:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X4:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X5:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

.field public static final enum X7:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    const-string v1, "X2"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X2:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 12
    .line 13
    const-string v1, "X4"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X4:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 22
    .line 23
    const-string v1, "X5"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X5:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 32
    .line 33
    const-string v1, "X7"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X7:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 42
    .line 43
    const-string v1, "X10"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X10:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 52
    .line 53
    const-string v1, "X20"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X20:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 62
    .line 63
    const-string v1, "EMPTY"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 70
    .line 71
    invoke-static {}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->a()[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->$VALUES:[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 76
    .line 77
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    sput-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->$ENTRIES:Lkotlin/enums/a;

    .line 82
    .line 83
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .locals 3

    .line 1
    const/4 v0, 0x7

    new-array v0, v0, [Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X2:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X4:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X5:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X7:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X10:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->X20:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->$VALUES:[Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final isEmpty()Z
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    return v0

    .line 7
    :cond_0
    const/4 v0, 0x0

    .line 8
    return v0
.end method
