.class public final Lorg/xbet/ui_common/moxy/activities/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/ui_common/moxy/activities/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/ui_common/moxy/activities/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lorg/xbet/ui_common/moxy/activities/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/onexlocalization/k;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/onexlocalization/b;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/moxy/activities/k;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/onexlocalization/k;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, Lorg/xbet/ui_common/moxy/activities/a$b;->a:Lorg/xbet/ui_common/moxy/activities/a$b;

    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/moxy/activities/a$b;->b(Lorg/xbet/onexlocalization/k;)V

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/onexlocalization/k;Lorg/xbet/ui_common/moxy/activities/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/ui_common/moxy/activities/a$b;-><init>(Lorg/xbet/onexlocalization/k;)V

    return-void
.end method


# virtual methods
.method public a()Landroidx/lifecycle/e0$c;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/a$b;->d()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final b(Lorg/xbet/onexlocalization/k;)V
    .locals 0

    .line 1
    invoke-static {p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p1}, Lorg/xbet/onexlocalization/c;->a(LBc/a;)Lorg/xbet/onexlocalization/c;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/ui_common/moxy/activities/l;->a(LBc/a;)Lorg/xbet/ui_common/moxy/activities/l;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    return-void
.end method

.method public final c()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/ui_common/moxy/activities/k;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/ui_common/moxy/activities/a$b;->d:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/google/common/collect/ImmutableMap;->of(Ljava/lang/Object;Ljava/lang/Object;)Lcom/google/common/collect/ImmutableMap;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final d()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/a$b;->c()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
