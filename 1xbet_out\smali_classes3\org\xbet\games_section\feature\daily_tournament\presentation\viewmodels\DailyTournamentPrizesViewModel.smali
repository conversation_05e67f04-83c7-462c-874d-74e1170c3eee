.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001:\u00011B3\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0015\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014H\u0000\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0010J\u0013\u0010\u0019\u001a\u00020\u000e*\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$R\u001a\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\u00150%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0018\u0010,\u001a\u0004\u0018\u00010)8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0016\u00100\u001a\u00020-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010/\u00a8\u00062"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
        "getTournamentPrizesFlowUseCase",
        "LwX0/c;",
        "router",
        "Lm8/a;",
        "dispatchers",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;LwX0/c;Lm8/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V",
        "",
        "z3",
        "()V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "x3",
        "()Lorg/xbet/uikit/components/lottie/a;",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;",
        "y3",
        "()Lkotlinx/coroutines/flow/e;",
        "A3",
        "C3",
        "(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V",
        "v1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
        "x1",
        "LwX0/c;",
        "y1",
        "Lm8/a;",
        "F1",
        "LSX0/a;",
        "H1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lkotlinx/coroutines/flow/V;",
        "I1",
        "Lkotlinx/coroutines/flow/V;",
        "state",
        "Lkotlinx/coroutines/x0;",
        "P1",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "",
        "S1",
        "Z",
        "contentLoaded",
        "a",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public P1:Lkotlinx/coroutines/x0;

.field public S1:Z

.field public final v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;LwX0/c;Lm8/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->x1:LwX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->y1:Lm8/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->F1:LSX0/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 13
    .line 14
    new-instance p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$a;

    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->x3()Lorg/xbet/uikit/components/lottie/a;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    invoke-direct {p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$a;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 21
    .line 22
    .line 23
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 28
    .line 29
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->z3()V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->A3()V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public static final B3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private static final D3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->D3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->B3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->S1:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic s3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)Lorg/xbet/uikit/components/lottie/a;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->x3()Lorg/xbet/uikit/components/lottie/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->C3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->S1:Z

    .line 2
    .line 3
    return-void
.end method

.method private final x3()Lorg/xbet/uikit/components/lottie/a;
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->F1:LSX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v7, 0x1c

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    return-object v0
.end method

.method private final z3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->P1:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeConnection$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->P1:Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    return-void
.end method


# virtual methods
.method public final A3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/d;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/d;-><init>()V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->y1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final C3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->y1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/c;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/c;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$send$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$send$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final y3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method
